package com.echronos.expo.api.feign;

import com.echronos.commons.Result;
import com.echronos.expo.api.req.IExpoEmailNmsReq;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * <AUTHOR>
 * @Date 2025/5/21 17:48
 * @ClassName IExpoAudienceFeign
 */
@FeignClient(value = "ech-expo", path = "/api/expo/audience")
public interface IExpoAudienceFeignClient {
    /**
     * 观众邮件推送通知
     *
     * @param req
     * @return
     */
    @PostMapping("notify/email")
    Result notifyEmailSend(@RequestBody IExpoEmailNmsReq req);
}
