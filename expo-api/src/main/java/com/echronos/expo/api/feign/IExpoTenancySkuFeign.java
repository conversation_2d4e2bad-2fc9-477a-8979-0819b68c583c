package com.echronos.expo.api.feign;

import com.echronos.commons.Result;
import com.echronos.expo.api.req.ExpoTenancySkuReq;
import com.echronos.expo.api.resp.ExpoTenancySkuResp;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * <AUTHOR>
 * date2025/8/15 10:20
 */
@FeignClient(value = "ech-expo", path = "api/expo/tenancy/sku", fallbackFactory = IExpoTenancySkuFeign.FallBack.class)
public interface IExpoTenancySkuFeign {

    /**
     * 根据展商ID查询租赁商品信息
     *
     * @param req
     * @return
     */
    @GetMapping(value = "/by/expoId")
    Result<List<ExpoTenancySkuResp>> queryTenancyByExpoId(@RequestBody ExpoTenancySkuReq req);

    class FallBack implements IExpoTenancySkuFeign {

        @Override
        public Result<List<ExpoTenancySkuResp>> queryTenancyByExpoId(ExpoTenancySkuReq req) {
            return null;
        }
    }
}
