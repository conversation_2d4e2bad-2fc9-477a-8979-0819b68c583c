package com.echronos.expo.controller;

import com.echronos.commons.Result;
import com.echronos.commons.exception.BusinessException;
import com.echronos.commons.utils.CopyObjectUtils;
import com.echronos.expo.dto.ExpoReferenceFormDTO;
import com.echronos.expo.param.IdParam;
import com.echronos.expo.service.IExpoReferenceFormService;
import com.echronos.expo.vo.ExpoReferenceFormVO;
import com.echronos.expo.vo.ReferenceFormAuditParam;
import com.echronos.expo.vo.referenform.AddOrEditReferenceFormParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * 展商或观众引用表单控制器
 * <AUTHOR>
 * @date 2025-08-09 10:28
 */
@Slf4j
@RestController
@RequestMapping("v1/expo/reference/form")
public class ExpoReferenceFormController {

    @Resource
    private IExpoReferenceFormService expoReferenceFormService;

    /**
     * 根据id查询表单信息
     *
     * @param param
     * @return
     */
    @GetMapping(value = "get/id")
    public Result<ExpoReferenceFormVO> getById(IdParam param) {
        ExpoReferenceFormDTO dto = new ExpoReferenceFormDTO();
        dto.setId(param.getId());
        ExpoReferenceFormVO vo = expoReferenceFormService.getDetailById(dto);
        return Result.build(vo);
    }

    /**
     * 新增表单
     * @return
     */
    @PostMapping(value = "add")
    public Result addForm(@RequestBody AddOrEditReferenceFormParam param) {
        ExpoReferenceFormDTO dto = CopyObjectUtils.copyAtoB(param, ExpoReferenceFormDTO.class);
        expoReferenceFormService.addOrEdit(dto);
        return Result.build();
    }

    /**
     * 编辑表单
     * @return
     */
    @PostMapping(value = "edit")
    public Result editForm(@RequestBody AddOrEditReferenceFormParam param){
        ExpoReferenceFormDTO dto = CopyObjectUtils.copyAtoB(param, ExpoReferenceFormDTO.class);
        if(null == param.getId()){
            throw new BusinessException(-1, "id不能为空");
        }
        expoReferenceFormService.addOrEdit(dto);
        return Result.build();
    }

    /**
     * 审核表单
     * @return
     */
    @PostMapping(value = "audit")
    public Result audit(@RequestBody ReferenceFormAuditParam param){
        ExpoReferenceFormDTO dto = CopyObjectUtils.copyAtoB(param, ExpoReferenceFormDTO.class);
        expoReferenceFormService.audit(dto);
        return Result.build();
    }
}
