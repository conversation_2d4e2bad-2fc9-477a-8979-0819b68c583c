package com.echronos.expo.controller;

import com.echronos.commons.Result;
import com.echronos.commons.utils.CopyObjectUtils;
import com.echronos.commons.utils.RequestUserUtils;
import com.echronos.expo.dto.AudienceCountDTO;
import com.echronos.expo.dto.ExpoInfoDTO;
import com.echronos.expo.dto.FormFieldDTO;
import com.echronos.expo.dto.QuestionAnswerStatisticsDTO;
import com.echronos.expo.param.AudienceCountParam;
import com.echronos.expo.param.ExpoStatisticsParam;
import com.echronos.expo.param.QuestionAnswerStatisticsParam;
import com.echronos.expo.param.QuestionFieldParam;
import com.echronos.expo.service.IExpoAudienceService;
import com.echronos.expo.service.IExpoService;
import com.echronos.expo.util.FormFieldsUtil;
import com.echronos.expo.vo.*;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.ZoneOffset;
import java.util.ArrayList;
import java.util.List;
import java.util.TimeZone;

/**
 * 展会及观众统计
 *
 * <AUTHOR>
 * @Date 2025/6/28 11:13
 * @ClassName ExpoStatisticsController
 */
@RestController
@RequestMapping("v1/expo/statistics")
public class ExpoStatisticsController {

    @Resource
    private IExpoAudienceService expoAudienceService;

    @Resource
    private IExpoService iExpoService;


    /**
     * 展会列表
     *
     * @return
     */
    @GetMapping("expo/list")
    public Result<List<ExpoInfoVO>> list(ExpoStatisticsParam param) {
        ExpoInfoDTO dto = new ExpoInfoDTO();
        dto.setCompanyId(RequestUserUtils.getUser().getCompanyId());
        List<ExpoInfoDTO> dtos = iExpoService.listDTO(dto);
        List<ExpoInfoVO> vos = CopyObjectUtils.copyAlistToBlist(dtos, ExpoInfoVO.class);
        TimeZone timeZone = TimeZone.getTimeZone(param.getZoneId());
        vos.stream().forEach(r -> {
            List<LocalDate> durationList = new ArrayList<>();
            LocalDate getStartTime = r.getStartTime().toInstant(ZoneOffset.UTC).atZone(timeZone.toZoneId()).toLocalDate();
            LocalDate getEndTime = r.getEndTime().toInstant(ZoneOffset.UTC).atZone(timeZone.toZoneId()).toLocalDate();
            for (LocalDate date = getStartTime; !date.isAfter(getEndTime); date = date.plusDays(1)) {
                durationList.add(date);
            }
            r.setDurationList(durationList);
        });
        return Result.build(vos);
    }
    /**
     * 观众报名/到场概览
     *
     * @param param
     * @return
     */
    @GetMapping("overviewStatistics")
    Result<AudienceOverviewStatisticsVO> overviewStatistics(AudienceCountParam param) {
        AudienceCountDTO dto = CopyObjectUtils.copyAtoB(param, AudienceCountDTO.class);
        dto.setCompanyId(RequestUserUtils.getUser().getCompanyId());
        AudienceOverviewStatisticsVO vo = expoAudienceService.overviewStatistics(dto);
        return Result.build(vo);
    }
    /**
     * 观众报名/到场分析
     *
     * @param param
     * @return
     */
    @GetMapping("signAndAttendance")
    Result<AudienceStatisticsVO> statisticsAudience(AudienceCountParam param) {
        AudienceCountDTO dto = CopyObjectUtils.copyAtoB(param, AudienceCountDTO.class);
        dto.setCompanyId(RequestUserUtils.getUser().getCompanyId());
        AudienceStatisticsVO vo = expoAudienceService.statisticsAudienceByDate(dto);
        return Result.build(vo);
    }


    /**
     * 展会下表单问题字段
     *
     * @param param
     * @return
     */
    @GetMapping("questionFieldList")
    Result<List<FormFieldStatisticsVO>> questionFieldList(QuestionFieldParam param) {
        List<FormFieldDTO> fieldDTOS = FormFieldsUtil.getAudienceField();
        List<FormFieldStatisticsVO> vos = CopyObjectUtils.copyAlistToBlist(fieldDTOS, FormFieldStatisticsVO.class);
        return Result.build(vos);
    }

    /**
     * 问答结果统计
     *
     * @param param
     * @return
     */
    @PostMapping("questionAnswerList")
    Result<List<QuestionStatisticsVO>> questionAnswerList(@RequestBody QuestionAnswerStatisticsParam param) {
        QuestionAnswerStatisticsDTO dto = CopyObjectUtils.copyAtoB(param, QuestionAnswerStatisticsDTO.class);
        dto.setCompanyId(RequestUserUtils.getUser().getCompanyId());
        List<QuestionStatisticsVO> vos = expoAudienceService.questionAnswerList(dto);
        return Result.build(vos);
    }

}
