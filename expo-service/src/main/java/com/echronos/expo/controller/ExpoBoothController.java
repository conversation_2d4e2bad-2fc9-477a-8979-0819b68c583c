package com.echronos.expo.controller;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.echronos.commons.Result;
import com.echronos.commons.model.RequestUser;
import com.echronos.commons.utils.CopyObjectUtils;
import com.echronos.commons.utils.RequestUserUtils;
import com.echronos.expo.dto.ExpoAttachmentFileDTO;
import com.echronos.expo.dto.ExpoBoothDTO;
import com.echronos.expo.dto.ExpoBoothOrderDTO;
import com.echronos.expo.dto.ExpoBoothPageDTO;
import com.echronos.expo.easyexcel.constant.DropDownFieldConstant;
import com.echronos.expo.easyexcel.model.ExportBoothExcelModel;
import com.echronos.expo.easyexcel.model.ImportBoothExcelModel;
import com.echronos.expo.enums.ExpoAttachmentFileEnum;
import com.echronos.expo.enums.ExpoBoothEnums;
import com.echronos.expo.enums.ExportTypeEnum;
import com.echronos.expo.param.*;
import com.echronos.expo.param.booth.*;
import com.echronos.expo.service.IExpoAttachmentFileService;
import com.echronos.expo.service.IExpoBoothService;
import com.echronos.expo.util.excel.CellPullDownWriteHandler;
import com.echronos.expo.util.excel.EasyExcelUtil;
import com.echronos.expo.util.excel.I18nCellWriteHandler;
import com.echronos.expo.vo.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.OutputStream;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 展位管理控制器
 *
 * <AUTHOR>
 * @date 2025-08-01
 */
@Slf4j
@RestController
@RequestMapping("v1/expo/booth")
public class ExpoBoothController {

    @Resource
    private IExpoBoothService expoBoothService;
    @Resource
    private IExpoAttachmentFileService expoAttachmentFileService;
    @Resource
    private HttpServletResponse response;

    /**
     * 展位统计
     * 
     * @param param 展位id
     * @return 结果
     */
    @GetMapping(value = "statistics")
    public Result<ExpoBoothStatisticsVO> boothStatistics(ExpoFormIdParam param) {
        ExpoBoothDTO dto = CopyObjectUtils.copyAtoB(param, ExpoBoothDTO.class);
        return Result.build(expoBoothService.boothStatistics(dto));
    }

    /**
     * 展位附件列表
     * 
     * @param param 展位id
     * @return 结果
     */
    @GetMapping(value = "attachment/files")
    public Result<List<ExpoAttachmentFileVO>> boothAttachmentFiles(ExpoFormIdParam param) {
        ExpoBoothDTO dto = CopyObjectUtils.copyAtoB(param, ExpoBoothDTO.class);
        return Result.build(expoBoothService.getBoothAttachmentFiles(dto));
    }

    /**
     * 添加/更新展位附件
     * 
     * @param param 附件参数
     * @return 结果
     */
    @PutMapping(value = "edit/files")
    public Result<ExpoBoothVO> addAttachmentFiles(@RequestBody ExpoAttachmentFileListParam param) {
        List<ExpoAttachmentFileDTO> fileDTOList = null;
        if (CollectionUtil.isNotEmpty(param.getFileList())) {
            fileDTOList = CopyObjectUtils.copyAlistToBlist(param.getFileList(), ExpoAttachmentFileDTO.class);
        }
        expoAttachmentFileService.addOrUpdateFile(param.getExpoId(), param.getBusinessId(), fileDTOList, ExpoAttachmentFileEnum.Type.BOOTH_LAYOUT);
        return Result.build();
    }

    /**
     * 展位列表
     * 
     * @param param 查询参数
     * @return 结果
     */
    @PostMapping(value = "page/list")
    public Result<List<ExpoBoothVO>> pageList(@RequestBody GetBoothPageParam param) {
        RequestUser user = RequestUserUtils.getUser();
        ExpoBoothPageDTO dto = CopyObjectUtils.copyAtoB(param, ExpoBoothPageDTO.class);
        dto.setCompanyId(user.getCompanyId());
        IPage<ExpoBoothVO> page = expoBoothService.pageList(dto);
        return Result.build(page.getRecords(), page.getTotal());
    }

    /**
     * 展位详情
     * @param param
     * @return
     */
    @GetMapping(value = "detail")
    public Result<ExpoBoothDetailVO> detail(ExpoFormIdParam param) {
        ExpoBoothPageDTO dto = CopyObjectUtils.copyAtoB(param, ExpoBoothPageDTO.class);
        return Result.build(expoBoothService.detail(dto));
    }

    /**
     * 新增/编辑展位
     *
     * @param param 展位参数
     * @return 结果
     */
    @PostMapping("saveOrUpdate")
    public Result saveOrUpdateBooth(@RequestBody AddOrUpdateBoothParam param) {
        ExpoBoothDTO dto = CopyObjectUtils.copyAtoB(param, ExpoBoothDTO.class);
        expoBoothService.saveOrUpdateBooth(dto);
        return Result.build();
    }

    /**
     * 删除展位
     * 
     * @param param 展位id
     * @return 删除结果
     */
    @PutMapping(value = "del")
    public Result delBooth(@RequestBody ExpoFormIdParam param) {
        ExpoBoothDTO dto = CopyObjectUtils.copyAtoB(param, ExpoBoothDTO.class);
        expoBoothService.delBooth(dto);
        return Result.build();
    }

    /**
     * 添加展位订单
     * @return 结果
     */
    @PostMapping(value = "add/order")
    public Result addOrder(@RequestBody AddBoothOrderParam param) {
        ExpoBoothOrderDTO dto = CopyObjectUtils.copyAtoB(param, ExpoBoothOrderDTO.class);
        RequestUser user = RequestUserUtils.getUser();
        dto.setCompanyId(user.getCompanyId());
        dto.setUserId(user.getId());
        dto.setMemberId(user.getMemberId());
        expoBoothService.addOrder(dto);
        return Result.build();
    }

    /**
     * 下载模板
     */
    @GetMapping(value = "down/template")
    public void downTemplate() {
        OutputStream outputStream = null;
        try {
            setDownResponse(response, ExportTypeEnum.ExportType.EXPORT_BOOTH.getDesc() + ExcelTypeEnum.XLSX.getValue());
            // 获取响应到客户端的输出流
            outputStream = response.getOutputStream();
            // 下拉框值
            Map<String, String[]> dropDownValMap = new HashMap<>();
            // 展位类型下拉（国际化处理）
            ExpoBoothEnums.BoothType[] boothTypeValues = ExpoBoothEnums.BoothType.values();
            String[] boothTypeDropDownVal = Arrays.stream(boothTypeValues).map(ExpoBoothEnums.BoothType::getMsg)
                    .collect(Collectors.toList()).toArray(new String[boothTypeValues.length]);
            dropDownValMap.put(DropDownFieldConstant.BOOTH_TYPE, boothTypeDropDownVal);
            // 开始导出
            EasyExcelUtil.exportExcelTemplate(outputStream, dropDownValMap, new ImportBoothExcelModel(), null,
                    null, null, null, Boolean.TRUE);
            // 刷新
            outputStream.flush();
        } catch (Exception e) {
            log.error("下载展位导入Excel模板异常 error={}", e.getMessage());
        } finally {
            if (null != outputStream) {
                try {
                    outputStream.close();
                } catch (IOException e) {
                    log.error("下载展位导入Excel模板关闭响应流异常 error={}", e.getMessage());
                }
            }
        }
    }

    /**
     * 导入模板数据
     * 
     * @return
     */
    @PostMapping(value = "import/template")
    public Result<ExpoBoothImportVO> importTemplate(ImportExpoBoothParam param) {
        ExpoBoothDTO dto = CopyObjectUtils.copyAtoB(param, ExpoBoothDTO.class);
        ExpoBoothImportVO vo = expoBoothService.importTemplate(dto);
        return Result.build(vo);
    }

    /**
     * 导出数据
     * 
     * @param param 查询参数
     * @throws IOException
     */
    @PostMapping(value = "export/data")
    public void exportData(ExportBoothParam param) throws IOException {
        RequestUser user = RequestUserUtils.getUser();
        ExpoBoothPageDTO dto = CopyObjectUtils.copyAtoB(param, ExpoBoothPageDTO.class);
        dto.setCompanyId(user.getCompanyId());
        // 分页查询导出数据
        List<ExportBoothExcelModel> excelModelList = new ArrayList<>();
        int pageNo = 1;
        int pageSize = 1000;
        while (true) {
            log.info("展位导出开始查询第{}页数据", pageNo);
            dto.setPageNo(pageNo);
            dto.setPageSize(pageSize);
            IPage<ExpoBoothVO> page = expoBoothService.pageList(dto);
            if (CollectionUtil.isEmpty(page.getRecords())) {
                log.info("展位导出结束查询最终数据数量={}", excelModelList.size());
                break;
            }
            for (ExpoBoothVO vo : page.getRecords()) {
                ExportBoothExcelModel model = CopyObjectUtils.copyAtoB(vo, ExportBoothExcelModel.class);
                excelModelList.add(model);
            }
        }
        // 下拉框值
        Map<Integer, String[]> dropDownValMap = new HashMap<>();
        // 展位类型下拉
        ExpoBoothEnums.BoothType[] boothTypeValues = ExpoBoothEnums.BoothType.values();
        String[] boothTypeDropDownVal = Arrays.stream(boothTypeValues).map(ExpoBoothEnums.BoothType::getMsg)
                .collect(Collectors.toList()).toArray(new String[boothTypeValues.length]);
        dropDownValMap.put(5, boothTypeDropDownVal);

        setDownResponse(response, ExportTypeEnum.ExportType.EXPORT_BOOTH.getDesc() + ExcelTypeEnum.XLSX.getValue());
        EasyExcel.write(response.getOutputStream(), ImportBoothExcelModel.class)
                .inMemory(true)
                .registerWriteHandler(new I18nCellWriteHandler())
                .registerWriteHandler(new CellPullDownWriteHandler(dropDownValMap, true))
                .sheet(ExportTypeEnum.ExportType.EXPORT_BOOTH.getDesc())
                .doWrite(excelModelList);
    }

    /**
     * 下载请求头设置
     * 
     * @param response 响应流
     * @param fileName 文件名
     * @throws UnsupportedEncodingException 拦截文件名encode转换异常
     */
    private void setDownResponse(HttpServletResponse response, String fileName) throws UnsupportedEncodingException {
        // 响应到页面下载，配置文件下载
        response.setCharacterEncoding("utf-8");
        response.setContentType("application/vnd.ms-excel;charset=utf-8");
        // 下载文件能正常显示中文
        response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(fileName, "UTF-8"));
    }

}
