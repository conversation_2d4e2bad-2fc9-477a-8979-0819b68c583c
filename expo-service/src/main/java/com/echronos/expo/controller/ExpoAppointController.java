package com.echronos.expo.controller;

import com.echronos.commons.Result;
import com.echronos.commons.utils.CopyObjectUtils;
import com.echronos.commons.utils.RequestUserUtils;
import com.echronos.expo.dto.ExpoAppointedPersonnelDTO;
import com.echronos.expo.dto.ExpoAppointedPersonnelTimeDTO;
import com.echronos.expo.dto.ExpoExhibitorDTO;
import com.echronos.expo.enums.ExpoAppointPurposeEnum;
import com.echronos.expo.param.IdParam;
import com.echronos.expo.param.appoint.AppointBusinessTypeParam;
import com.echronos.expo.param.appoint.AppointedPersonnelListParam;
import com.echronos.expo.service.IExpoAppointService;
import com.echronos.expo.vo.appoint.AppointPageInfoVO;
import com.echronos.expo.vo.appoint.AppointedPersonnelListVO;
import com.echronos.expo.vo.appoint.AppointmentPurposeVO;
import com.echronos.expo.vo.appoint.ExpoAppointedPersonnelTimeVO;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * 展会预约相关
 *
 * <AUTHOR>
 * @date 2025/8/7 11:15
 */
@RestController
@RequestMapping("/v1/expo/appoint/")
public class ExpoAppointController {

    @Resource
    private IExpoAppointService expoAppointService;

    /**
     * 查询可预约人员
     *
     * @param param 参数
     * @return List<AppointedPersonnelListVO>
     */
    @GetMapping("personnel/list")
    public Result<List<AppointedPersonnelListVO>> personnelList(AppointedPersonnelListParam param) {
        ExpoAppointedPersonnelDTO dto = CopyObjectUtils.copyAtoB(param, ExpoAppointedPersonnelDTO.class);
        dto.setCompanyId(RequestUserUtils.getUser().getCompanyId());
        List<AppointedPersonnelListVO> list = expoAppointService.personnelList(dto);
        return Result.build(list);
    }

    /**
     * 查询预约目的
     *
     * @param param 参数
     * @return List<AppointmentPurposeVO>
     */
    @GetMapping("purpose/list")
    public Result<List<AppointmentPurposeVO>> purposeList(AppointBusinessTypeParam param) {
        return Result.build(ExpoAppointPurposeEnum.getByType(param.getBusinessType()));
    }

    /**
     * 根据展商ID查询预约页面信息
     *
     * @param param 参数
     * @return AppointPageInfoVO
     */
    @GetMapping("page/info")
    public Result<AppointPageInfoVO> pageInfo(IdParam param) {
        ExpoExhibitorDTO dto = new ExpoExhibitorDTO();
        dto.setId(param.getId());
        AppointPageInfoVO vo = expoAppointService.pageInfo(dto);
        return Result.build(vo);
    }

    /**
     * 查询预约人员的预约时间段
     *
     * @param param 参数
     * @return List<ExpoAppointedPersonnelTimeVO>
     */
    @GetMapping("personnel/time")
    public Result<List<ExpoAppointedPersonnelTimeVO>> personnelTime(IdParam param) {
        ExpoAppointedPersonnelTimeDTO dto = new ExpoAppointedPersonnelTimeDTO();
        dto.setPersonnelId(param.getId());
        List<ExpoAppointedPersonnelTimeVO> list = expoAppointService.personnelTime(dto);
        return Result.build(list);
    }
}

