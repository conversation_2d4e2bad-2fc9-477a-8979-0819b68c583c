package com.echronos.expo.controller;

import com.echronos.commons.Result;
import com.echronos.commons.annotation.Permission;
import com.echronos.commons.utils.CopyObjectUtils;
import com.echronos.commons.utils.RequestUserUtils;
import com.echronos.expo.dto.ExpoFormDTO;
import com.echronos.expo.dto.ExpoFormPageDTO;
import com.echronos.expo.dto.ExpoFormScanDTO;
import com.echronos.expo.param.*;
import com.echronos.expo.service.IExpoFormService;
import com.echronos.expo.vo.ExpoFormVO;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * 展会表单管理
 *
 * <AUTHOR>
 * @Date 20025/5/13 16:004
 * @ClassName ExpoFormController
 */
@RestController
@RequestMapping("v1/expo/form")
public class ExpoFormController {

    @Resource
    private IExpoFormService iExpoFormService;

    /**
     * 查询展会表单
     * @param param
     * @return
     */
    @GetMapping("/all/list")
    public Result<List<ExpoFormVO>> queryList(IdParam param){
        return Result.build(iExpoFormService.getList(param.getId()));
    }

    /**
     * 创建展会表单
     *
     * @param param
     * @return
     */
    @PostMapping("add")
    @Permission({"BV_001_002_004_001_001"})
    public Result add(@RequestBody ExpoFormAddParam param) {
        ExpoFormDTO dto = CopyObjectUtils.copyAtoB(param, ExpoFormDTO.class);
        dto.setCompanyId(RequestUserUtils.getUser().getCompanyId());
        return iExpoFormService.edit(dto);
    }

    /**
     * 编辑展会表单
     *
     * @param param
     * @return
     */
    @PostMapping("edit")
    @Permission({"BV_001_002_004_001_002"})
    public Result edit(@RequestBody ExpoFormEditParam param) {
        ExpoFormDTO dto = CopyObjectUtils.copyAtoB(param, ExpoFormDTO.class);
        dto.setCompanyId(RequestUserUtils.getUser().getCompanyId());
        return iExpoFormService.edit(dto);
    }

    /**
     * 分页查询展会表单
     *
     * @param param
     * @return
     */
    @GetMapping("pageFor")
    @Permission({"BV_001_002_004_001"})
    public Result<List<ExpoFormVO>> pageFor(ExpoFormPageParam param) {
        ExpoFormPageDTO dto = CopyObjectUtils.copyAtoB(param, ExpoFormPageDTO.class);
        dto.setCompanyId(RequestUserUtils.getUser().getCompanyId());
        return iExpoFormService.pageFor(dto);
    }

    /**
     * 查询展会表单详情
     *
     * @param param
     * @return
     */
    @GetMapping("info")
    @Permission({"BV_001_002_004_001"})
    public Result<ExpoFormVO> info(ExpoFormIdParam param) {
        ExpoFormDTO dto = CopyObjectUtils.copyAtoB(param, ExpoFormDTO.class);
        dto.setCompanyId(RequestUserUtils.getUser().getCompanyId());
        return iExpoFormService.info(dto);
    }

    /**
     * 展会表单列表
     *
     * @param param
     * @return
     */
    @GetMapping("list")
    public Result<List<ExpoFormVO>> list(ExpoFormListParam param) {
        ExpoFormPageDTO dto = CopyObjectUtils.copyAtoB(param, ExpoFormPageDTO.class);
        dto.setCompanyId(RequestUserUtils.getUser().getCompanyId());
        return iExpoFormService.list(dto);
    }

    /**
     * 表单删除
     *
     * @param param
     * @return
     */
    @PostMapping("delete")
    @Permission({"BV_001_002_004_001_003"})
    public Result del(@RequestBody ExpoFormDelParam param) {
        ExpoFormDTO dto = CopyObjectUtils.copyAtoB(param, ExpoFormDTO.class);
        dto.setCompanyId(RequestUserUtils.getUser().getCompanyId());
        return iExpoFormService.del(dto);
    }

    /**
     * 查询扫码配置
     *
     * @param param
     * @return
     */
    @GetMapping("/scan/config")
    @Permission({"BV_001_002_006_001"})
    public Result<ExpoFormVO> scanFormConfig(ExpoFormConfigGetParam param) {
        ExpoFormDTO dto = CopyObjectUtils.copyAtoB(param, ExpoFormDTO.class);
        dto.setCompanyId(RequestUserUtils.getUser().getCompanyId());
        return iExpoFormService.scanConfig(dto);
    }

    /**
     * 配置观众扫码表单
     *
     * @param param
     * @return
     */
    @PostMapping("editConfigScan")
    @Permission({"BV_001_002_006_002"})
    public Result editConfigScan(@RequestBody ExpoFormScanConfigSaveParam param) {
        ExpoFormScanDTO dto = CopyObjectUtils.copyAtoB(param, ExpoFormScanDTO.class);
        dto.setCompanyId(RequestUserUtils.getUser().getCompanyId());
        return iExpoFormService.editConfigScan(dto);
    }

    /**
     * 复制表单
     * @param copyExpoFormParam
     * @return
     */
    @PostMapping(value = "copy/form")
    public Result copyExpoForm(@RequestBody CopyExpoFormParam copyExpoFormParam){
        ExpoFormDTO dto = CopyObjectUtils.copyAtoB(copyExpoFormParam, ExpoFormDTO.class);
        dto.setCompanyId(RequestUserUtils.getUser().getCompanyId());
        dto.setUserId(RequestUserUtils.getUser().getId());
        return iExpoFormService.copyExpoForm(dto);
    }

    /**
     * 保存打印配置
     *
     * @param param 参数
     */
    @PostMapping("/save/print/config")
    public Result<?> savePrintConfig(@RequestBody ExpoPrintConfigParam param) {
        return Result.build();
    }

    /**
     * 查询打印配置
     *
     * @param param 参数
     */
    @GetMapping("/query/print/config")
    public Result<?> queryPrintConfig(ExpoPrintConfigParam param) {
        return Result.build();
    }
}
