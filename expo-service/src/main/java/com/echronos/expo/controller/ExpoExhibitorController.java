package com.echronos.expo.controller;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.excel.EasyExcel;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.echronos.commons.Result;
import com.echronos.commons.model.RequestUser;
import com.echronos.commons.page.PageVO;
import com.echronos.commons.utils.CopyObjectUtils;
import com.echronos.commons.utils.RequestUserUtils;
import com.echronos.expo.dto.AddExhibitorOrderDTO;
import com.echronos.expo.dto.ExpoAppointedPersonnelDTO;
import com.echronos.expo.dto.ExpoAppointmentDTO;
import com.echronos.expo.dto.ExpoExhibitorDTO;
import com.echronos.expo.easyexcel.model.ExportExhibitorExcelModel;
import com.echronos.expo.easyexcel.model.ImportBoothExcelModel;
import com.echronos.expo.enums.ExpoAppointmentStatusEnum;
import com.echronos.expo.enums.ExpoBusinessTypeEnum;
import com.echronos.expo.enums.ExportTypeEnum;
import com.echronos.expo.param.IdParam;
import com.echronos.expo.param.appoint.*;
import com.echronos.expo.param.exhibitor.*;
import com.echronos.expo.service.IExpoAppointService;
import com.echronos.expo.service.IExpoExhibitorService;
import com.echronos.expo.util.excel.CellPullDownWriteHandler;
import com.echronos.expo.util.excel.I18nCellWriteHandler;
import com.echronos.expo.vo.*;
import com.echronos.expo.vo.appoint.*;
import com.echronos.expo.vo.exhibitor.ExhibitorDetailPlatformVO;
import com.echronos.expo.vo.exhibitor.ExhibitorDetailVO;
import com.echronos.expo.vo.exhibitor.ExhibitorInviteQrCodeVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

/**
 * 展商管理控制器
 *
 * <AUTHOR>
 * date2025/8/4 15:00
 */
@Slf4j
@RestController
@RequestMapping("v1/expo/exhibitor")
public class ExpoExhibitorController {

    @Resource
    private IExpoExhibitorService expoExhibitorService;
    @Resource
    private HttpServletResponse response;
    @Resource
    private IExpoAppointService expoAppointService;

    /**
     * 展商列表
     *
     * @param param 查询参数
     * @return 结果
     */
    @PostMapping(value = "page/list")
    public Result<List<ExpoExhibitorVO>> pageList(@RequestBody GetExhibitorPageParam param) {
        ExpoExhibitorDTO dto = CopyObjectUtils.copyAtoB(param, ExpoExhibitorDTO.class);
        buildUserInfo(dto);
        IPage<ExpoExhibitorVO> page = expoExhibitorService.pageList(dto);
        return Result.build(page.getRecords(), page.getTotal());
    }

    /**
     * 获取编辑详情
     *
     * @param param 参数
     * @return 结果
     */
    @GetMapping(value = "get/edit/detail")
    public Result<ExpoExhibitorEditDetailVO> getEditDetail(GetExhibitorEditDetailParam param) {
        ExpoExhibitorDTO dto = CopyObjectUtils.copyAtoB(param, ExpoExhibitorDTO.class);
        buildUserInfo(dto);
        return Result.build(expoExhibitorService.getEditDetail(dto));
    }

    /**
     * 展商编辑基础信息详情（平台方）
     *
     * @param param 参数
     * @return
     */
    @PutMapping(value = "edit/detail")
    public Result editDetail(@RequestBody EditExhibitorDetailParam param) {
        ExpoExhibitorDTO dto = CopyObjectUtils.copyAtoB(param, ExpoExhibitorDTO.class);
        buildUserInfo(dto);
        expoExhibitorService.editDetail(dto);
        return Result.build();
    }

    /**
     * 展商详情（平台方视角）
     */
    @GetMapping(value = "get/detail/platform")
    public Result<ExhibitorDetailPlatformVO> getDetailPlatform(@RequestBody IdParam param) {
        ExpoExhibitorDTO dto = CopyObjectUtils.copyAtoB(param, ExpoExhibitorDTO.class);
        buildUserInfo(dto);
        return Result.build(expoExhibitorService.getDetailPlatform(dto));
    }


    /**
     * 展商详情（展商视角）
     *
     * @param param 参数
     * @return
     */
    @GetMapping(value = "get/detail/participants")
    public Result<ExhibitorDetailVO> getDetailParticipants(@RequestBody IdParam param) {
        ExpoExhibitorDTO dto = CopyObjectUtils.copyAtoB(param, ExpoExhibitorDTO.class);
        buildUserInfo(dto);
        return Result.build(expoExhibitorService.getDetailParticipants(dto));
    }

    /**
     * 删除展商
     *
     * @param param
     * @return
     */
    @PostMapping(value = "del")
    public Result delExhibitor(@RequestBody IdParam param) {
        ExpoExhibitorDTO dto = CopyObjectUtils.copyAtoB(param, ExpoExhibitorDTO.class);
        buildUserInfo(dto);
        expoExhibitorService.delete(dto);
        return Result.build();
    }

    /**
     * 获取展商邀请观众二维码
     *
     * @param param
     * @return
     */
    @GetMapping(value = "get/invite/qrcode")
    public Result<ExhibitorInviteQrCodeVO> getInviteQrCode(IdParam param) {
        ExpoExhibitorDTO dto = CopyObjectUtils.copyAtoB(param, ExpoExhibitorDTO.class);
        return Result.build(expoExhibitorService.getInviteQrCode(dto));
    }


    /**
     * 添加租赁订单
     *
     * @return 结果
     */
    @PostMapping(value = "add/lease/order")
    public Result addLeaseOrder(@RequestBody AddLeaseOrderParam param) {
        AddExhibitorOrderDTO dto = CopyObjectUtils.copyAtoB(param, AddExhibitorOrderDTO.class);
        RequestUser user = RequestUserUtils.getUser();
        dto.setCompanyId(user.getCompanyId());
        dto.setUserId(user.getId());
        dto.setMemberId(user.getMemberId());
        expoExhibitorService.addLeaseOrder(dto);
        return Result.build();
    }

    /**
     * 添加商旅订单
     *
     * @return 结果
     */
    @PostMapping(value = "add/travel/order")
    public Result addTravelOrder(@RequestBody AddTravelOrderParam param) {
        AddExhibitorOrderDTO dto = CopyObjectUtils.copyAtoB(param, AddExhibitorOrderDTO.class);
        RequestUser user = RequestUserUtils.getUser();
        dto.setCompanyId(user.getCompanyId());
        dto.setUserId(user.getId());
        dto.setMemberId(user.getMemberId());
        expoExhibitorService.addTravelOrder(dto);
        return Result.build();
    }


    /**
     * 导出数据
     *
     * @param param 查询参数
     * @throws IOException
     */
    @PostMapping(value = "export/data")
    public void exportData(ExportExhibitorParam param) throws IOException {
        ExpoExhibitorDTO dto = CopyObjectUtils.copyAtoB(param, ExpoExhibitorDTO.class);
        buildUserInfo(dto);
        // 分页查询导出数据
        List<ExportExhibitorExcelModel> excelModelList = new ArrayList<>();
        int pageNo = 1;
        int pageSize = 300;
        while (true) {
            log.info("展商导出开始查询第{}页数据", pageNo);
            dto.setPageNo(pageNo);
            dto.setPageSize(pageSize);
            IPage<ExpoExhibitorVO> page = expoExhibitorService.pageList(dto);
            if (CollectionUtil.isEmpty(page.getRecords())) {
                log.info("展商导出结束查询最终数据数量={}", excelModelList.size());
                break;
            }
            for (ExpoExhibitorVO vo : page.getRecords()) {
                ExportExhibitorExcelModel model = CopyObjectUtils.copyAtoB(vo, ExportExhibitorExcelModel.class);
                excelModelList.add(model);
            }
        }
        EasyExcel.write(response.getOutputStream(), ImportBoothExcelModel.class)
                .inMemory(true)
                .registerWriteHandler(new I18nCellWriteHandler())
                .registerWriteHandler(new CellPullDownWriteHandler(new HashMap<>(), true))
                .sheet(ExportTypeEnum.ExportType.EXPORT_EXHIBITOR.getDesc())
                .doWrite(excelModelList);
    }


    /**
     * 构建用户信息
     *
     * @param dto
     */
    private void buildUserInfo(ExpoExhibitorDTO dto) {
        RequestUser user = RequestUserUtils.getUser();
        dto.setCompanyId(user.getCompanyId());
        dto.setUserId(user.getId());
    }


    /**
     * 展商扫码
     *
     * @return
     */
    @GetMapping(value = "/scan/code")
    public Result scanCode(ExpoExhibitorScanCodeParam param) {
        ExpoExhibitorDTO dto = CopyObjectUtils.copyAtoB(param, ExpoExhibitorDTO.class);
        buildUserInfo(dto);
        expoExhibitorService.scanCode(dto);
        return Result.build();
    }

    /**
     * 建站查询展商列表
     *
     * @return
     */
    @PostMapping(value = "/web/query/list")
    public Result<List<WebExpoExhibitorVO>> webQueryExhibitorList(@RequestBody WebExhibitorParam param) {
        ExpoExhibitorDTO dto = CopyObjectUtils.copyAtoB(param, ExpoExhibitorDTO.class);
        buildUserInfo(dto);
        ExpoPageVO<WebExpoExhibitorVO> expoPageVO = expoExhibitorService.webQueryExhibitorList(dto);
        return Result.build(expoPageVO.getRecords(), expoPageVO.getTotalElements());
    }

    /**
     * 校验是否为展商对应展会的观众
     *
     * @return
     */
    @GetMapping(value = "/validity/has/audience")
    public Result<ValidityHasAudienceVO> validityHasAudience(GetExhibitorEditDetailParam param) {
        ExpoExhibitorDTO dto = CopyObjectUtils.copyAtoB(param, ExpoExhibitorDTO.class);
        buildUserInfo(dto);
        return Result.build(expoExhibitorService.validityHasAudience(dto));
    }


    /**
     * 预约设置
     *
     * @param param 参数
     */
    @PutMapping("appoint/set")
    public Result<?> appointSet(@RequestBody AppointSetParam param) {
        ExpoExhibitorDTO dto = CopyObjectUtils.copyAtoB(param, ExpoExhibitorDTO.class);
        dto.setCompanyId(RequestUserUtils.getUser().getCompanyId());
        expoExhibitorService.appointSet(dto);
        return Result.build();
    }

    /**
     * 查询预约设置
     *
     * @param param 参数
     * @return AppointmentSetVO
     */
    @GetMapping("appoint/get/set")
    public Result<AppointmentSetVO> getAppointSet(AppointSetGetParam param) {
        ExpoAppointedPersonnelDTO dto = CopyObjectUtils.copyAtoB(param, ExpoAppointedPersonnelDTO.class);
        dto.setBusinessType(ExpoBusinessTypeEnum.EXHIBITOR.getCode());
        dto.setCompanyId(RequestUserUtils.getUser().getCompanyId());
        AppointmentSetVO appointmentSetVO = expoAppointService.getAppointSet(dto);
        return Result.build(appointmentSetVO);
    }

    /**
     * 设置时间
     *
     * @param param 参数
     */
    @PutMapping("appoint/set/time")
    public Result<?> appointSetTime(@RequestBody AppointSetTimeParam param) {
        ExpoAppointedPersonnelDTO dto = CopyObjectUtils.copyAtoB(param, ExpoAppointedPersonnelDTO.class);
        dto.setCompanyId(RequestUserUtils.getUser().getCompanyId());
        expoAppointService.appointSetTime(dto);
        return Result.build();
    }

    /**
     * 可预约人员回显
     *
     * @param param 参数
     * @return List<ExpoAppointedPersonnelVO>
     */
    @GetMapping("appoint/personnel/echo")
    public Result<List<ExpoAppointedPersonnelVO>> appointPersonnelEcho(AppointSetGetParam param) {
        ExpoAppointedPersonnelDTO dto = CopyObjectUtils.copyAtoB(param, ExpoAppointedPersonnelDTO.class);
        dto.setBusinessType(ExpoBusinessTypeEnum.EXHIBITOR.getCode());
        dto.setCompanyId(RequestUserUtils.getUser().getCompanyId());
        List<ExpoAppointedPersonnelVO> list = expoAppointService.appointPersonnelEcho(dto);
        return Result.build(list);
    }

    /**
     * 添加可预约人员
     *
     * @param param 参数
     */
    @PostMapping("appoint/add/personnel")
    public Result<?> appointAddPersonnel(@RequestBody AppointAddPersonnelParam param) {
        ExpoAppointedPersonnelDTO dto = CopyObjectUtils.copyAtoB(param, ExpoAppointedPersonnelDTO.class);
        dto.setBusinessType(ExpoBusinessTypeEnum.EXHIBITOR.getCode());
        dto.setCompanyId(RequestUserUtils.getUser().getCompanyId());
        expoAppointService.appointAddPersonnel(dto);
        return Result.build();
    }

    /**
     * 删除可预约人员
     *
     * @param param 参数
     */
    @PutMapping("appoint/del/personnel")
    public Result<?> appointDelPersonnel(@RequestBody IdParam param) {
        ExpoAppointedPersonnelDTO dto = new ExpoAppointedPersonnelDTO();
        dto.setIsDeleted(param.getId());
        dto.setCompanyId(RequestUserUtils.getUser().getCompanyId());
        expoAppointService.appointDelPersonnel(dto);
        return Result.build();
    }

    /**
     * 预约观众
     *
     * @param param 参数
     */
    @PostMapping("appoint/audience")
    public Result<?> appointAudience(@RequestBody AppointSubmitParam param) {
        ExpoAppointmentDTO dto = CopyObjectUtils.copyAtoB(param, ExpoAppointmentDTO.class);
        dto.setBusinessType(ExpoBusinessTypeEnum.EXHIBITOR.getCode());
        dto.setCreateUser(RequestUserUtils.getUser().getId());
        dto.setMemberId(RequestUserUtils.getUser().getMemberId());
        dto.setCompanyId(RequestUserUtils.getUser().getCompanyId());
        expoAppointService.appointSubmit(dto);
        return Result.build();
    }

    /**
     * 待处理预约列表
     *
     * @param param 参数
     * @return List<AppointPendingListVO>
     */
    @GetMapping("appoint/pending/list")
    public Result<List<AppointPendingListVO>> pendingList(AppointSetGetParam param) {
        ExpoAppointmentDTO dto = CopyObjectUtils.copyAtoB(param, ExpoAppointmentDTO.class);
        dto.setBusinessType(ExpoBusinessTypeEnum.EXHIBITOR.getCode());
        dto.setCompanyId(RequestUserUtils.getUser().getCompanyId());
        PageVO<AppointPendingListVO> pageVO = expoAppointService.pendingList(dto);
        return Result.build(pageVO.getList(), (long) pageVO.getTotal());
    }

    /**
     * 展会预约时间列表
     *
     * @param param 参数
     * @return List<AppointTimeListVO>
     */
    @GetMapping("appoint/time/list")
    public Result<List<AppointTimeListVO>> timeList(AppointSetGetParam param) {
        ExpoAppointmentDTO dto = CopyObjectUtils.copyAtoB(param, ExpoAppointmentDTO.class);
        dto.setBusinessType(ExpoBusinessTypeEnum.EXHIBITOR.getCode());
        dto.setCompanyId(RequestUserUtils.getUser().getCompanyId());
        List<AppointTimeListVO> list = expoAppointService.timeList(dto);
        return Result.build(list);
    }

    /**
     * 预约详情
     *
     * @param param 参数
     * @return AppointDetailVO
     */
    @GetMapping("appoint/detail")
    public Result<AppointDetailVO> appointDetail(IdParam param) {
        ExpoAppointmentDTO dto = new ExpoAppointmentDTO();
        dto.setId(param.getId());
        dto.setBusinessType(ExpoBusinessTypeEnum.EXHIBITOR.getCode());
        dto.setCompanyId(RequestUserUtils.getUser().getCompanyId());
        AppointDetailVO appointDetailVO = expoAppointService.appointDetail(dto);
        return Result.build(appointDetailVO);
    }

    /**
     * 接受预约
     *
     * @param param 参数
     */
    @PutMapping("appoint/accept")
    public Result<?> appointAccept(@RequestBody AppointHandleParam param) {
        ExpoAppointmentDTO dto = CopyObjectUtils.copyAtoB(param, ExpoAppointmentDTO.class);
        dto.setStatus(ExpoAppointmentStatusEnum.ACCEPTED.getCode());
        dto.setBusinessType(ExpoBusinessTypeEnum.EXHIBITOR.getCode());
        dto.setCompanyId(RequestUserUtils.getUser().getCompanyId());
        expoAppointService.appointHandle(dto);
        return Result.build();
    }

    /**
     * 拒绝预约
     *
     * @param param 参数
     */
    @PutMapping("appoint/refuse")
    public Result<?> appointRefuse(@RequestBody AppointHandleParam param) {
        ExpoAppointmentDTO dto = CopyObjectUtils.copyAtoB(param, ExpoAppointmentDTO.class);
        dto.setStatus(ExpoAppointmentStatusEnum.REJECTED.getCode());
        dto.setBusinessType(ExpoBusinessTypeEnum.EXHIBITOR.getCode());
        dto.setCompanyId(RequestUserUtils.getUser().getCompanyId());
        expoAppointService.appointHandle(dto);
        return Result.build();
    }

    /**
     * 取消预约
     *
     * @param param 参数
     */
    @PutMapping("appoint/cancel")
    public Result<?> appointCancel(@RequestBody AppointHandleParam param) {
        ExpoAppointmentDTO dto = CopyObjectUtils.copyAtoB(param, ExpoAppointmentDTO.class);
        dto.setStatus(ExpoAppointmentStatusEnum.CANCELLED.getCode());
        dto.setBusinessType(ExpoBusinessTypeEnum.EXHIBITOR.getCode());
        dto.setCompanyId(RequestUserUtils.getUser().getCompanyId());
        expoAppointService.appointHandle(dto);
        return Result.build();
    }

    /**
     * 根据租户ID查询展商信息(官网展商列表)
     *
     * @return
     */
    @GetMapping(value = "/list/by/tenantId")
    public Result<List<WebExpoExhibitorVO>> queryExhibitorListByTenantId(QueryExhibitorByTenantIdParam param) {
        ExpoExhibitorDTO dto = CopyObjectUtils.copyAtoB(param, ExpoExhibitorDTO.class);
        ExpoPageVO<WebExpoExhibitorVO> expoPageVO = expoExhibitorService.queryExhibitorListByTenantId(dto);
        return Result.build(expoPageVO.getRecords(), expoPageVO.getTotalElements());
    }
}
