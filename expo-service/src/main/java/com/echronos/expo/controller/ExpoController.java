package com.echronos.expo.controller;

import cn.hutool.core.collection.CollectionUtil;
import com.echronos.commons.Result;
import com.echronos.commons.annotation.Permission;
import com.echronos.commons.utils.CopyObjectUtils;
import com.echronos.commons.utils.RequestUserUtils;
import com.echronos.expo.dto.*;
import com.echronos.expo.param.*;
import com.echronos.expo.service.IExpoService;
import com.echronos.expo.vo.*;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * 展会管理
 *
 * <AUTHOR>
 * @Date 20025/5/13 16:004
 * @ClassName ExpoController
 */
@RestController
@RequestMapping("v1/expo")
public class ExpoController {

    @Resource
    private IExpoService iExpoService;

    /**
     * 创建展会
     *
     * @param param
     * @return
     */
    @PostMapping("add")
    @Permission({"BV_001_001"})
    public Result add(@RequestBody ExpoInfoAddParam param) {
        ExpoInfoDTO dto = CopyObjectUtils.copyAtoB(param, ExpoInfoDTO.class);
        dto.setCompanyId(RequestUserUtils.getUser().getCompanyId());
        return iExpoService.edit(dto);
    }

    /**
     * 编辑展会
     *
     * @param param
     * @return
     */
    @PostMapping("edit")
    @Permission({"BV_001_002_002_002"})
    public Result edit(@RequestBody ExpoInfoEditParam param) {
        ExpoInfoDTO dto = CopyObjectUtils.copyAtoB(param, ExpoInfoDTO.class);
        dto.setCompanyId(RequestUserUtils.getUser().getCompanyId());
        iExpoService.edit(dto);
        return Result.build();
    }

    /**
     * 分页查询展会
     *
     * @param param
     * @return
     */
    @GetMapping("pageFor")
    @Permission({"BV_001_002"})
    public Result<List<ExpoInfoVO>> pageFor(ExpoPageParam param) {
        ExpoInfoPageDTO dto = CopyObjectUtils.copyAtoB(param, ExpoInfoPageDTO.class);
        dto.setCompanyId(RequestUserUtils.getUser().getCompanyId());
        return iExpoService.pageFor(dto);
    }

    /**
     * 展会列表分页（主办方）
     *
     * @param param
     * @return
     */
    @GetMapping("page/list/platform")
    public Result<List<ExpoInfoVO>> pageListPlatform(ExpoPageParam param){
        ExpoInfoPageDTO dto = CopyObjectUtils.copyAtoB(param, ExpoInfoPageDTO.class);
        dto.setCompanyId(RequestUserUtils.getUser().getCompanyId());
        return iExpoService.pageList(dto);
    }

    /**
     *  展会列表分页（参展方）
     *
     * @param param
     * @return
     */
    @GetMapping("page/list/participants")
    public Result<List<ExpoInfoByExhibitorVO>> pageListParticipants(ExpoPageParam param){
        ExpoInfoPageDTO dto = CopyObjectUtils.copyAtoB(param, ExpoInfoPageDTO.class);
        dto.setCustomerCompanyId(RequestUserUtils.getUser().getCompanyId());
        Result<List<ExpoInfoVO>> result = iExpoService.pageList(dto);
        List<ExpoInfoByExhibitorVO> voList = null;
        if(CollectionUtil.isNotEmpty(result.getData())){
            voList = CopyObjectUtils.copyAlistToBlist(result.getData(), ExpoInfoByExhibitorVO.class);
        }
        return Result.build(voList, result.getTotalCount());
    }


    /**
     * 获取展会及复制表单列表
     *
     * @param param
     * @return
     */
    @GetMapping("list/copy/form")
    public List<ExpoInfoCopyFormVO> getListCopyFormList(IdParam param) {
        ExpoInfoDTO dto = CopyObjectUtils.copyAtoB(param, ExpoInfoDTO.class);
        dto.setCompanyId(RequestUserUtils.getUser().getCompanyId());
        return iExpoService.getListCopyFormList(dto);
    }

    /**
     * 展会详情
     *
     * @param param
     * @return
     */
    @GetMapping("info")
    @Permission({"BV_001_002_002_001"})
    public Result<ExpoInfoVO> info(ExpoInfoParam param) {
        ExpoInfoDTO dto = CopyObjectUtils.copyAtoB(param, ExpoInfoDTO.class);
        dto.setCompanyId(RequestUserUtils.getUser().getCompanyId());
        return iExpoService.info(dto);
    }

    /**
     * 展会列表
     *
     * @param param
     * @return
     */
    @GetMapping("list")
    public Result<List<ExpoInfoVO>> list(ExpoListParam param) {
        ExpoInfoDTO dto = CopyObjectUtils.copyAtoB(param, ExpoInfoDTO.class);
        dto.setCompanyId(RequestUserUtils.getUser().getCompanyId());
        return Result.build(iExpoService.list(dto));
    }

    /**
     * 删除展会
     *
     * @param param
     * @return
     */
    @PostMapping("delete")
    public Result delete(@RequestBody ExpoInfoParam param) {
        ExpoInfoDTO dto = CopyObjectUtils.copyAtoB(param, ExpoInfoDTO.class);
        dto.setCompanyId(RequestUserUtils.getUser().getCompanyId());
        return iExpoService.delete(dto);
    }

    /**
     * 渠道配置分页查询
     *
     * @param param
     * @return
     */
    @GetMapping("pageForChannel")
    @Permission({"BV_001_002_005_001"})
    public Result<List<ExpoChannelConfigVO>> pageForChannel(ExpoChannelConfigPageParam param) {
        ExpoChannelConfigPageDTO dto = CopyObjectUtils.copyAtoB(param, ExpoChannelConfigPageDTO.class);
        dto.setCompanyId(RequestUserUtils.getUser().getCompanyId());
        dto.setUserId(RequestUserUtils.getUser().getId());
        dto.setMemberId(RequestUserUtils.getUser().getMemberId());
        return iExpoService.pageForChannel(dto);
    }
    /**
     * 渠道配置列表
     *
     * @param param
     * @return
     */
    @GetMapping("listForChannelConfig")
    public Result<List<ExpoChannelConfigVO>> listForChannelConfig(ExpoChannelConfigPageParam param) {
        ExpoChannelConfigDTO dto = CopyObjectUtils.copyAtoB(param, ExpoChannelConfigDTO.class);
        dto.setCompanyId(RequestUserUtils.getUser().getCompanyId());
        dto.setUserId(RequestUserUtils.getUser().getId());
        return iExpoService.channelConfigList(dto);
    }
    /**
     * 配置展会观众渠道
     *
     * @param param
     * @return
     */
    @PostMapping("channelConfigAdd")
    @Permission({"BV_001_002_005_001_001"})
    public Result channelConfigAdd(@RequestBody ExpoChannelConfigAddParam param) {
        ExpoChannelConfigDTO dto = CopyObjectUtils.copyAtoB(param, ExpoChannelConfigDTO.class);
        dto.setCompanyId(RequestUserUtils.getUser().getCompanyId());
        dto.setChannelType(1);
        return iExpoService.channelConfigSetting(dto);
    }

    /**
     * 修改展会观众渠道
     *
     * @param param
     * @return
     */
    @PostMapping("channelConfigEdit")
    @Permission({"BV_001_002_005_001_002"})
    public Result channelConfigEdit(@RequestBody ExpoChannelConfigEditParam param) {
        ExpoChannelConfigDTO dto = CopyObjectUtils.copyAtoB(param, ExpoChannelConfigDTO.class);
        dto.setCompanyId(RequestUserUtils.getUser().getCompanyId());
        return iExpoService.channelConfigSetting(dto);
    }

    /**
     * 渠道配置详情
     *
     * @param param
     * @return
     */
    @GetMapping("channelConfig")
    public Result<ExpoChannelConfigVO> channelConfig(ExpoChannelConfigParam param) {
        ExpoChannelConfigDTO dto = CopyObjectUtils.copyAtoB(param, ExpoChannelConfigDTO.class);
        dto.setCompanyId(RequestUserUtils.getUser().getCompanyId());
        return iExpoService.channelConfig(dto);
    }

    /**
     * 删除渠道配置
     *
     * @param param
     * @return
     */
    @PostMapping("deleteChannelConfig")
    @Permission({"BV_001_002_005_001_003"})
    public Result deleteChannelConfig(@RequestBody ExpoChannelConfigParam param) {
        ExpoChannelConfigDTO dto = CopyObjectUtils.copyAtoB(param, ExpoChannelConfigDTO.class);
        dto.setCompanyId(RequestUserUtils.getUser().getCompanyId());
        return iExpoService.deleteChannelConfig(dto);
    }

    /**
     * 建站管理查询展会信息
     *
     * @return
     */
    @GetMapping(value = "/web/expo/page")
    public Result<List<ExpoInfoVO>> webExpoPage() {
        ExpoInfoDTO dto = new ExpoInfoDTO();
        dto.setCompanyId(RequestUserUtils.getUser().getCompanyId());
        return Result.build(iExpoService.webExpoPage(dto));
    }

    /**
     * 首页立即沟通
     *
     * @param param 参数
     * @return ExpoIndexCommunicateVO
     */
    @PostMapping("/index/communicate")
    public Result<ExpoIndexCommunicateVO> indexCommunicate(@RequestBody ExpoCommunicateParam param) {
        ExpoCommunicateGroupDTO dto = CopyObjectUtils.copyAtoB(param, ExpoCommunicateGroupDTO.class);
        dto.setCreateUser(RequestUserUtils.getUser().getId());
        ExpoIndexCommunicateVO vo = iExpoService.indexCommunicate(dto);
        return Result.build(vo);
    }
}
