package com.echronos.expo.controller;

import com.echronos.commons.Result;
import com.echronos.commons.model.RequestUser;
import com.echronos.commons.utils.CopyObjectUtils;
import com.echronos.commons.utils.RequestUserUtils;
import com.echronos.expo.dto.ExpoTenancySkuDTO;
import com.echronos.expo.param.tenancysku.EditTenancySkuParam;
import com.echronos.expo.param.tenancysku.QueryTenancySkuParam;
import com.echronos.expo.param.tenancysku.TenancySkuDetailParam;
import com.echronos.expo.service.IExpoTenancySkuService;
import com.echronos.expo.vo.ExpoPageVO;
import com.echronos.expo.vo.tenancysku.ExpoTenancySkuVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 展会商品租赁
 *
 * <AUTHOR>
 * date2025/8/9 13:59
 */
@Slf4j
@RestController
@RequestMapping("v1/expo/tenancy/sku")
public class ExpoTenancySkuController {

    @Autowired
    private IExpoTenancySkuService iExpoTenancySkuService;

    /**
     * 列表查询
     *
     * @return
     */
    @GetMapping(value = "/page")
    public Result<List<ExpoTenancySkuVO>> page(QueryTenancySkuParam param) {
        ExpoTenancySkuDTO dto = CopyObjectUtils.copyAtoB(param, ExpoTenancySkuDTO.class);
        buildUserDTO(dto);
        ExpoPageVO<ExpoTenancySkuVO> voExpoPageVO = iExpoTenancySkuService.page(dto);
        return Result.build(voExpoPageVO.getRecords(), voExpoPageVO.getTotalElements());
    }

    /**
     * 新增商品信息
     *
     * @param param
     * @return
     */
    @PostMapping(value = "/add")
    public Result add(@RequestBody EditTenancySkuParam param) {
        ExpoTenancySkuDTO dto = CopyObjectUtils.copyAtoB(param, ExpoTenancySkuDTO.class);
        buildUserDTO(dto);
        iExpoTenancySkuService.add(dto);
        return Result.build();
    }

    /**
     * 删除商品信息
     *
     * @return
     */
    @DeleteMapping(value = "/deleted")
    public Result del(@RequestBody TenancySkuDetailParam param) {
        ExpoTenancySkuDTO dto = CopyObjectUtils.copyAtoB(param, ExpoTenancySkuDTO.class);
        buildUserDTO(dto);
        iExpoTenancySkuService.del(dto);
        return Result.build();
    }

    /**
     * 填充用户信息
     *
     * @param dto
     */
    public void buildUserDTO(ExpoTenancySkuDTO dto) {
        RequestUser user = RequestUserUtils.getUser();
        dto.setUserId(user.getId());
        dto.setCompanyId(user.getCompanyId());
        dto.setMemberId(user.getMemberId());
    }


}
