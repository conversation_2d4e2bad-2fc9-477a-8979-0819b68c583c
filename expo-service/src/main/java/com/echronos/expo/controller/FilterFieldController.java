package com.echronos.expo.controller;

import com.echronos.commons.Result;
import com.echronos.expo.enums.BaseEnum;
import com.echronos.expo.enums.ExpoBoothEnums;
import com.echronos.expo.enums.ExpoExhibitorEnums;
import com.echronos.expo.param.FieldFilterParam;
import com.echronos.expo.vo.FieldFilterVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * 展会过滤字段下拉列表
 * <AUTHOR>
 * @date 2025-08-05 18:00
 */
@Slf4j
@RestController
@RequestMapping("v1/filter/field")
public class FilterFieldController {

    /**
     * 获取过滤字段列表
     * @return
     */
    @GetMapping(value = "get/list")
    private Result<List<FieldFilterVO>> selectFilterField(FieldFilterParam param){
        List<FieldFilterVO> voList = new ArrayList<>();
        switch (param.getBusinessType()){
            case 1:
                voList = convertEnumsToVOs(ExpoBoothEnums.BoothType.values());
                break;
            case 2:
                voList = convertEnumsToVOs(ExpoBoothEnums.Status.values());
                break;
            case 3:
                voList = convertEnumsToVOs(ExpoExhibitorEnums.ContractStatus.values());
                break;
            case 4:
                voList = convertEnumsToVOs(ExpoExhibitorEnums.SendEmailStatus.values());
                break;
            case 5:
                voList = convertEnumsToVOs(ExpoExhibitorEnums.EnterpriseInfoStatus.values());
                break;
            case 6:
                voList = convertEnumsToVOs(ExpoExhibitorEnums.JournalInfoStatus.values());
                break;
            case 7:
                voList = convertEnumsToVOs(ExpoExhibitorEnums.LeaseDemandType.values());
                break;
            default:
                return Result.build(Collections.emptyList());
        }
        return Result.build(voList);
    }

    /**
     * 将枚举数组转换为 FieldFilterVO 列表
     */
    private <T extends Enum<T> & BaseEnum<Integer, String>> List<FieldFilterVO> convertEnumsToVOs(T[] enumValues) {
        List<FieldFilterVO> list = new ArrayList<>();
        for (T value : enumValues) {
            list.add(new FieldFilterVO(value.code(), value.msg()));
        }
        return list;
    }

}
