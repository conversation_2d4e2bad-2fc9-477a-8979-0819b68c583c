package com.echronos.expo.annotation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * <AUTHOR>
 * @date 2025-5-14 16:15:29
 */
@Retention(RetentionPolicy.RUNTIME)
@Target({ElementType.METHOD, ElementType.FIELD, ElementType.PARAMETER})
public @interface FormField {
    /**
     * 名称
     *
     * @return
     */
    String label() default "";

    /**
     * 分组信息(1:基本信息，2：其它资料，3:归属资料，4:系统信息）
     *
     * @return
     */
    int group() default 1;

    /**
     * 是否必填
     */
    boolean required() default false;

    /**
     * 是否选中
     */
    boolean selected() default false;
    /**
     * [
     *   "text",
     *   "textarea",
     *   "number",
     *   "date",
     *   "radio",
     *   "checkbox",
     *   "image",
     *   "attachment",
     *   "childForm",
     *   "area",
     *   "select",
     *   "empty",
     *   "instruction"
     * ]
     *
     * @return
     */
    String type() default "text";

    /**
     * 父级字段
     *
     * @return
     */
    String parentFiled() default "";
}
