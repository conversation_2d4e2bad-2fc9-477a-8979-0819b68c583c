package com.echronos.expo.interceptor;


import com.echronos.commons.Result;
import com.echronos.commons.exception.BusinessException;
import com.echronos.commons.exception.ParamsValidateException;
import com.echronos.commons.utils.LogUtils;
import com.echronos.commons.utils.RandomUtils;
import com.echronos.expo.ExpoResult;
import com.echronos.expo.enums.ExpoResultCode;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.MessageSource;
import org.springframework.core.annotation.Order;
import org.springframework.validation.BindException;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletRequest;
import java.security.NoSuchAlgorithmException;

/**
 * <AUTHOR>
 * @date 2025年5月13日15:55:22
 */
@ControllerAdvice
@ResponseBody
@Order(1001)
@Slf4j
public class GlobalExceptionHandler {
    @ExceptionHandler(value = Exception.class)
    public Result<?> exceptionHandler(HttpServletRequest request, Exception e) {
        Result<?> result = new ExpoResult();
        //绑定异常是需要明确提示给用户的
        if (e instanceof BusinessException) {
            result.setMessage(e.getMessage());
            result.setCode(((BusinessException) e).getCode());
        } else if (e instanceof NoSuchAlgorithmException) {
            log.error("create salt or password is error.", e);
            result.setMessage(e.getMessage());
            result.setCode(((BusinessException) e).getCode());
        } else if (e instanceof ParamsValidateException) {
            result.setMessage(e.getMessage());
            result.setCode(((ParamsValidateException) e).getCode());
        } else if (e instanceof BindException) {
            result.setMessage(e.getMessage());
            result.setCode(ExpoResultCode.CommonResultEnum.BAD_REQUEST.getCode());
        } else if (e instanceof MethodArgumentNotValidException) {
            result.setMessage(((MethodArgumentNotValidException) e).getBindingResult().getFieldError().getDefaultMessage());
            result.setCode(ExpoResultCode.CommonResultEnum.BAD_REQUEST.getCode());
        } else {
            result.setMessage(ExpoResultCode.CommonResultEnum.SYSTEM_EXCEPTION.getMessage());
            result.setCode(ExpoResultCode.CommonResultEnum.SYSTEM_EXCEPTION.getCode());
        }
        // 其余异常简单返回为服务器异常
        LogUtils.error(log, RandomUtils.getTraceId(), "global exception", e);
        return result;
    }
}
