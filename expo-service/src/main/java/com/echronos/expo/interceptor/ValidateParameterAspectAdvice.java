package com.echronos.expo.interceptor;

import com.echronos.commons.enums.CommonResultCode;
import com.echronos.commons.exception.ParamsValidateException;
import com.echronos.commons.utils.FastJsonUtils;
import com.echronos.commons.utils.ParamCheckSpringUtils;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.Ordered;
import org.springframework.stereotype.Component;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.groups.ConvertGroup;
import javax.validation.groups.Default;
import java.lang.annotation.Annotation;
import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.List;


/**
 * 参数拦截
 */
@Aspect
@Component
@Slf4j
public class ValidateParameterAspectAdvice implements Ordered {

    @Autowired
    private ParamCheckSpringUtils paramCheckSpringUtils;

    /**
     * (non-Javadoc)
     *
     * @see Ordered#getOrder()
     */
    @Override
    public int getOrder() {
        return 1000;
    }

    @Before("execution(* com.echronos.expo.controller.*.*(..)) || execution(* com.echronos.expo.api.feign.*.*(..))")
    public void before(JoinPoint pjd) {
        Object[] args = pjd.getArgs();
        if (args.length > 0) {
            Object oneParam = args[0];
            if (!(oneParam instanceof HttpServletRequest) && !(oneParam instanceof HttpServletResponse)) {
                MethodSignature signature = (MethodSignature) pjd.getSignature();
                Method method = signature.getMethod();
                Annotation[][] parameterAnnotations = method.getParameterAnnotations();
                List<Class<?>> list = new ArrayList<>();
                for (Annotation[] annotations : parameterAnnotations) {
                    for (Annotation annotation : annotations) {
                        if (annotation instanceof ConvertGroup) {
                            list.add(Default.class);
                            ConvertGroup convertGroup = (ConvertGroup) annotation;
                            Class<?> curClass = convertGroup.to();
                            list.add(curClass);
                        } else if (annotation instanceof ConvertGroup.List) {
                            ConvertGroup.List convertGroupList = (ConvertGroup.List) annotation;
                            ConvertGroup[] groups = convertGroupList.value();
                            for (ConvertGroup convertGroup : groups) {
                                Class<?> curClass = convertGroup.to();
                                list.add(curClass);
                            }
                        }
                    }
                }
                Class<?> targetClass = method.getDeclaringClass();
                log.info("{}方法,入参为:{}", targetClass.getName() + "#" + method.getName(), FastJsonUtils.toJSONNoFeatures(oneParam));
                //异常处理
                String check = null;
                if (list.size() > 1) {
                    Class[] arrClass = list.toArray(new Class[list.size()]);
                    check = paramCheckSpringUtils.checkParam(oneParam, arrClass);
                } else {
                    check = paramCheckSpringUtils.checkParam(oneParam);
                }
                if (check != null) {
                    log.error("finance fail,method={},msg={}", pjd.getSignature().getName(), check);
                    throw new ParamsValidateException(CommonResultCode.CommonResultEnum.BAD_REQUEST.getCode(), check);
                }
            }

        }
    }
}
