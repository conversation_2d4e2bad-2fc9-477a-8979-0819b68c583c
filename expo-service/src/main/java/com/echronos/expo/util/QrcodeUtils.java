package com.echronos.expo.util;

import cn.hutool.extra.qrcode.QrCodeUtil;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.Base64;

/**
 * 二维码操作
 *
 * <AUTHOR>
 * @Date 2025/5/17 14:33
 * @ClassName QrcodeUtils
 */
public class QrcodeUtils {
    /**
     * 生成普通二维码
     *
     * @param text   生成文本
     * @param width  宽
     * @param height 高
     * @param format 图片格式
     * @return
     */
    public static String generateQRCodeBase64(String text, int width, int height, String format) {
        try {
            BufferedImage generate = QrCodeUtil.generate(text, width, height);
            ByteArrayOutputStream baos = new ByteArrayOutputStream();
            // 使用ImageIO将图片写入字节流中
            ImageIO.write(generate, format, baos);
            // 获取图片的字节数据
            byte[] imageBytes = baos.toByteArray();
            // 将字节数据转换为Base64编码的字符串
            return Base64.getEncoder().encodeToString(imageBytes);
        } catch (IOException e) {
            e.printStackTrace();
            return null;
        }
    }
}
