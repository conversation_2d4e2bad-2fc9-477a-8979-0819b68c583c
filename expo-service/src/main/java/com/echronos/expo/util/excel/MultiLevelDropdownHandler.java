package com.echronos.expo.util.excel;

import com.alibaba.excel.write.handler.SheetWriteHandler;
import com.alibaba.excel.write.metadata.holder.WriteSheetHolder;
import com.alibaba.excel.write.metadata.holder.WriteWorkbookHolder;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddressList;

import java.util.Arrays;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2025/5/29 15:16
 * @ClassName MultiLevelDropdownHandler
 */
public class MultiLevelDropdownHandler implements SheetWriteHandler {
    @Override
    public void afterSheetCreate(WriteWorkbookHolder writeWorkbookHolder, WriteSheetHolder writeSheetHolder) {
        Workbook workbook = writeWorkbookHolder.getWorkbook();
        Sheet mainSheet = writeSheetHolder.getSheet();

        // 创建数据源Sheet
        Sheet dataSheet = workbook.createSheet("区域数据");
        workbook.setSheetHidden(workbook.getSheetIndex(dataSheet), true); // 隐藏数据源

        // 定义区域数据
        Map<String, Map<String, List<String>>> regionData = new LinkedHashMap<>();

        // 添加省份数据
        Map<String, List<String>> zhejiang = new LinkedHashMap<>();
        zhejiang.put("杭州市", Arrays.asList("西湖区", "上城区", "滨江区"));
        zhejiang.put("宁波市", Arrays.asList("海曙区", "江北区", "鄞州区"));
        regionData.put("浙江省", zhejiang);

        Map<String, List<String>> jiangsu = new LinkedHashMap<>();
        jiangsu.put("南京市", Arrays.asList("玄武区", "鼓楼区", "秦淮区"));
        jiangsu.put("苏州市", Arrays.asList("姑苏区", "虎丘区", "吴中区"));
        regionData.put("江苏省", jiangsu);

        // 写入数据源
        int rowNum = 0;
        for (Map.Entry<String, Map<String, List<String>>> provinceEntry : regionData.entrySet()) {
            // 省份行
            Row provinceRow = dataSheet.createRow(rowNum++);
            provinceRow.createCell(0).setCellValue(provinceEntry.getKey());

            for (Map.Entry<String, List<String>> cityEntry : provinceEntry.getValue().entrySet()) {
                // 城市行
                Row cityRow = dataSheet.createRow(rowNum++);
                cityRow.createCell(1).setCellValue(cityEntry.getKey());

                // 区域行
                for (String district : cityEntry.getValue()) {
                    Row districtRow = dataSheet.createRow(rowNum++);
                    districtRow.createCell(2).setCellValue(district);
                }
            }
        }

        // 定义名称
        createNames(workbook, regionData);

        // 设置多级下拉验证
        setupDropdownValidation(mainSheet, regionData);
    }

    private void createNames(Workbook workbook, Map<String, Map<String, List<String>>> regionData) {
        // 创建省份名称
        Name provinceName = workbook.createName();
        provinceName.setNameName("Provinces");
        provinceName.setRefersToFormula("区域数据!$A$1:$A$" + regionData.size());

        // 为每个省份创建城市名称
        int rowStart = 1;
        for (String province : regionData.keySet()) {
            int cityCount = regionData.get(province).size();

            Name cityName = workbook.createName();
            cityName.setNameName(province + "_Cities");
            cityName.setRefersToFormula("区域数据!$B$" + rowStart + ":$B$" + (rowStart + cityCount - 1));

            rowStart += cityCount;
        }

        // 为每个城市创建区域名称
        rowStart = 1;
        for (Map<String, List<String>> cities : regionData.values()) {
            for (String city : cities.keySet()) {
                int districtCount = cities.get(city).size();

                Name districtName = workbook.createName();
                districtName.setNameName(city + "_Districts");
                districtName.setRefersToFormula("区域数据!$C$" + rowStart + ":$C$" + (rowStart + districtCount - 1));

                rowStart += districtCount;
            }
        }
    }

    private void setupDropdownValidation(Sheet sheet, Map<String, Map<String, List<String>>> regionData) {
        DataValidationHelper helper = sheet.getDataValidationHelper();

        // 省份下拉
        DataValidationConstraint provinceConstraint = helper.createFormulaListConstraint("Provinces");
        CellRangeAddressList provinceRange = new CellRangeAddressList(1, 1000, 2, 2); // C列
        DataValidation provinceValidation = helper.createValidation(provinceConstraint, provinceRange);
        sheet.addValidationData(provinceValidation);

        // 城市下拉（依赖省份）
        DataValidationConstraint cityConstraint = helper.createFormulaListConstraint(
                "INDIRECT(SUBSTITUTE($C2,\" \",\"_\") & \"_Cities\")"
        );
        CellRangeAddressList cityRange = new CellRangeAddressList(1, 1000, 3, 3); // D列
        DataValidation cityValidation = helper.createValidation(cityConstraint, cityRange);
        sheet.addValidationData(cityValidation);

        // 区域下拉（依赖城市）
        DataValidationConstraint districtConstraint = helper.createFormulaListConstraint(
                "INDIRECT(SUBSTITUTE($D2,\" \",\"_\") & \"_Districts\")"
        );
        CellRangeAddressList districtRange = new CellRangeAddressList(1, 1000, 4, 4); // E列
        DataValidation districtValidation = helper.createValidation(districtConstraint, districtRange);
        sheet.addValidationData(districtValidation);
    }
}
