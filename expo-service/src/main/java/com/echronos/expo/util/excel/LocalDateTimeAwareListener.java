package com.echronos.expo.util.excel;

import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.excel.exception.ExcelDataConvertException;
import com.alibaba.excel.metadata.Cell;
import com.echronos.expo.util.AudienceTemplateFiled;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2025/6/21 11:42
 * @ClassName LocalDateTimeAwareListener
 */
public class LocalDateTimeAwareListener extends AnalysisEventListener<AudienceTemplateFiled> {
    private final List<AudienceTemplateFiled> successList = new ArrayList<>();

    private final Map<Integer, String> errorMap = new HashMap<>();

    @Override
    public void onException(Exception exception, AnalysisContext context) throws Exception {
        if (exception instanceof ExcelDataConvertException) {
            ExcelDataConvertException ex = (ExcelDataConvertException) exception;
            // 获取单元格位置信息
            int rowIndex = ex.getRowIndex() + 1;
            // 获取原始值
            String rawValue = "";
            Map<Integer, Cell> cellMap = context.readRowHolder().getCellMap();
            try {
                rawValue = ex.getCellData().getStringValue();
            } catch (Exception e) {
                rawValue = "无法获取原始值";
            }
            // 获取异常原因
            String cause = exception.getCause() != null ?
                    exception.getCause().getMessage() : exception.getMessage();
            //构建错误信息
            String errorMsg = String.format(
                    "转换失败: 值[%s], 原因: %s", rawValue, cause
            );
            errorMap.put(rowIndex, errorMsg);
        }
    }

    @Override
    public void invoke(AudienceTemplateFiled data, AnalysisContext context) {
        successList.add(data);
    }

    @Override
    public void doAfterAllAnalysed(AnalysisContext context) {
        System.out.println("导入完成: " + successList.size() + " 条成功, " +
                errorMap.size() + " 条失败");
    }

    public List<AudienceTemplateFiled> getSuccessList() {
        return successList;
    }

    public Map<Integer, String> getErrorMap() {
        return errorMap;
    }
}
