package com.echronos.expo.util.excel;

import com.alibaba.excel.metadata.Head;
import com.alibaba.excel.write.handler.CellWriteHandler;
import com.alibaba.excel.write.metadata.holder.WriteSheetHolder;
import com.alibaba.excel.write.metadata.holder.WriteTableHolder;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.Workbook;

/**
 * 样式居左、右、中
 *
 * <AUTHOR>
 * @Date 2025/6/4 11:44
 * @ClassName AlignmentStyleHandler
 */

public class AlignmentStyleHandler implements CellWriteHandler {
    /**
     * 列号
     */
    private Integer colNum;
    /**
     * 数据样式
     */
    private HorizontalAlignment dataAlignment;

    public AlignmentStyleHandler(Integer colNum, HorizontalAlignment dataAlignment) {
        this.colNum = colNum;
        this.dataAlignment = dataAlignment;
    }

    @Override
    public void afterCellCreate(WriteSheetHolder writeSheetHolder, WriteTableHolder writeTableHolder, Cell cell,
                                Head head, Integer relativeRowIndex, Boolean isHead) {
        Workbook workbook = writeSheetHolder.getSheet().getWorkbook();

        // 获取或创建单元格样式
        CellStyle cellStyle = cell.getCellStyle();
        if (cellStyle == null) {
            cellStyle = workbook.createCellStyle();
        } else {
            cellStyle = workbook.createCellStyle();
            cellStyle.cloneStyleFrom(cell.getCellStyle());
        }
        if(colNum == cell.getColumnIndex()){
            cellStyle.setAlignment(dataAlignment);
            cell.setCellStyle(cellStyle);
        }
    }
}
