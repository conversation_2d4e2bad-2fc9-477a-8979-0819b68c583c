package com.echronos.expo.util;

import java.time.ZoneId;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2025/5/29 17:06
 * @ClassName CityTimezoneFinder
 */
public class CityTimezoneFinder {
    public static final Map<String, ZoneId> CITY_TO_ZONE = new HashMap<>();
    static {
        // 初始化城市-时区映射表（示例数据，需自行补充完整）
        Map<String, String> rawMap = new HashMap<>();
        //{
        //                    "label": "曼谷",
        //                    "pinyin": "Bangkok",
        //                    "value": 1002818
        //                }
        rawMap.put("1002818", "Asia/Bangkok");
        // {
        //                            "label": "吉隆坡",
        //                            "pinyin": "Kuala Lumpur",
        //                            "value": 1001700
        //                        }
        rawMap.put("1001700", "Asia/Kuala_Lumpur");
        // {
        //                    "label": "胡志明市",
        //                    "pinyin": "Ho Chi Minh City",
        //                    "value": 1003582
        //                }
        rawMap.put("1003582", "Asia/Ho_Chi_Minh");
        //{
        //                    "label": "阿拉木图",
        //                    "pinyin": "Almaty",
        //                    "value": 1000949
        //                }
        rawMap.put("1000949", "Asia/Almaty");
        // {
        //                            "label": "珀斯",
        //                            "pinyin": "Perth",
        //                            "value": 1000260
        //                        }
        rawMap.put("1000260", "Australia/Perth");
        //{
        //                    "label": "雅加达",
        //                    "pinyin": "Jakarta Raya",
        //                    "value": 1003489
        //                }
        rawMap.put("1003489", "Asia/Jakarta");
        //{
        //                    "label": "利雅得",
        //                    "pinyin": "Riyad",
        //                    "value": 1002660
        //                }
        rawMap.put("1002660", "Asia/Riyadh");
        rawMap.put("Hongkong", "Hongkong");
        rawMap.forEach((city, zoneId) ->
                CITY_TO_ZONE.put(city.toLowerCase(), ZoneId.of(zoneId))
        );
    }
}
