package com.echronos.expo.util.excel;

import com.alibaba.excel.annotation.format.DateTimeFormat;
import com.alibaba.excel.converters.Converter;
import com.alibaba.excel.enums.CellDataTypeEnum;
import com.alibaba.excel.metadata.GlobalConfiguration;
import com.alibaba.excel.metadata.data.ReadCellData;
import com.alibaba.excel.metadata.property.ExcelContentProperty;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025/6/21 12:01
 * @ClassName LocalDateTimeConverter
 */
public class LocalDateTimeConverter implements Converter<LocalDateTime> {

    // 支持的时间格式列表（按优先级排序）
    private static final List<DateTimeFormatter> FORMATTERS = Arrays.asList(
            DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"),
            DateTimeFormatter.ofPattern("yyyy/MM/dd HH:mm:ss"),
            DateTimeFormatter.ofPattern("yyyy年MM月dd日 HH时mm分ss秒"),
            DateTimeFormatter.ofPattern("yyyy-MM-dd"),
            DateTimeFormatter.ofPattern("yyyy/MM/dd"),
            DateTimeFormatter.ofPattern("yyyyMMddHHmmss"),
            DateTimeFormatter.ofPattern("dd-MM-yyyy HH:mm:ss"),
            DateTimeFormatter.ofPattern("MM/dd/yyyy HH:mm:ss")
    );
    // 默认时间（当日期存在但时间不存在时使用）
    private static final String DEFAULT_TIME = "00:00:00";

    @Override
    public Class<LocalDateTime> supportJavaTypeKey() {
        return LocalDateTime.class;
    }

    @Override
    public CellDataTypeEnum supportExcelTypeKey() {
        return CellDataTypeEnum.STRING;
    }

    @Override
    public LocalDateTime convertToJavaData(ReadCellData<?> cellData, ExcelContentProperty contentProperty,
                                           GlobalConfiguration globalConfiguration) {
        // 处理空值
        if (cellData == null || StringUtils.isBlank(cellData.getStringValue())) {
            return handleEmptyValue(contentProperty);
        }

        String rawValue = cellData.getStringValue().trim();

        try {
            // 尝试直接解析为数字（Excel日期值）
            if (rawValue.matches("^\\d+\\.?\\d*$")) {
                double excelDateValue = Double.parseDouble(rawValue);
                return convertExcelDate(excelDateValue);
            }
            // 尝试各种格式解析
            for (DateTimeFormatter formatter : FORMATTERS) {
                try {
                    // 处理只有日期没有时间的情况
                    if (formatter.toString().contains("HH") && !rawValue.contains(":")) {
                        rawValue += " " + DEFAULT_TIME;
                    }

                    return LocalDateTime.parse(rawValue, formatter);
                } catch (DateTimeParseException ignored) {
                    // 继续尝试下一个格式
                }
            }

            // 尝试处理带时区的时间（去除时区部分）
            if (rawValue.contains("+")) {
                String withoutTimezone = rawValue.substring(0, rawValue.indexOf('+')).trim();
                return parseDateTime(withoutTimezone);
            }
        } catch (Exception e) {
            throw new DateTimeParseException("无法解析日期时间: " + rawValue, rawValue, 0, e);
        }

        throw new DateTimeParseException("没有匹配的日期时间格式: " + rawValue, rawValue, 0);
    }

    private LocalDateTime parseDateTime(String value) {
        for (DateTimeFormatter formatter : FORMATTERS) {
            try {
                return LocalDateTime.parse(value, formatter);
            } catch (DateTimeParseException ignored) {
            }
        }
        throw new DateTimeParseException("解析失败: " + value, value, 0);
    }

    // 处理Excel日期数值（OLE自动化日期）
    private LocalDateTime convertExcelDate(double excelDate) {
        // Excel日期从1899-12-30开始
        long days = (long) excelDate;
        double fractionalDay = excelDate - days;

        // 计算日期部分
        LocalDateTime base = LocalDateTime.of(1899, 12, 30, 0, 0);
        LocalDateTime datePart = base.plusDays(days);

        // 计算时间部分（小数部分代表一天中的比例）
        int secondsInDay = (int) (fractionalDay * 24 * 60 * 60);
        int hours = secondsInDay / 3600;
        int minutes = (secondsInDay % 3600) / 60;
        int seconds = secondsInDay % 60;

        return datePart
                .plusHours(hours)
                .plusMinutes(minutes)
                .plusSeconds(seconds);
    }

    // 处理空值策略
    private LocalDateTime handleEmptyValue(ExcelContentProperty property) {
        // 检查是否有默认值注解
        if (property != null && property.getField() != null) {
            DateTimeFormat formatAnnotation = property.getField().getAnnotation(DateTimeFormat.class);
            if (formatAnnotation != null && !"".equals(formatAnnotation.value())) {
                return LocalDateTime.parse(formatAnnotation.value(),
                        DateTimeFormatter.ofPattern(formatAnnotation.value()));
            }
        }

        // 返回当前时间或null（根据业务需求）
        // return LocalDateTime.now();
        return null;
    }
}
