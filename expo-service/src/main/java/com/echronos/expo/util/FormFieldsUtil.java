package com.echronos.expo.util;

import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.beust.jcommander.internal.Lists;
import com.echronos.expo.annotation.FormField;
import com.echronos.expo.dto.AudienceFieldDTO;
import com.echronos.expo.dto.FormFieldDTO;
import lombok.experimental.UtilityClass;
import lombok.extern.slf4j.Slf4j;

import java.lang.reflect.Field;
import java.util.List;

/**
 * 字段操作
 *
 * <AUTHOR>
 * @date 2025-5-14 20:00:55
 */
@Slf4j
@UtilityClass
public class FormFieldsUtil {
    public List<FormFieldDTO> getAudienceField() {
        List<FormFieldDTO> formFieldDTOS = convertToFields(AudienceFieldDTO.class);
        return formFieldDTOS;
    }


    public <T> List<FormFieldDTO> convertToFields(Class<T> clazz) {
        Field[] fields = clazz.getDeclaredFields();
        List<FormFieldDTO> approvalFields = Lists.newArrayList();
        for (Field field : fields) {
            FormField fieldAnnotation = field.getAnnotation(FormField.class);
            if (fieldAnnotation == null) {
                continue;
            }
            FormFieldDTO fieldDTO = new FormFieldDTO();
            fieldDTO.setColName(field.getName());
            fieldDTO.setGroup(fieldAnnotation.group());
            fieldDTO.setLabel(fieldAnnotation.label());
            fieldDTO.setSelected(fieldAnnotation.selected());
            fieldDTO.setRequired(fieldAnnotation.required());
            fieldDTO.setType(fieldAnnotation.type());
            approvalFields.add(fieldDTO);
        }
        return approvalFields;
    }

    /**
     * 表单字段
     *
     * @param clazz
     * @param <T>
     * @return
     */
    public <T> List<String> fieldList(Class<T> clazz) {
        Field[] fields = clazz.getDeclaredFields();
        List<String> approvalFields = Lists.newArrayList();
        for (Field field : fields) {
            FormField fieldAnnotation = field.getAnnotation(FormField.class);
            if (fieldAnnotation == null) {
                continue;
            }
            approvalFields.add(field.getName());
        }
        return approvalFields;
    }
    public <T> List<String> fieldNoList(Class<T> clazz) {
        Field[] fields = clazz.getDeclaredFields();
        List<String> approvalFields = Lists.newArrayList();
        for (Field field : fields) {
            approvalFields.add(field.getName());
        }
        return approvalFields;
    }
    public String getFieldValue(Object fieldEntity, String fieldName) {
        if (fieldEntity == null) {
            return null;
        }
        try {
            Field field = fieldEntity.getClass().getDeclaredField(fieldName);
            field.setAccessible(true);
            if (ObjectUtils.isNull(field.get(fieldEntity))) {
                return null;
            }
            return (String) field.get(fieldEntity);
        } catch (Exception ex) {
            log.error("FormFieldsUtil getFieldValue 获取属性异常", ex);
        }
        return null;
    }

}
