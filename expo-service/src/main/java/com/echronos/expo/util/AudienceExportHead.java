package com.echronos.expo.util;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.echronos.expo.util.excel.YesOrNoConverter;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 观众导入模板
 *
 * <AUTHOR>
 * @Date 2025/5/19 14:13
 * @ClassName AudienceTemplateFiled
 */
@Data
public class AudienceExportHead {
    @ExcelIgnore
    private Long id;
    /**
     * 观众名称
     */
    @ExcelProperty(value = "*Name")
    @ColumnWidth(value = 20)
    private String name;
    /**
     * 手机号
     */
    @ExcelProperty(value = "*Mobile")
    @ColumnWidth(value = 20)
    private String phone;
    /**
     * 电子邮箱
     */
    @ExcelProperty(value = "*E-mail")
    @ColumnWidth(value = 30)
    private String email;

    /**
     * 渠道
     */
    @ExcelProperty(value = "Channel")
    private String channelName;
    /**
     * 公司名称
     */
    @ExcelProperty(value = "Company")
    @ColumnWidth(value = 20)
    private String companyName;
    /**
     * 公司网站
     */
    @ExcelProperty(value = "Company Website")
    @ColumnWidth(value = 30)
    private String companyWebsite;
    /**
     * 贵公司的主要行业是什么？
     */
    @ExcelProperty(value = "What is your company's main industry?")
    @ColumnWidth(value = 40)
    private String industryCode;

    /**
     * 您想注册成为专业买家吗？
     */
    @ExcelProperty(value = "Would you like to register as a professional buyer?", converter = YesOrNoConverter.class)
    @ColumnWidth(value = 40)
    private String asBuyer;

    /**
     * 您在展会上扮演什么角色？
     */
    @ExcelProperty(value = "What is your role at the exhibition?")
    @ColumnWidth(value = 40)
    private String expoRole;
    /**
     * 您最感兴趣的是哪个展区？（可多选）
     */
    @ExcelProperty(value = "Which exhibition area interests you most? (Multiple choice)")
    @ColumnWidth(value = 40)
    private String interests;

    /**
     * 您想参加哪些活动？（可多选）
     */
    @ExcelProperty(value = "Which activities would you like to participate in? (Multiple choice)")
    @ColumnWidth(value = 40)
    private String participate;


    /**
     * 您有特定的采购或合作需求吗？
     */
    @ExcelProperty(value = "Do you have specific procurement or cooperation needs?")
    @ColumnWidth(value = 40)
    private String procurementNeeds;


    /**
     * 您的公司规模是多少？
     */
    @ExcelProperty(value = "What is your company size?")
    @ColumnWidth(value = 40)
    private String companySize;

    /**
     * 您的职位是什么？
     */
    @ExcelProperty(value = "What is your position?")
    @ColumnWidth(value = 20)
    private String position;

    /**
     * 您在寻找什么产品或解决方案？（可多选）
     */
    @ExcelProperty(value = "What products or solutions are you looking for? (Multiple choice)")
    @ColumnWidth(value = 40)
    private String productService;
    /**
     * 您的采购时间表是怎样的？
     */
    @ExcelProperty(value = "What is your procurement timeline?")
    @ColumnWidth(value = 40)
    private String procurementTimeline;
    /**
     * 您的项目预算范围是多少？（美元）
     */
    @ExcelProperty(value = "What is your project budget range? (USD)")
    @ColumnWidth(value = 40)
    private String budget;
    /**
     * 您在采购决策中扮演什么角色？
     */
    @ExcelProperty(value = "What is your role in procurement decisions?")
    @ColumnWidth(value = 40)
    private String procurementRole;

    /**
     * 您对供应商的主要要求是什么？（可多选）？
     */
    @ExcelProperty(value = "What are your main requirements for suppliers? (Multiple choice)")
    @ColumnWidth(value = 40)
    private String requirements;
    /**
     * 您首选的合作模式是什么？
     */
    @ExcelProperty(value = "What is your preferred cooperation model?")
    @ColumnWidth(value = 40)
    private String cooperationModel;
    /**
     * 请简要描述您的具体需求或项目情况
     */
    @ExcelProperty(value = "Please briefly describe your specific needs or project situation")
    @ColumnWidth(value = 40)
    private String specificNeeds;
    /**
     * 您希望我们如何联系您？
     */
    @ExcelProperty(value = "How would you prefer us to contact you?")
    @ColumnWidth(value = 40)
    private String contactType;
    /**
     * 您在展会期间有什么计划？（可多选）
     */
    @ExcelProperty(value = "What are your plans during the exhibition? (Multiple choice)")
    @ColumnWidth(value = 40)
    private String expoPlans;
    /**
     * ip
     */
    @ExcelProperty(value = "ip")
    @ColumnWidth(value = 20)
    private String ip;
    /**
     * 提交时间
     */
    @ExcelProperty(value = "CreateTime")
    @ColumnWidth(value = 20)
    private LocalDateTime createTime;
    /**
     * 是否签到
     */
    @ExcelProperty(value = "Check in or not", converter = YesOrNoConverter.class)
    @ColumnWidth(value = 20)
    private String isSign;
    /**
     * 最近签到时间
     */
    @ExcelProperty(value = "Recent check-in time")
    @ColumnWidth(value = 20)
    private LocalDateTime lastSignTime;
    /**
     * 签到次数
     */
    @ExcelProperty(value = "Check in frequency")
    @ColumnWidth(value = 20)
    private Integer signCount;
}
