package com.echronos.expo.util.excel;

import com.alibaba.excel.converters.Converter;
import com.alibaba.excel.metadata.GlobalConfiguration;
import com.alibaba.excel.metadata.data.ReadCellData;
import com.alibaba.excel.metadata.data.WriteCellData;
import com.alibaba.excel.metadata.property.ExcelContentProperty;
import org.apache.commons.lang3.StringUtils;

import java.util.HashMap;
import java.util.Map;

/**
 * 是否选择器
 *
 * <AUTHOR>
 * @Date 2025/5/26 14:58
 * @ClassName YesOrNoConverter
 */
public class YesOrNoConverter implements Converter<String> {
    private static final Map<String, String> EN_MAP = new HashMap<>();

    static {
        EN_MAP.put("NO", "0");
        EN_MAP.put("YES", "1");
    }

    @Override
    public String convertToJavaData(ReadCellData<?> cellData, ExcelContentProperty contentProperty, GlobalConfiguration globalConfiguration) throws Exception {
        String orDefault = EN_MAP.get(cellData.getStringValue().toUpperCase());
        if (StringUtils.isEmpty(orDefault)) {
            orDefault = EN_MAP.getOrDefault(cellData.getStringValue(), "0");
        }
        return orDefault;
    }


    @Override
    public WriteCellData<?> convertToExcelData(String value, ExcelContentProperty contentProperty, GlobalConfiguration globalConfiguration) throws Exception {
        String displayValue = EN_MAP.entrySet().stream().filter(r -> r.getValue().equalsIgnoreCase(value))
                .findFirst()
                .map(Map.Entry::getKey)
                .orElse(value);
        return new WriteCellData<>(displayValue);
    }
}
