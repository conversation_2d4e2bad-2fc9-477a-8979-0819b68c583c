package com.echronos.expo.util.excel;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.exception.ExcelAnalysisException;
import com.echronos.expo.enums.TemplateDownLoadEnums;
import com.echronos.expo.util.AudienceTemplateFiled;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025/5/30 15:10
 * @ClassName ExcelReaderExample
 */
public class ExcelReaderExample {
    public static void main(String[] args) {
        String filePath = "D:\\home/观众导入-20250530_072443.xlsx";

        // 1. 调试Excel结构
        ExcelReaderDebugger.printExcelStructure(filePath);

        // 2. 验证映射关系
        ExcelReaderDebugger.validateMapping(filePath, AudienceTemplateFiled.class);

        // 3. 读取数据
        readExcelData(filePath);
    }
    private static void readExcelData(String filePath) {
        try {
            List<AudienceTemplateFiled> users = EasyExcel.read(filePath)
                    .head(AudienceTemplateFiled.class)
                    .sheet("观众导入")
                    .doReadSync();

            if (users.isEmpty()) {
                System.err.println("警告: 成功读取但数据为空!");
            } else {
                System.out.println("成功读取数据: " + users.size() + " 条记录");
                users.forEach(System.out::println);
            }
        } catch (Exception e) {
            System.err.println("读取失败: " + e.getMessage());
            e.printStackTrace();

            // 特殊异常处理
            if (e.getCause() instanceof ExcelAnalysisException) {
                handleExcelAnalysisException((ExcelAnalysisException) e.getCause());
            }
        }
    }

    private static void handleExcelAnalysisException(ExcelAnalysisException ex) {
        System.err.println("=== Excel解析错误详情 ===");
        ex.printStackTrace();
    }
}
