package com.echronos.expo.util;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;

import java.util.List;
import java.util.Map;

/**
 * 断言判断工具类
 * <AUTHOR>
 * @create 2022-04-25-17:06
 */
public class AssertUtil {

    /**
     * 校验是否为空
     * @param object
     * @return
     */
    public static boolean checkNull(Object object){
        if (object == null) {
            return true;
        }else if (object instanceof String && StrUtil.isBlank((String) object)) {
            return true;
        }else if (object instanceof Map && MapUtil.isEmpty((Map<?, ?>) object)) {
            return true;
        }else if (object instanceof List && CollectionUtil.isEmpty((List<?>) object)) {
            return true;
        }
        return false;
    }

}
