package com.echronos.expo.util.excel;

import com.alibaba.excel.write.handler.SheetWriteHandler;
import com.alibaba.excel.write.metadata.holder.WriteSheetHolder;
import com.alibaba.excel.write.metadata.holder.WriteWorkbookHolder;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddressList;

import java.util.Map;
import java.util.Set;

/**
 * 单元格下来框处理
 * <AUTHOR>
 * @create 2022-03-08-16:39
 */
public class CellPullDownWriteHandler implements SheetWriteHandler {

    /**
     * 下拉框的值
      */
    private Map<Integer,String[]> map = null;
    /**
     * 下拉框是否添加约束校验
     */
    private boolean pullDownValidationFlag = false;

    public CellPullDownWriteHandler(Map<Integer,String[]> map, boolean pullDownValidationFlag){
        this.map = map;
        this.pullDownValidationFlag = pullDownValidationFlag;
    }

    @Override
    public void beforeSheetCreate(WriteWorkbookHolder writeWorkbookHolder, WriteSheetHolder writeSheetHolder) {
    }

    @Override
    public void afterSheetCreate(WriteWorkbookHolder writeWorkbookHolder, WriteSheetHolder writeSheetHolder) {
        // 这里可以对cell进行任何操作
        Sheet sheet = writeSheetHolder.getSheet();
        DataValidationHelper helper = sheet.getDataValidationHelper();
        // k 为存在下拉数据集的单元格下表 v为下拉数据集
        Set<Map.Entry<Integer, String[]>> entries = map.entrySet();

        for(Map.Entry<Integer, String[]> val : entries) {
            // 设置下拉单元格的首行 末行 首列 末列
            CellRangeAddressList rangeList = new CellRangeAddressList(1, 65536, val.getKey() - 1, val.getKey() - 1);
            // 将刚才设置的sheet引用到你的下拉列表中
            DataValidationConstraint constraint = null;
            // 如果下拉值总数大于100，则使用一个新sheet存储，避免生成的导入模板下拉值获取不到
            if (val.getValue().length > 0) {
                //定义sheet的名称
                //1.创建一个隐藏的sheet 名称为 hidden + k
                String sheetName = "hidden" + val.getKey();
                Workbook workbook = writeWorkbookHolder.getWorkbook();
                Sheet hiddenSheet = workbook.createSheet(sheetName);
                for (int i = 0, length = val.getValue().length; i < length; i++) {
                    // 开始的行数i，列数k
                    hiddenSheet.createRow(i).createCell(val.getKey()).setCellValue(val.getValue()[i]);
                }
                Name category1Name = workbook.createName();
                category1Name.setNameName(sheetName);
                String excelLine = getExcelLine(val.getKey());
                // =hidden!$H:$1:$H$50  sheet为hidden的 H1列开始H50行数据获取下拉数组
                String refers = "=" + sheetName + "!$" + excelLine + "$1:$" + excelLine + "$" + (val.getValue().length + 1);
                // 将刚才设置的sheet引用到你的下拉列表中
                constraint = helper.createFormulaListConstraint(refers);
                // 设置存储下拉列值得sheet为隐藏

                int hiddenIndex = workbook.getSheetIndex(sheetName);
                if (!workbook.isSheetHidden(hiddenIndex)) {
                    workbook.setSheetHidden(hiddenIndex, true);
                }
            }
            // 根据传进来的值判断是否需要添加下拉框的校验
            if(pullDownValidationFlag){
                if(null != constraint){
                    DataValidation validation = helper.createValidation(constraint, rangeList);
                    // 设置约束，阻止输入非下拉选项的值
                    validation.setErrorStyle(DataValidation.ErrorStyle.STOP);
                    validation.setShowErrorBox(true);
                    validation.setSuppressDropDownArrow(true);
                    validation.createErrorBox("提示","此值与单元格定义格式不一致");
                    writeSheetHolder.getSheet().addValidationData(validation);
                }
            }
        }
    }


        /**
         * 返回excel列标A-Z-AA-ZZ
         *
         * @param num 列数
         * @return java.lang.String
         */
        private String getExcelLine(int num) {
            String line = "";
            int first = num / 26;
            int second = num % 26;
            if (first > 0) {
                line = (char) ('A' + first - 1) + "";
            }
            line += (char) ('A' + second) + "";
            return line;
        }
}
