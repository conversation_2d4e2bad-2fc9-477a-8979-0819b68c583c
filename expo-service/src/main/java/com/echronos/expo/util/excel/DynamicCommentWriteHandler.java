package com.echronos.expo.util.excel;

import com.alibaba.excel.metadata.Head;
import com.alibaba.excel.write.handler.CellWriteHandler;
import com.alibaba.excel.write.metadata.holder.WriteSheetHolder;
import com.alibaba.excel.write.metadata.holder.WriteTableHolder;
import org.apache.poi.ss.usermodel.*;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025/5/22 9:28
 * @ClassName DynamicCommentWriteHandler
 */
public class DynamicCommentWriteHandler implements CellWriteHandler {
    /**
     * 批注列
     */
    private List<Integer> colnums;
    /**
     * 批注内容
     */
    private String commentStr;

    public DynamicCommentWriteHandler(List<Integer> colnums, String commentStr) {
        this.colnums = colnums;
        this.commentStr = commentStr;
    }

    @Override
    public void afterCellCreate(WriteSheetHolder writeSheetHolder, WriteTableHolder writeTableHolder, Cell cell, Head head, Integer relativeRowIndex, Boolean isHead) {
        if (isHead) {
            Workbook workbook = writeSheetHolder.getSheet().getWorkbook();
            // 创建字体并设置大小
            Font headerFont = workbook.createFont();
            headerFont.setFontHeightInPoints((short) 10); // 字体大小为10磅
            headerFont.setBold(true);                     // 加粗（可选）
            // 创建单元格样式并应用字体
            CellStyle headerStyle = workbook.createCellStyle();
            headerStyle.setFont(headerFont);
            // 将样式应用到当前表头单元格
            cell.setCellStyle(headerStyle);
            // 仅处理表头单元格
            int columnIndex = cell.getColumnIndex();
            if (colnums.contains(columnIndex)) {
                addCommentToCell(cell, commentStr);
            }
        }
    }

    private void addCommentToCell(Cell cell, String commentText) {
        Sheet sheet = cell.getSheet();
        Workbook workbook = sheet.getWorkbook();

        Drawing<?> drawing = sheet.createDrawingPatriarch();
        CreationHelper factory = workbook.getCreationHelper();

        // 设置批注位置和大小
        ClientAnchor anchor = factory.createClientAnchor();
        anchor.setCol1(cell.getColumnIndex());
        anchor.setRow1(cell.getRowIndex());
        anchor.setCol2(cell.getColumnIndex() + 2); // 宽度跨2列
        anchor.setRow2(cell.getRowIndex() + 3);    // 高度跨3行

        // 创建批注
        Comment comment = drawing.createCellComment(anchor);
        RichTextString str = factory.createRichTextString(commentText);
        comment.setString(str);
        comment.setAuthor("system remark");

        cell.setCellComment(comment);
    }
}
