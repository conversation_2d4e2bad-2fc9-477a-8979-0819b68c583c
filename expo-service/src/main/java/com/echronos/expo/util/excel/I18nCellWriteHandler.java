package com.echronos.expo.util.excel;

import com.alibaba.excel.metadata.Head;
import com.alibaba.excel.write.handler.CellWriteHandler;
import com.alibaba.excel.write.metadata.holder.WriteSheetHolder;
import com.alibaba.excel.write.metadata.holder.WriteTableHolder;
import com.echronos.commons.utils.MessageUtil;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.Row;
import org.springframework.context.MessageSource;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

@RequiredArgsConstructor
public class I18nCellWriteHandler implements CellWriteHandler {

    @Resource
    private MessageSource messageSource;




    @Override
    public void beforeCellCreate(WriteSheetHolder writeSheetHolder, WriteTableHolder writeTableHolder, Row row, Head head, Integer columnIndex, Integer relativeRowIndex, Boolean isHead) {
        // 仅处理表头
        if (!isHead || head == null) {
            return;
        }

        // 获取原始表头名称列表
        List<String> originHeadNames = head.getHeadNameList();
        if (CollectionUtils.isEmpty(originHeadNames)) {
            return;
        }
        // 仅在预计算阶段执行国际化逻辑
        if (relativeRowIndex == 0) { // relativeRowIndex == 0 表示预计算阶段
            List<String> i18nHeadNames = originHeadNames.stream()
                    .map(code ->{
                                if (StringUtils.isNotBlank(code)){
                                    try {
                                        // 没有匹配的key，则返回code
                                        return MessageUtil.getMessage(code);
                                    } catch (Exception e) {
                                        return code;
                                    }
                                }
                                return code;
                            }
                            ) // 缓存结果
                    .collect(Collectors.toList());

            // 替换表头名称
            head.setHeadNameList(i18nHeadNames);
        }
    }

}

