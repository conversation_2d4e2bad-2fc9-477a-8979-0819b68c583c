package com.echronos.expo.util.excel;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.alibaba.excel.write.builder.ExcelWriterBuilder;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.echronos.expo.easyexcel.annotation.DropDownField;
import com.echronos.expo.easyexcel.model.ExcelBaseModel;
import lombok.extern.slf4j.Slf4j;

import java.io.*;
import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * EasyExcel工具类
 * <AUTHOR>
 * @create 2022-03-08-16:33
 */
@Slf4j
public class EasyExcelUtil {

    /**
     * 导出模板（公共方法）
     * @param outputStream   响应流
     * @param dropDownValMap 模板中所有下拉选项对应的值（字段-下拉值）
     * @param excelBaseModel  excel实体类
     * @param dataList   需要导出的数据集：如无需导出数据则传null
     * @param groundRowColumns   需要修改背景颜色的单元格
     * @param highlightFonts   每行需要高亮的字体
     * @param ignoreFields   需要忽略的字段
     * @param pullDownValidationFlag 下拉框是否需要添加校验
     */
    public static void exportExcelTemplate(OutputStream outputStream, Map<String,String[]> dropDownValMap,
                                           ExcelBaseModel excelBaseModel, List dataList, Map<Integer,
                                           List<Integer>> groundRowColumns, Map<Integer,List<String>> highlightFonts,
                                           List<String> ignoreFields, boolean pullDownValidationFlag){
        ExcelWriter excelWriter = null;
        try {
            // 获取该类声明的所有字段
            Field[] fields = excelBaseModel.getClass().getDeclaredFields();
            // 响应字段对应的下拉集合
            Map<Integer, String[]> map = new HashMap<>();
            Field field = null;
            // 循环判断哪些字段有下拉数据集，并获取
            List<Integer> ignoreColumns = new ArrayList<>();
            for(int i = 0; i < fields.length; i++){
                field = fields[i];
                // 找出需要忽略列
                if (ObjectUtil.isNotEmpty(ignoreFields)) {
                    for (String ignoreField : ignoreFields) {
                        if (field.getName().equals(ignoreField)) {
                            ignoreColumns.add(i);
                        }
                    }
                }
                // 解析注解信息
                DropDownField dropDownField = field.getAnnotation(DropDownField.class);
                if(null != dropDownField){
                    String[] sources = SolveDropAnnotationUtil.solve(dropDownField,dropDownValMap);
                    if(null != sources && sources.length > 0){
                        int j = i;
                        if (ObjectUtil.isNotEmpty(ignoreColumns)) {
                            j = j - ignoreColumns.size();
                        }
                        map.put(j,sources);
                    }
                }
            }
            // 构建Excel对象
            ExcelWriterBuilder write = EasyExcel.write(outputStream, excelBaseModel.getClass()).excelType(ExcelTypeEnum.XLSX);
            // 忽略字段
            if (ObjectUtil.isNotEmpty(ignoreFields)) {
                write.excludeColumnFiledNames(ignoreFields);
            }
            excelWriter = write
                    .inMemory(true)
                    .build();
            WriteSheet sheet = EasyExcel.writerSheet(0,"")
                    .registerWriteHandler(new I18nCellWriteHandler())
                    .registerWriteHandler(new CellPullDownWriteHandler(map, pullDownValidationFlag))
                    .build();
            excelWriter.write(dataList,sheet);
        }catch (Exception e){
            e.printStackTrace();
            log.error("导出Excel模板异常: error={}",e.getMessage());
        }finally {
            if(null != excelWriter){
                excelWriter.finish();
            }
        }
    }


    /**
     * 下载到本地
     *
     * @param inputStream
     */
    private static void approvalFile(InputStream inputStream) {
        OutputStream os = null;
        try {
            String path = "C:\\Users\\<USER>\\Desktop\\新建文件夹";
            // 2、保存到临时文件
            // 1K的数据缓冲
            byte[] bs = new byte[1024];
            // 读取到的数据长度
            int len;
            // 输出的文件流保存到本地文件
            File tempFile = new File(path);
            if (!tempFile.exists()) {
                tempFile.mkdirs();
            }
            os = new FileOutputStream(tempFile.getPath() + File.separator + "MyFileName.xlsx");
            // 开始读取
            while ((len = inputStream.read(bs)) != -1) {
                os.write(bs, 0, len);
            }
        } catch (IOException e) {
            e.printStackTrace();
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            // 完毕，关闭所有链接
            try {
                os.close();
                inputStream.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
    }

}
