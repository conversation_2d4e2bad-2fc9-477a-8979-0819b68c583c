package com.echronos.expo.util.excel;

import com.alibaba.excel.write.handler.CellWriteHandler;
import com.alibaba.excel.write.handler.SheetWriteHandler;
import com.alibaba.excel.write.metadata.holder.WriteSheetHolder;
import com.alibaba.excel.write.metadata.holder.WriteWorkbookHolder;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddressList;

/**
 * 下拉框选项
 *
 * <AUTHOR>
 * @Date 2025/5/29 14:11
 * @ClassName DropDownWriteHandler
 */
public class DropDownWriteHandler implements CellWriteHandler, SheetWriteHandler {
    /**
     * 第几列
     */
    private Integer colNum;

    /**
     * 第几行开始
     */
    private Integer rowNum;
    /**
     * 选项
     */
    private String[] options;

    private String hiddenSheetName = "hidden_dropdown";

    public DropDownWriteHandler(Integer colNum, Integer rowNum, String[] options) {
        this.colNum = colNum;
        this.rowNum = rowNum;
        this.options = options;
    }

    @Override
    public void beforeSheetCreate(WriteWorkbookHolder writeWorkbookHolder, WriteSheetHolder writeSheetHolder) {
        // 确保整个工作簿只创建一次隐藏Sheet

        Workbook workbook = writeWorkbookHolder.getWorkbook();

        // 检查是否已存在同名Sheet
        Sheet hiddenSheet = workbook.getSheet(hiddenSheetName + colNum);
        if (hiddenSheet == null) {
            // 创建隐藏Sheet
            hiddenSheet = workbook.createSheet(hiddenSheetName + colNum);
        }
        // 写入下拉选项
        for (int i = 0; i < options.length; i++) {
            Row row = hiddenSheet.createRow(i);
            Cell cell = row.createCell(0);
            cell.setCellValue(options[i]);
        }
        // 隐藏该Sheet
        workbook.setSheetHidden(workbook.getSheetIndex(hiddenSheet), true);
    }

    @Override
    public void afterSheetCreate(WriteWorkbookHolder writeWorkbookHolder, WriteSheetHolder writeSheetHolder) {
        Sheet sheet = writeSheetHolder.getSheet();
        setDropdown(sheet, colNum, 1000, options, rowNum);
    }

    private void setDropdown(Sheet sheet, int colIndex, int maxRow, String[] options, int firstRow) {
        DataValidationHelper helper = sheet.getDataValidationHelper();
        CellRangeAddressList addressList = new CellRangeAddressList(firstRow, maxRow, colIndex, colIndex);
        // 构建引用公式：hidden_dropdown!$A$1:$A$n
        String formula = hiddenSheetName + colNum + "!$A$1:$A$" + options.length;
//        DataValidationConstraint constraint = helper.createExplicitListConstraint(options);
        DataValidationConstraint constraint = helper.createFormulaListConstraint(formula);
        DataValidation validation = helper.createValidation(constraint, addressList);
//        // 设置错误提示
//        validation.setErrorStyle(DataValidation.ErrorStyle.STOP);
//        validation.createErrorBox("输入错误", "请从下拉列表中选择有效选项");
//        validation.setShowErrorBox(true);
//        // 设置输入提示
//        validation.createPromptBox("选择提示", "请从下拉列表中选择");
//        validation.setShowPromptBox(true);

        sheet.addValidationData(validation);
    }
}
