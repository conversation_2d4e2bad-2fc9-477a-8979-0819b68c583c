package com.echronos.expo.util.excel;

import com.echronos.expo.easyexcel.annotation.DropDownField;
import org.apache.logging.log4j.util.Strings;
import org.springframework.util.CollectionUtils;

import java.util.Map;
import java.util.Optional;

/**
 * 解析下拉数据集工具
 * <AUTHOR>
 * @create 2022-03-08-16:36
 */
public class SolveDropAnnotationUtil {

    /**
     * 解析下拉数据集方法
     * @param dropDownField 下拉选项注解
     * @param dropDownValMap 下拉选择值
     * @return  下拉值
     */
    public static String[] solve(DropDownField dropDownField, Map<String,String[]> dropDownValMap){
        if(!Optional.ofNullable(dropDownField).isPresent()){
            return null;
        }

        // 获取固定下拉信息
        String[] source = dropDownField.source();
        if(null != source && source.length > 0){
            return source;
        }

        // 获取动态的下拉数据
        String dropDownKeyName = dropDownField.fieldName();
        if(Strings.isNotBlank(dropDownKeyName) && !CollectionUtils.isEmpty(dropDownValMap)){
            if(dropDownValMap.containsKey(dropDownKeyName)){
                String[] values = dropDownValMap.get(dropDownKeyName);
                if(null != values && values.length > 0){
                    return values;
                }
            }
        }
        return null;
    }

}
