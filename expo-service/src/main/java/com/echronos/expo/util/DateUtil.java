package com.echronos.expo.util;

import com.echronos.expo.constants.NumberConstant;
import com.echronos.expo.dto.DateTimeDTO;
import com.echronos.expo.enums.DateEnum;

import java.text.SimpleDateFormat;
import java.time.*;
import java.util.Calendar;
import java.util.Date;

/**
 * <AUTHOR>
 * date2025/8/11 19:32
 */
public class DateUtil {

    // 获取当前月的第一天
    public static LocalDateTime getTimesMonthMorning() {
        return LocalDateTime.now().withDayOfMonth(1).withHour(0)
                .withMinute(0).withSecond(0).withNano(0);
    }

    /**
     * 获取上一个月的开始时间
     *
     * @return
     */
    public static LocalDateTime getFirstDayOfLastMonth() {
        LocalDateTime now = LocalDateTime.now().plusHours(NumberConstant.EIGHT);
        YearMonth lastMonth = YearMonth.from(now).minusMonths(NumberConstant.ONE);
        LocalDate firstDayOfLastMonth = lastMonth.atDay(NumberConstant.ONE);
        return firstDayOfLastMonth.atStartOfDay().minusMonths(NumberConstant.EIGHT);
    }


    /**
     * 获取上一个月的结束时间
     *
     * @return
     */
    public static LocalDateTime getLastDayOfLastMonth() {
        LocalDateTime now = LocalDateTime.now().plusHours(NumberConstant.EIGHT);
        YearMonth lastMonth = YearMonth.from(now).minusMonths(NumberConstant.ONE);
        LocalDate lastDayOfLastMonth = lastMonth.atEndOfMonth();
        return lastDayOfLastMonth.atStartOfDay().minusMonths(NumberConstant.EIGHT);
    }

    /**
     * 获取到今年的开始时间
     *
     * @return
     */
    public static LocalDateTime getFirstThisYear() {
        // 获取当前年份
        int currentYear = Year.now().getValue();
        LocalDate startOfYear = LocalDate.of(currentYear, NumberConstant.ONE, NumberConstant.ONE);
        return startOfYear.atStartOfDay().minusMonths(NumberConstant.EIGHT);
    }

    /**
     * 获取上一年年份
     * @return
     */
    public static Integer getLastYear() {
        SimpleDateFormat formats = new SimpleDateFormat("yyyy");
        Calendar c = Calendar.getInstance();
        //添加8小时
        c.add(Calendar.HOUR_OF_DAY, NumberConstant.EIGHT);
        c.add(Calendar.YEAR,-1);
        Date date =  c.getTime();
        // Date类型转String类型
        String format = formats.format(date);
        return Integer.parseInt(format);
    }

    /**
     * 获取某年第一天日期
     * @param year 年份
     * @return Date
     */
    public static LocalDateTime getYearFirst(int year){
        Calendar calendar = Calendar.getInstance();
        calendar.clear();
        calendar.set(Calendar.YEAR, year);
        Date currYearFirst = calendar.getTime();
        return LocalDateTime.ofInstant(Instant.ofEpochMilli(currYearFirst.getTime()),
                ZoneId.systemDefault());
    }

    /**
     * 获取某年最后一天日期
     * @param year 年份
     * @return Date
     */
    public static LocalDateTime getYearLast(int year){
        Calendar calendar = Calendar.getInstance();
        calendar.clear();
        calendar.set(Calendar.YEAR, year);
        calendar.roll(Calendar.DAY_OF_YEAR, -1);
        Date calendarTime = calendar.getTime();
        return LocalDateTime.ofInstant(Instant.ofEpochMilli(calendarTime.getTime()),
                ZoneId.systemDefault());
    }

    /**
     * 根据时间筛选获取到对应的时间
     *
     * @param dateScreeType
     */
    public static DateTimeDTO getStartAndEndTime(Integer dateScreeType) {
        //开始时间
        LocalDateTime startTime = null;
        //结束时间
        LocalDateTime endTime = null;
        //本年度
        if (DateEnum.ScreeTypeEnum.THIS_YEAR.getCode().equals(dateScreeType)) {
            startTime = getFirstThisYear().minusHours(NumberConstant.EIGHT);
            endTime = LocalDateTime.now();
        }
        if (DateEnum.ScreeTypeEnum.LAST_YEAR.getCode().equals(dateScreeType)) {
            //获取到上一年年份
            Integer lastYear = getLastYear();
            startTime = getYearFirst(lastYear).minusHours(NumberConstant.EIGHT);
            endTime = getYearLast(lastYear).minusHours(NumberConstant.EIGHT);
        }
        if (DateEnum.ScreeTypeEnum.THIS_MONTH.getCode().equals(dateScreeType)) {
            startTime = getTimesMonthMorning().minusHours(NumberConstant.EIGHT);
            endTime = LocalDateTime.now();
        }
        DateTimeDTO dateTimeDTO = new DateTimeDTO();
        dateTimeDTO.setStartTime(startTime);
        dateTimeDTO.setEndTime(endTime);
        return dateTimeDTO;
    }

}
