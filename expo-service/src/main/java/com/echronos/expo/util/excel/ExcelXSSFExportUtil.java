package com.echronos.expo.util.excel;

import javax.servlet.http.HttpServletResponse;
import java.net.URLEncoder;

/**
 * 导出 XSSFSheet 工具类
 *
 * @author: nangua
 * @create: 2020-11-26 14:14
 */
public class ExcelXSSFExportUtil {

    /**
     * 发送响应流方法
     *
     * @param response
     * @param fileName
     */
    public static void setResponseHeader(HttpServletResponse response, String fileName) {
        try {
            fileName = URLEncoder.encode(fileName, "UTF-8");
            response.setContentType("application/vnd.ms-excel");
            response.setHeader("Content-disposition", "attachment;filename=" + fileName + ";" + "filename*=utf-8''" + fileName + ".xlsx");
            response.addHeader("Pargam", "no-cache");
            response.addHeader("Cache-Control", "no-cache");
        } catch (Exception ex) {
            ex.printStackTrace();
        }
    }
}
