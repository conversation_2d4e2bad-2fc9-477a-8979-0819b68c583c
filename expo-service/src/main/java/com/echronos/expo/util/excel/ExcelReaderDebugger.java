package com.echronos.expo.util.excel;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;

import java.lang.reflect.Field;
import java.util.Arrays;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2025/5/30 15:11
 * @ClassName ExcelReaderDebugger
 */
public class ExcelReaderDebugger {
    /**
     * 打印Excel结构信息（调试用）
     * @param filePath Excel文件路径
     */
    public static void printExcelStructure(String filePath) {
        EasyExcel.read(filePath, new AnalysisEventListener<Map<Integer, String>>() {
            @Override
            public void invokeHeadMap(Map<Integer, String> headMap, AnalysisContext context) {
                System.out.println("=== 表头信息 ===");
                headMap.forEach((index, header) ->
                        System.out.println("列" + index + ": " + header));
            }

            @Override
            public void invoke(Map<Integer, String> data, AnalysisContext context) {
                System.out.println("=== 行数据 ===");
                data.forEach((index, value) ->
                        System.out.println("列" + index + ": " + value));
            }

            @Override
            public void doAfterAllAnalysed(AnalysisContext context) {
                System.out.println("=== 文件解析完成 ===");
            }
        }).sheet().doRead();
    }

    /**
     * 验证实体类与Excel映射
     * @param filePath Excel文件路径
     * @param clazz 实体类类型
     */
    public static void validateMapping(String filePath, Class<?> clazz) {
        // 获取实体类注解信息
        Field[] fields = clazz.getDeclaredFields();
        System.out.println("=== 实体类映射配置 ===");
        for (Field field : fields) {
            ExcelProperty property = field.getAnnotation(ExcelProperty.class);
            if (property != null) {
                String[] headers = property.value();
                System.out.printf("字段: %-15s 映射表头: %s%n",
                        field.getName(), Arrays.toString(headers));
            }
        }

        // 读取实际表头
        EasyExcel.read(filePath, new AnalysisEventListener<Map<Integer, String>>() {
            @Override
            public void invokeHeadMap(Map<Integer, String> headMap, AnalysisContext context) {
                System.out.println("\n=== Excel实际表头 ===");
                headMap.values().forEach(System.out::println);
            }

            @Override
            public void invoke(Map<Integer, String> data, AnalysisContext context) {}

            @Override
            public void doAfterAllAnalysed(AnalysisContext context) {}
        }).sheet().doRead();
    }
}
