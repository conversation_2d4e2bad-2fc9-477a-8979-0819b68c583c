package com.echronos.expo.util;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 观众导入模板
 *
 * <AUTHOR>
 * @Date 2025/5/19 14:13
 * @ClassName AudienceTemplateFiled
 */
@Data
public class AudienceTemplateFiled {
    /**
     * 观众名称
     */
    @ExcelProperty(value = "*Name", index = 0)
    @ColumnWidth(value = 20)
    private String name;
    /**
     * 手机号
     */
    @ExcelProperty(value = "*Mobile", index = 1)
    @ColumnWidth(value = 20)
    private String phone;
    /**
     * 电子邮箱
     */
    @ExcelProperty(value = "*E-mail", index = 2)
    @ColumnWidth(value = 30)
    private String email;

    /**
     * 渠道
     */
    @ExcelProperty(value = "Channel", index = 3)
    @ColumnWidth(value = 20)
    private String channelName;
    /**
     * 公司名称
     */
    @ExcelProperty(value = "Company", index = 4)
    @ColumnWidth(value = 20)
    private String companyName;
    /**
     * 公司网站
     */
    @ExcelProperty(value = "Company Website", index = 5)
    @ColumnWidth(value = 30)
    private String companyWebsite;

    /**
     * 贵公司的主要行业是什么？
     */
    @ExcelProperty(value = "What is your company's main industry?", index = 6)
    @ColumnWidth(value = 40)
    private String industryCode;

    /**
     * 您想注册成为专业买家吗？
     */
    @ExcelProperty(value = "Would you like to register as a professional buyer?", index = 7)
    @ColumnWidth(value = 40)
    private String asBuyer;

    /**
     * 您在展会上扮演什么角色？
     */
    @ExcelProperty(value = "What is your role at the exhibition?", index = 8)
    @ColumnWidth(value = 40)
    private String expoRole;
    /**
     * 您最感兴趣的是哪个展区？（可多选）
     */
    @ExcelProperty(value = "Which exhibition area interests you most? (Multiple choice)", index = 9)
    @ColumnWidth(value = 40)
    private String interests;

    /**
     * 您想参加哪些活动？（可多选）
     */
    @ExcelProperty(value = "Which activities would you like to participate in? (Multiple choice)", index = 10)
    @ColumnWidth(value = 40)
    private String participate;


    /**
     * 您有特定的采购或合作需求吗？
     */
    @ExcelProperty(value = "Do you have specific procurement or cooperation needs?", index = 11)
    @ColumnWidth(value = 40)
    private String procurementNeeds;


    /**
     * 您的公司规模是多少？
     */
    @ExcelProperty(value = "What is your company size?", index = 12)
    @ColumnWidth(value = 40)
    private String companySize;

    /**
     * 您的职位是什么？
     */
    @ExcelProperty(value = "What is your position?", index = 13)
    @ColumnWidth(value = 20)
    private String position;

    /**
     * 您在寻找什么产品或解决方案？（可多选）
     */
    @ExcelProperty(value = "What products or solutions are you looking for? (Multiple choice)", index = 14)
    @ColumnWidth(value = 40)
    private String productService;
    /**
     * 您的采购时间表是怎样的？
     */
    @ExcelProperty(value = "What is your procurement timeline?", index = 15)
    @ColumnWidth(value = 40)
    private String procurementTimeline;
    /**
     * 您的项目预算范围是多少？（美元）
     */
    @ExcelProperty(value = "What is your project budget range? (USD)", index = 16)
    @ColumnWidth(value = 40)
    private String budget;
    /**
     * 您在采购决策中扮演什么角色？
     */
    @ExcelProperty(value = "What is your role in procurement decisions?", index = 17)
    @ColumnWidth(value = 40)
    private String procurementRole;

    /**
     * 您对供应商的主要要求是什么？（可多选）？
     */
    @ExcelProperty(value = "What are your main requirements for suppliers? (Multiple choice)", index = 18)
    @ColumnWidth(value = 40)
    private String requirements;
    /**
     * 您首选的合作模式是什么？
     */
    @ExcelProperty(value = "What is your preferred cooperation model?", index = 19)
    @ColumnWidth(value = 40)
    private String cooperationModel;
    /**
     * 请简要描述您的具体需求或项目情况
     */
    @ExcelProperty(value = "Please briefly describe your specific needs or project situation", index = 20)
    @ColumnWidth(value = 40)
    private String specificNeeds;
    /**
     * 您希望我们如何联系您？
     */
    @ExcelProperty(value = "How would you prefer us to contact you?", index = 21)
    @ColumnWidth(value = 40)
    private String contactType;
    /**
     * 您在展会期间有什么计划？（可多选）
     */
    @ExcelProperty(value = "What are your plans during the exhibition? (Multiple choice)", index = 22)
    @ColumnWidth(value = 40)
    private String expoPlans;
    /**
     * ip地址
     */
    @ExcelProperty(value = "ip", index = 23)
    @ColumnWidth(value = 20)
    private String ip;
    /**
     * 提交时间
     */
    @ExcelProperty(value = "CreateTime")
    @ColumnWidth(value = 20)
    private LocalDateTime createTime;
}
