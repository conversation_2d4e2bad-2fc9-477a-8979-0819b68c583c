package com.echronos.expo;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.scheduling.annotation.EnableScheduling;

import javax.annotation.PostConstruct;
import java.util.TimeZone;

/**
 * <AUTHOR>
 * @Date 2025/5/12 19:28
 * @ClassName ExpoServiceApplication
 */

@SpringBootApplication
@ComponentScan({"com.echronos.*"})
@MapperScan("com.echronos.expo.dao")
@EnableDiscoveryClient
@EnableScheduling
@EnableFeignClients(basePackages = {"com.echronos"})
public class ExpoServiceApplication {

    @PostConstruct
    void started() {
        TimeZone.setDefault(TimeZone.getTimeZone("UTC"));
    }

    public static void main(String[] args) {
        SpringApplication.run(ExpoServiceApplication.class, args);
    }
}
