package com.echronos.expo.config;

import com.baomidou.mybatisplus.core.handlers.MetaObjectHandler;
import com.echronos.commons.model.AppThreadLocal;
import com.echronos.commons.utils.RequestUserUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.reflection.MetaObject;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.Arrays;
import java.util.List;

/**
 * 自动填充字段处理
 *
 * <AUTHOR>
 */
@Primary
@Slf4j
@Component
public class ExpoMetaObjectHandler implements MetaObjectHandler {

    @Override
    public void insertFill(MetaObject metaObject) {
        List<String> strings = Arrays.asList(metaObject.getGetterNames());
        if (strings.contains("createUser")) {
            setUser(metaObject, "createUser");
        }
        if (strings.contains("createMember")) {
            setMember(metaObject, "createMember");
        }
        if (strings.contains("createTime")) {
            setTime(metaObject, "createTime");
        }
        if (strings.contains("updateTime")) {
            setUpdateTime(metaObject, "updateTime");
        }
        if (strings.contains("updateUser")) {
            setUpdateUser(metaObject, "updateUser");
        }
        if (strings.contains("tenantId")) {
            setTenantId(metaObject);
        }
    }

    @Override
    public void updateFill(MetaObject metaObject) {
        List<String> strings = Arrays.asList(metaObject.getGetterNames());
        if (strings.contains("updateUser")) {
            setUpdateUser(metaObject, "updateUser");
        }
        if (strings.contains("updateMember")) {
            setMember(metaObject, "updateMember");
        }
        if (strings.contains("updateTime")) {
            setUpdateTime(metaObject, "updateTime");
        }
    }

    private void setUser(MetaObject metaObject, String column) {
        if (null != metaObject.getValue(column)) {
            return;
        }
        try {
            Object userId = null != RequestUserUtils.getUser() ? RequestUserUtils.getUser().getId() : metaObject.getValue(column);
            if (null != userId) {
                this.setFieldValByName(column, userId, metaObject);
            }
        } catch (Exception ignored) {
        }
    }

    private void setUpdateUser(MetaObject metaObject, String column) {
        try {
            Object userId = null != RequestUserUtils.getUser() ? RequestUserUtils.getUser().getId() : metaObject.getValue(column);
            if (null != userId) {
                this.setFieldValByName(column, userId, metaObject);
            }
        } catch (Exception ignored) {
        }
    }

    private void setMember(MetaObject metaObject, String column) {
        if (null != metaObject.getValue(column)) {
            return;
        }
        try {
            Object memberId = null != RequestUserUtils.getUser() ? RequestUserUtils.getUser().getMemberId() : metaObject.getValue(column);
            if (null != memberId) {
                this.setFieldValByName(column, memberId, metaObject);
            }
        } catch (Exception ignored) {
        }
    }

    private void setTime(MetaObject metaObject, String column) {
        if (null != metaObject.getValue(column)) {
            return;
        }
        this.setFieldValByName(column, LocalDateTime.now(ZoneOffset.UTC), metaObject);
    }

    private void setUpdateTime(MetaObject metaObject, String column) {
        this.setFieldValByName(column, LocalDateTime.now(ZoneOffset.UTC), metaObject);
    }

    private void setTenantId(MetaObject metaObject) {
        if (null != metaObject.getValue("tenantId")) {
            return;
        }
        try {
            Object tenantId = null;
            //优先从线程上下文件获取再从登录用户获取
            if (null != AppThreadLocal.get()) {
                tenantId = AppThreadLocal.getTenantId();
            } else if (null != RequestUserUtils.getUser()) {
                tenantId = RequestUserUtils.getUser().getTenantId();
            }
            if (null != tenantId) {
                this.setFieldValByName("tenantId", tenantId, metaObject);
            } else {
                log.error("未取到租户ID");
            }
        } catch (Exception ignored) {
        }
    }
}
