package com.echronos.expo.feign.impl;

import com.echronos.commons.Result;
import com.echronos.commons.exception.BusinessException;
import com.echronos.commons.utils.CopyObjectUtils;
import com.echronos.expo.api.feign.IExpoTenancySkuFeign;
import com.echronos.expo.api.req.ExpoTenancySkuReq;
import com.echronos.expo.api.resp.ExpoTenancySkuResp;
import com.echronos.expo.enums.ExpoResultCode;
import com.echronos.expo.manager.ExpoInfoManager;
import com.echronos.expo.manager.ExpoTenancySkuManager;
import com.echronos.expo.model.ExpoInfo;
import com.echronos.expo.model.ExpoTenancySku;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Objects;

/**
 * 展商租赁商品feign接口实现
 *
 * <AUTHOR>
 * date2025/8/15 10:25
 */
@Slf4j
@RestController
public class ExpoTenancySkuFeign implements IExpoTenancySkuFeign {

    @Autowired
    private ExpoInfoManager expoInfoManager;
    @Autowired
    private ExpoTenancySkuManager expoTenancySkuManager;

    @Override
    public Result<List<ExpoTenancySkuResp>> queryTenancyByExpoId(ExpoTenancySkuReq req) {
        ExpoInfo expoInfo = expoInfoManager.getById(req.getExpoId());
        if(Objects.isNull(expoInfo)){
            throw new BusinessException(ExpoResultCode.ExpoResultEnum.EXPO_IS_NOT_EXIST.getCode(),
                    ExpoResultCode.ExpoResultEnum.EXPO_IS_NOT_EXIST.getMessage());
        }
        List<ExpoTenancySku> expoTenancySkuList = expoTenancySkuManager.queryTenancySkuByExpoId(req.getExpoId(), null);
        return Result.build(CopyObjectUtils.copyAlistToBlist(expoTenancySkuList, ExpoTenancySkuResp.class));
    }
}
