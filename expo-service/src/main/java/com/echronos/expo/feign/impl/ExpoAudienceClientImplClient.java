package com.echronos.expo.feign.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.echronos.commons.Result;
import com.echronos.expo.api.feign.IExpoAudienceFeignClient;
import com.echronos.expo.api.req.IExpoEmailNmsReq;
import com.echronos.expo.manager.ExpoAudienceEmailRecordsManager;
import com.echronos.expo.model.ExpoAudienceEmailRecords;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * <AUTHOR>
 * @Date 2025/5/21 19:59
 * @ClassName ExpoAudienceClientImpl
 */
@RestController
@RequestMapping("/api/expo/audience")
public class ExpoAudienceClientImplClient implements IExpoAudienceFeignClient {
    @Resource
    private ExpoAudienceEmailRecordsManager expoAudienceEmailRecordsManager;

    @Override
    public Result notifyEmailSend(IExpoEmailNmsReq req) {
        ExpoAudienceEmailRecords records = expoAudienceEmailRecordsManager.getOne(new LambdaQueryWrapper<ExpoAudienceEmailRecords>()
                .eq(ExpoAudienceEmailRecords::getBusinessCode, req.getEmailTxCode())
        );
        if (Objects.nonNull(records)) {
            records.setIsSend(req.getIsSend());
            expoAudienceEmailRecordsManager.saveOrUpdate(records);
        }
        return Result.build();
    }
}
