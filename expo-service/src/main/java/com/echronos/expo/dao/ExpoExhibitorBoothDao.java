/**
 * Copyright © 2020 ECHRONOS Corp. All rights reserved.
 * This software is proprietary to and embodies the confidential
 * technology of ECHRONOS Corp.  Possession, use, or copying
 * of this software and media is authorized only pursuant to a
 * valid written license from ECHRONOS Corp or an authorized sublicensor.
 */
package com.echronos.expo.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.echronos.expo.dto.ExpoExhibitorBoothDTO;
import com.echronos.expo.model.ExpoExhibitorBooth;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * ExpoExhibitorBooth Dao
 * 
 * <AUTHOR>
 * @date 2025-08-06
 */
public interface ExpoExhibitorBoothDao extends BaseMapper<ExpoExhibitorBooth> {

    /**
     * 获取展商展位详情
     * @param exhibitorIdList 展商ID列表
     * @return
     */
    List<ExpoExhibitorBoothDTO> getDetailByExhibitorIdList(@Param("exhibitorIdList") List<Integer> exhibitorIdList);

}
