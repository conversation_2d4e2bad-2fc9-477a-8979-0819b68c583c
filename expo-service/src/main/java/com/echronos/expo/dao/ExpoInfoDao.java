/**
 * Copyright © 2020 ECHRONOS Corp. All rights reserved.
 * This software is proprietary to and embodies the confidential
 * technology of ECHRONOS Corp.  Possession, use, or copying
 * of this software and media is authorized only pursuant to a
 * valid written license from ECHRONOS Corp or an authorized sublicensor.
 */
package com.echronos.expo.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.echronos.expo.dto.ExpoInfoPageDTO;
import com.echronos.expo.model.ExpoInfo;
import com.echronos.expo.model.ext.ExpoIndexCountExt;
import com.echronos.expo.model.ext.ExpoInfoExt;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * EchExpoInfo Dao
 * 
 * <AUTHOR>
 * @date 2025-05-13
 */
public interface ExpoInfoDao extends BaseMapper<ExpoInfo> {

    /**
     * 分页查询
     * @param page
     * @param dto
     * @return
     */
    List<ExpoInfoExt> pageList(Page page, @Param("dto") ExpoInfoPageDTO dto);

    /**
     * 获取展会总数（首页）
     * @param companyId
     * @return
     */
    ExpoIndexCountExt getExpoIndexCount(@Param("companyId") Integer companyId);

}
