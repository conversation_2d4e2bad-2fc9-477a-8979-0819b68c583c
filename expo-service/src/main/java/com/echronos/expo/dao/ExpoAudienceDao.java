/**
 * Copyright © 2020 ECHRONOS Corp. All rights reserved.
 * This software is proprietary to and embodies the confidential
 * technology of ECHRONOS Corp.  Possession, use, or copying
 * of this software and media is authorized only pursuant to a
 * valid written license from ECHRONOS Corp or an authorized sublicensor.
 */
package com.echronos.expo.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.echronos.expo.dto.ExpoAudienceDTO;
import com.echronos.expo.dto.ExpoAudiencePageDTO;
import com.echronos.expo.model.ExpoAudience;
import com.echronos.expo.model.ext.ExpoChannelAudienceExt;
import com.echronos.expo.model.ext.ExpoIndexCountExt;
import com.echronos.expo.vo.statistics.ExpoIndexCountVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * EchExpoAudience Dao
 *
 * <AUTHOR>
 * @date 2025-05-14
 */
public interface ExpoAudienceDao extends BaseMapper<ExpoAudience> {

    /**
     * 分页查询观众
     *
     * @param page
     * @param dto
     * @return
     */
    Page<ExpoAudienceDTO> pageForAudience(Page<ExpoAudienceDTO> page, @Param("dto") ExpoAudiencePageDTO dto);

    /**
     * 查询观众
     *
     * @param dto
     * @return
     */
    List<ExpoAudienceDTO> pageForAudience(@Param("dto") ExpoAudiencePageDTO dto);

    /**
     * 查询观众到场次数
     * @param dto
     * @return
     */
    List<Integer> queryAudienceIdList(ExpoAudienceDTO dto);

    /**
     * 获取首页观众数量
     * @param companyId
     * @return
     */
    ExpoIndexCountExt getIndexAudienceCount(@Param("companyId") Integer companyId);

    /**
     * 获取展会观众签到数量
     * @param expoId 展会ID
     * @return
     */
    Integer getAudienceSignInCount(@Param("expoId") Integer expoId);

    /**
     * 获取展会累计签到数量
     * @param expoId
     * @return
     */
    Integer getAudienceCumulativeSignInCount(@Param("expoId")Integer expoId);

    /**
     * 获取展会渠道观众数量
     * @param expoId
     * @return
     */
    List<ExpoChannelAudienceExt> getChannelAudienceCountList(@Param("expoId")Integer expoId);
}
