/**
 * Copyright © 2020 ECHRONOS Corp. All rights reserved.
 * This software is proprietary to and embodies the confidential
 * technology of ECHRONOS Corp.  Possession, use, or copying
 * of this software and media is authorized only pursuant to a
 * valid written license from ECHRONOS Corp or an authorized sublicensor.
 */
package com.echronos.expo.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.echronos.expo.model.ExpoAppointedPersonnelTime;
import com.echronos.expo.model.ext.ExpoAppointedPersonnelTimeExt;
import org.apache.ibatis.annotations.Param;

import java.util.List;


/**
 * ExpoAppointedPersonnelTime Dao
 * 
 * <AUTHOR>
 * @date 2025-08-07
 */
public interface ExpoAppointedPersonnelTimeDao extends BaseMapper<ExpoAppointedPersonnelTime> {

    /**
     * 查询已设置的时间段（包含是否被预约标识）
     *
     * @param personnelId
     * @return
     */
    List<ExpoAppointedPersonnelTimeExt> queryBy(@Param("personnelId") Integer personnelId);
}
