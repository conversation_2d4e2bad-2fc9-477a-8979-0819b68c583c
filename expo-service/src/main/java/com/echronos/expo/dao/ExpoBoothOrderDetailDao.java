/**
 * Copyright © 2020 ECHRONOS Corp. All rights reserved.
 * This software is proprietary to and embodies the confidential
 * technology of ECHRONOS Corp.  Possession, use, or copying
 * of this software and media is authorized only pursuant to a
 * valid written license from ECHRONOS Corp or an authorized sublicensor.
 */
package com.echronos.expo.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.echronos.expo.model.ExpoBoothOrderDetail;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDateTime;
import java.util.List;

/**
 * ExpoBoothOrderDetail Dao
 * 
 * <AUTHOR>
 * @date 2025-08-01
 */
public interface ExpoBoothOrderDetailDao extends BaseMapper<ExpoBoothOrderDetail> {

    /**
     * 根据展商ID移除订单明细
     * @param exhibitorId 展商ID
     * @param userId 用户ID
     * @param updateTime 更新时间
     */
    void removeByExhibitorId(@Param("exhibitorId") Integer exhibitorId, @Param("userId")Integer userId, @Param("updateTime")LocalDateTime updateTime);

}
