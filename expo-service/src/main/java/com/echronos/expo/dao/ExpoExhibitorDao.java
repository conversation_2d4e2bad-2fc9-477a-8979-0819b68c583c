/**
 * Copyright © 2020 ECHRONOS Corp. All rights reserved.
 * This software is proprietary to and embodies the confidential
 * technology of ECHRONOS Corp.  Possession, use, or copying
 * of this software and media is authorized only pursuant to a
 * valid written license from ECHRONOS Corp or an authorized sublicensor.
 */
package com.echronos.expo.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.echronos.expo.dto.ExpoExhibitorDTO;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.echronos.expo.dto.ExpoExhibitorDTO;
import com.echronos.expo.model.ExpoExhibitor;
import com.echronos.expo.model.ext.ExpoIndexCountExt;
import org.apache.ibatis.annotations.MapKey;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

import com.echronos.expo.model.ext.ExpoExhibitorExt;
import org.apache.ibatis.annotations.Param;

/**
 * ExpoExhibitor Dao
 *
 * <AUTHOR>
 * @date 2025-08-01
 */
public interface ExpoExhibitorDao extends BaseMapper<ExpoExhibitor> {

    /**
     * 分页查询
     * @param page 分页对象
     * @param dto 参数DTO
     * @return
     */
    List<ExpoExhibitorDTO> pageList(Page page, @Param("dto") ExpoExhibitorDTO dto);

    /**
     * 建站分页查询展商信息
     *
     * @return
     */
    IPage<ExpoExhibitorExt> webExhibitorPage(Page<ExpoExhibitorDTO> page, @Param("dto") ExpoExhibitorDTO dto);

    /**
     * 官网分页查询展商信息
     * @param page
     * @param dto
     * @return
     */
    IPage<ExpoExhibitorExt> queryAllExhibitorList(Page<ExpoExhibitorDTO> page, @Param("dto") ExpoExhibitorDTO dto);

    /**
     * 获取首页展商数量
     * @param companyId 公司ID
     * @return
     */
    ExpoIndexCountExt getIndexExhibitorCount(@Param("companyId") Integer companyId);

//    /**
//     * 获取展会展商列表（用于排行统计）
//     * @param expoId 展会ID
//     * @param companyId 公司ID
//     * @return 展商列表
//     */
    @MapKey("exhibitor_id")
    List<Map<String, Object>> getExhibitorListForRanking(@Param("expoId") Integer expoId, @Param("companyId") Integer companyId);
}
