/**
 * Copyright © 2020 ECHRONOS Corp. All rights reserved.
 * This software is proprietary to and embodies the confidential
 * technology of ECHRONOS Corp.  Possession, use, or copying
 * of this software and media is authorized only pursuant to a
 * valid written license from ECHRONOS Corp or an authorized sublicensor.
 */
package com.echronos.expo.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.echronos.expo.model.ExpoAudienceExtend;

import java.util.Set;

/**
 * ExpoAudienceExtendDao Dao
 *
 * <AUTHOR>
 * @date 2025-05-14
 */
public interface ExpoAudienceExtendDao extends BaseMapper<ExpoAudienceExtend> {


    /**
     * 查询展会自定义表单字段
     *
     * @param expoId
     * @param formCode
     * @return
     */
    Set<String> fieldList(Integer expoId, String formCode);
}
