/**
 * Copyright © 2020 ECHRONOS Corp. All rights reserved.
 * This software is proprietary to and embodies the confidential
 * technology of ECHRONOS Corp.  Possession, use, or copying
 * of this software and media is authorized only pursuant to a
 * valid written license from ECHRONOS Corp or an authorized sublicensor.
 */
package com.echronos.expo.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.echronos.expo.model.ExpoExhibitorScanRecord;
import com.echronos.expo.model.ext.ExpoExhibitorScanCountExt;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDate;
import java.util.List;

/**
 * ExpoExhibitorScanRecord Dao
 *
 * <AUTHOR>
 * @date 2025-08-15
 */
public interface ExpoExhibitorScanRecordDao extends BaseMapper<ExpoExhibitorScanRecord> {

    /**
     * 按日期统计展商扫码次数
     *
     * @param expoId 展会ID
     * @param companyId 公司ID
     * @return 日期和扫码次数的映射
     */
    List<ExpoExhibitorScanCountExt> countScanRecordsByDate(@Param("expoId") Integer expoId, @Param("companyId") Integer companyId);

    /**
     * 按小时统计展商扫码次数
     *
     * @param expoId 展会ID
     * @param companyId 公司ID
     * @param statisticsDate 统计日期
     * @return 小时和扫码次数的映射
     */
    List<ExpoExhibitorScanCountExt> countScanRecordsByHour(@Param("expoId") Integer expoId, @Param("companyId") Integer companyId, @Param("statisticsDate") LocalDate statisticsDate);

    /**
     * 按展商统计扫码次数
     *
     * @param expoId 展会ID
     * @param companyId 公司ID
     * @return 展商ID和扫码次数的映射
     */
    List<ExpoExhibitorScanCountExt> countScanRecordsByExhibitor(@Param("expoId") Integer expoId, @Param("companyId") Integer companyId);
}
