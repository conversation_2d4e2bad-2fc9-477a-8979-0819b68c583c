package com.echronos.expo.easyexcel.model;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.echronos.expo.easyexcel.annotation.DropDownField;
import com.echronos.expo.easyexcel.constant.DropDownFieldConstant;
import lombok.Data;

import java.math.BigDecimal;


/**
 * 导入新增展位模板对象
 * <AUTHOR>
 * @create 2020/7/23 10:07
 */
@Data
public class ImportBoothExcelModel extends ExcelBaseModel{


    /**
     * 展馆号/名称
     */
    @ExcelProperty(value = {"EXPO.BOOTH.EXCEL.BOOTHNAME"})
    @ColumnWidth(value = 20)
    private String boothName;

    /**
     * 楼层
     */
    @ExcelProperty(value = {"EXPO.BOOTH.EXCEL.BOOTHFLOOR"})
    @ColumnWidth(value = 20)
    private String boothFloor;

    /**
     * 区号/区域
     */
    @ExcelProperty(value = {"EXPO.BOOTH.EXCEL.BOOTHZONE"})
    @ColumnWidth(value = 20)
    private String boothZone;

    /**
     * 展位号
     */
    @ExcelProperty(value = {"EXPO.BOOTH.EXCEL.boothnumber"})
    @ColumnWidth(value = 20)
    private String boothNumber;

    /**
     * 展位类型：1.标准展位  2.光地 3.角位 4.半岛位 5.岛位 6.双开口位
     */
    @ExcelProperty(value = {"EXPO.BOOTH.EXCEL.BOOTHTYPE"})
    @ColumnWidth(value = 20)
    @DropDownField(fieldName = DropDownFieldConstant.BOOTH_TYPE)
    private String boothType;

    /**
     * 尺寸
     */
    @ExcelProperty(value = {"EXPO.BOOTH.EXCEL.DIMENSIONS"})
    @ColumnWidth(value = 20)
    private String dimensions;

    /**
     * 价格
     */
    @ExcelProperty(value = {"EXPO.BOOTH.EXCEL.PRICE"})
    @ColumnWidth(value = 20)
    private BigDecimal price;

}
