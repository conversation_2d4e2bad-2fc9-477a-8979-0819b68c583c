package com.echronos.expo.constants;

import com.google.common.collect.Lists;
import lombok.Data;

import java.util.LinkedHashMap;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025/5/15 20:46
 * @ClassName ExpoAudienceConstants
 */
@Data
public class ExpoAudienceConstants {
    public static LinkedHashMap<String, List<String>> audienceIndustryMap = new LinkedHashMap<>();
    public static LinkedHashMap<String, List<String>> audienceProductsMap = new LinkedHashMap<>();
    public static String[] yesOrNo = {"Yes", "No"};

    //展会上扮演什么角色
    public static String[] expoRole = {"Professional Buyer", "Procurement Manager", "Industry Professional", "Investor", "Partner", "Media", "Academic", "Other"};

    //感兴趣的是哪个展区
    public static String[] interests = {"Green Manufacturing & Digital Infrastructure", "Smart Home & Consumer Electronics",
            "New Energy & Environmental Technology", "Education Technology & Digitalization",
            "Industrial Automation & Smart Manufacturing", "Other"
    };
    //参加哪些活动
    public static String[] participate = {"Exhibition Visit", "Business Matching", "Technical Forum", "Product Launch",
            "Business Tour"
    };

    //特定的采购或合作需求
    public static String[] procurementNeeds = {"Yes, I have specific procurement needs", "Yes, I am looking for partners",
            "Not at the moment, just gathering market information", "Not sure"
    };
    //行业
    public static String[] audienceIndustry = {"Manufacturing", "Retail & E-commerce", "Real Estate & Construction",
            "Education", "Healthcare", "Hotel & Tourism", "Logistics & Transportation", "Finance & Insurance",
            "Government & Public Service", "Other"
    };
    //公司规模
    public static String[] companySize = {"Small Enterprise (<50 employees)", "Medium Enterprise (50-500 employees)",
            "Large Enterprise (>500 employees)", "Government Institution", "Public Organization"
    };
    //职位
    public static String[] position = {"Senior Management (CEO/GM/VP)", "Middle Management (Department Manager/Supervisor)",
            "Procurement Manager", "Technical Manager", "Business Development", "Other"
    };

    //产品服务
    public static String[] products = {"Smart Door Locks & Security Systems", "Smart Lighting & Electrical Equipment",
            "Solar & New Energy Equipment", "Home & Office Automation", "Educational Digitalization Equipment",
            "Industrial Automation Equipment", "Audio-Visual & Display Equipment", "IoT & Sensors",
            "Software & System Integration", "Glass & Window & Curtain Wall Equipment", "Other"
    };
    //采购时间表
    public static String[] procurementTimeline = {"Within 3 months", "3-6 months", "6-12 months", "More than 12 months",
            "No specific timeline"
    };
    //采购时间表
    public static String[] budget = {"<50,000", "50,000-200,000", "200,000-500,000", "500,000-1,000,000", ">1,000,000",
            "Not determined yet"
    };

    //采购决策中扮演什么角色
    public static String[] procurementRole = {"Final Decision Maker", "Key Influencer", "Technical Evaluator",
            "Information Gatherer", "Other"
    };

    //您对供应商的主要要求是什么
    public static String[] requirements = {"Reliable Product Quality", "Competitive Pricing", "Advanced Technology",
            "Local Service Support", "Rich Success Cases", "Fast Delivery Capability", "Customization Capability", "Other"
    };
    //合作模式
    public static String[] cooperationModel = {"Direct Procurement", "Agency Distribution", "Technology Licensing",
            "Joint Venture", "OEM ODM", "Other"
    };
    //联系方式
    public static String[] contact = {"Phone Contact", "Email Contact", "WeChat WhatsApp", "Face-to-face Meeting",
            "No contact needed for now"
    };
    //展会期间有什么计划
    public static String[] plans = {"Exhibition Visit", "Business Matching Session", "Technical Forum", "Business Tour",
            "Meeting with Specific Exhibitors", "Other"
    };

    public static String[] positions = {"CEO/GM", "Director", "Manager/Staff"};
    public static String[] procurement = {"<300,000", "300,000-800,000", ">800,000"};
    public static String[] revenue = {"<1,000,000", "1,000,000-10,000,000", "10,000,000-50,000,000", "50,000,000-100,000,000", ">100,000,000"};

    static {
        audienceIndustryMap.put("Manufacturer", Lists.newArrayList("Automotive", "Electronics",
                "Consumer Electronics", "Home Appliance", "Security", "Communication", "Hardware and Plastic", "Machinery",
                "Medical Device", "Electric Power", "Pharmaceutical", "Chemical", "Energy", "Shipping", "Food & Beverage",
                "Textile", "Household", "Packaging/Printing")
        );
        audienceIndustryMap.put("Retailer/Distributor", Lists.newArrayList("Cross-border E-commerce",
                "Chain Stores/Supermarket", "Import/Export Traders", "Wholesaler")
        );
        audienceIndustryMap.put("Servicer", Lists.newArrayList("Hospitality & Travel",
                "Restaurant", "Finance & Banking", "Transportation", "Beauty", "Logistics, Warehouse and Fulfillment",
                "Sofeware Designer", "IDC", "Data & Cloud", "Cybersecurity")
        );
        audienceIndustryMap.put("Construction", Lists.newArrayList("Developer", "Contractor", "Design Consultancy"));

        audienceIndustryMap.put("Government/Organization", Lists.newArrayList("Government", "University/School",
                "Research Institution", "Association&Chamber", "Hospital")
        );
        audienceIndustryMap.put("Media", Lists.newArrayList());
        audienceIndustryMap.put("Others", Lists.newArrayList());


        audienceProductsMap.put("Smart Home", Lists.newArrayList("Smart Locks",
                "Smart Lighting Control",
                "Smart Security Systems",
                "Smart Appliances (e.g., Refrigerators, Washing Machines, Air Conditioners)",
                "Smart Speakers",
                "Smart Curtains",
                "Smart Temperature Control Devices",
                "Home Robots",
                "Smart Home Management Platforms"));
        audienceProductsMap.put("Smart Transportation", Lists.newArrayList("Autonomous Driving Technology",
                "Intelligent Traffic Management Systems",
                "Smart Parking Systems",
                "Electric Vehicles",
                "Intelligent Traffic Signal Systems",
                "In-Vehicle IoT Devices",
                "In-Car Entertainment Systems",
                "Autonomous Public Transportation"));

        audienceProductsMap.put("Smart Healthcare & Health", Lists.newArrayList("Wearable Health Devices (e.g., Smartwatches, Health Monitoring Devices)",
                "Smart Medical Devices (e.g., Smart Blood Pressure Monitors, Smart Glucose Meters)",
                "Telemedicine Platforms",
                "AI Diagnostic Systems",
                "Smart Medicine Management",
                "Health Data Analytics Platforms",
                "Medical Robots",
                "Health Management Software"));

        audienceProductsMap.put("Smart Manufacturing", Lists.newArrayList("Industrial Robots",
                "PLC Control Systems",
                "Automation Equipment for Production Lines",
                "3D Printing Technology",
                "Smart Warehouse Management Systems",
                "Drones for Monitoring & Management",
                "Smart Factory Management Systems"));

        audienceProductsMap.put("Artificial Intelligence", Lists.newArrayList("Speech Recognition Technology",
                "Facial Recognition Systems",
                "Machine Learning Platforms",
                "Data Analysis & Prediction Tools",
                "Chatbots",
                "Natural Language Processing (NLP) Technology",
                "Image Processing & Recognition",
                "Autonomous Driving Technology"));

        audienceProductsMap.put("New Energy & Battery Technology", Lists.newArrayList("Electric Vehicle Batteries",
                "Solid-State Batteries",
                "Solar Panels",
                "Wind Energy Technology",
                "Energy Storage Systems",
                "Battery Management Systems",
                "EV Charging Stations",
                "Energy Monitoring & Management Systems"));

        audienceProductsMap.put("Consumer Electronics", Lists.newArrayList("Smartphones",
                "Projection",
                "Smart Speakers & Headphones",
                "Tablets",
                "Wearable Devices (e.g., Smartwatches, Health Monitors)",
                "Smart Home Control Centers",
                "Gaming Devices & Accessories",
                "Wireless Charging Devices"));

        audienceProductsMap.put("Smart Agriculture", Lists.newArrayList("Agricultural Drones",
                "Smart Irrigation Systems",
                "Precision Agriculture Sensors",
                "Greenhouse Control Systems",
                "Agricultural Robots",
                "Crop Health Monitoring Devices",
                "Smart Soil Monitoring",
                "Automated Farming Machinery"));
        audienceProductsMap.put("Smart Buildings & Real Estate", Lists.newArrayList("Smart Building Management Systems",
                "Smart Lighting Systems",
                "Security & Surveillance Systems",
                "Smart Power Management Systems",
                "Building Automation Systems",
                "Unmanned Access Control Systems",
                "Smart Environmental Control (Temperature, Humidity, etc.)",
                "Integrated Smart Home Systems"));
        audienceProductsMap.put("Smart Education", Lists.newArrayList("Online Education Platforms",
                "Smart Classroom Systems",
                "AR/VR Educational Tools",
                "Educational Robots",
                "Smart Examination & Evaluation Systems",
                "Education Big Data Analytics Platforms",
                "Digital Learning Content",
                "Learning Management Systems (LMS)"));
        audienceProductsMap.put("Smart Retail & E-commerce", Lists.newArrayList("Smart POS Systems",
                "Smart Credit Scoring Systems",
                "Electronic Payment Solutions",
                "Virtual Fitting Rooms",
                "Unmanned Shelves & Vending Machines",
                "Customer Behavior Analytics Tools",
                "Smart Warehouse & Distribution Management",
                "E-commerce Platform Development & Optimization",
                "Smart Customer Service Robots"));

        audienceProductsMap.put("Smart Environmental & Green Technology", Lists.newArrayList("Environmental Sensors",
                "Smart Waste Sorting Systems",
                "Green Energy Management Systems",
                "Smart Water Quality Monitoring",
                "Smart Air Purifiers",
                "Carbon Emission Monitoring & Management",
                "Smart Waste Disposal Equipment"));
        audienceProductsMap.put("Communication & Networking Technology", Lists.newArrayList("5G Technology Solutions",
                "Wireless Networking Devices",
                "Fiber Optic Communication Equipment",
                "Data Center Infrastructure",
                "Network Security Devices",
                "Communication Modules & Chips",
                "Cloud Networking Services",
                "Wireless Local Area Network (Wi-Fi) Devices"));

        audienceProductsMap.put("Smart Tourism & Services", Lists.newArrayList("Smart Travel Guides",
                "Autonomous Tourist Vehicles",
                "Smart Hotel Management Systems",
                "Virtual Tourism Experiences",
                "Smart Luggage Tracking",
                "Tourism Big Data Analytics Platforms",
                "Smart Tour Guide Robots"));
        audienceProductsMap.put("Smart Logistics & Supply Chain", Lists.newArrayList("Automated Warehousing Equipment",
                "Logistics Robots (eg.AGV)",
                "Drone Delivery Systems",
                "Smart Supply Chain Management Software",
                "Smart Tracking & Monitoring Devices",
                "IoT-based Logistics Devices"));
        audienceProductsMap.put("Others", Lists.newArrayList());


    }
}
