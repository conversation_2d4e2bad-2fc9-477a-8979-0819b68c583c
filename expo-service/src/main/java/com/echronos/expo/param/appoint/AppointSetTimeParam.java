package com.echronos.expo.param.appoint;

import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/8/7 20:31
 */
@Data
public class AppointSetTimeParam {
    /**
     * 预约人员ID
     */
    @NotNull(message = "{NOT.NULL.EXPO.ID}")
    private Integer id;
    /**
     * 预约时间段
     */
    @Valid
    private List<TimeSlotParam> timeList;
}
