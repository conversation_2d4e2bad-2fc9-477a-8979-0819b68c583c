package com.echronos.expo.param.exhibitor;

import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * 展商扫码
 * <AUTHOR>
 * date2025/8/4 11:49
 */
@Data
public class ExpoExhibitorScanCodeParam {

    /**
     * 观众ID
     */
    @NotNull(message = "{NOTNULL.EXPO.AUDIENCEID}")
    private Integer expoAudienceId;

    /**
     * 展会ID
     */
    @NotNull(message = "{NOTNILL.EXPOID}")
    private Integer expoId;
}
