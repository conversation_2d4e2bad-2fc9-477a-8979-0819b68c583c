package com.echronos.expo.param.booth;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2025-08-01 17:46
 */
@Data
public class AddOrUpdateBoothParam {

    /**
     * 展位ID（编辑时必填，新增时为空）
     */
    private Integer id;

    /**
     * 展会id
     */
    @NotNull(message = "展会ID不能为空")
    private Integer expoId;

    /**
     * 展馆号/名称
     */
    @NotBlank(message = "展馆号/名称不能为空")
    private String boothName;

    /**
     * 楼层
     */
    @NotBlank(message = "楼层不能为空")
    private String boothFloor;

    /**
     * 区号/区域
     */
    @NotBlank(message = "区号/区域不能为空")
    private String boothZone;

    /**
     * 展位号
     */
    @NotBlank(message = "展位号不能为空")
    private String boothNumber;

    /**
     * 展位类型：1.标准展位  2.光地 3.角位 4.半岛位 5.岛位 6.双开口位
     */
    @NotNull(message = "展位类型不能为空")
    private Integer boothType;

    /**
     * 尺寸
     */
    @NotBlank(message = "尺寸不能为空")
    private String dimensions;

    /**
     * 价格
     */
    @NotNull(message = "价格不能为空")
    private BigDecimal price;

}
