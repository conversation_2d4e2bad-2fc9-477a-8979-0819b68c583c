package com.echronos.expo.param;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025/5/16 15:36
 * @ClassName ExpoAudienceListFilterParam
 */
@Data
public class ExpoAudienceListFilterParam extends BaseListFilterParam {

    /**
     * 观众ID集合
     */
    private List<Integer> idList;

    /**
     * 展会ID
     */
    @NotNull(message = "{NOT.NULL.EXPO}")
    private Integer expoId;
}
