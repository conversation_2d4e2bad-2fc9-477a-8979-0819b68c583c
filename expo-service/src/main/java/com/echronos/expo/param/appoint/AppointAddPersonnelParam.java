package com.echronos.expo.param.appoint;

import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/8/8 17:48
 */
@Data
public class AppointAddPersonnelParam {
    /**
     * 展商ID
     */
    @NotNull(message = "{NOT.NULL.EXPO.ID}")
    private Integer businessId;
    /**
     * 展会ID
     */
    @NotNull(message = "{NOT.NULL.EXPO}")
    private Integer expoId;
    /**
     * 成员ID
     */
    @NotEmpty(message = "成员ID不能为空")
    private List<Integer> memberIds;
}
