package com.echronos.expo.param.exhibitor;

import lombok.Data;

import javax.validation.constraints.DecimalMin;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * 展位订单明细
 * <AUTHOR>
 * @date 2025-08-04 20:48
 */
@Data
public class AddLeaseOrderDetailParam {

    /**
     * 商品shopSkuId
     */
    @NotNull(message = "商品ID不能为空")
    private Integer shopSkuId;

    /**
     * 数量
     */
    @NotNull(message = "数量不能为空")
    @DecimalMin(value = "0.01", message = "数量必须大于0")
    private BigDecimal quantity;

    /**
     * 含税单价
     */
    @NotNull(message = "含税单价不能为空")
    @DecimalMin(value = "0.01", message = "含税单价必须大于0")
    private BigDecimal price;

    /**
     * 税率(%)
     */
    private BigDecimal taxRate;

}
