package com.echronos.expo.param;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025-08-14 10:59
 */
@Data
public class CopyExpoFormParam {

    /**
     * 展会ID
     */
    @NotNull(message = "展会ID不能为空")
    private Integer expoId;
    /**
     * 复制的展会ID
     */
    @NotNull(message = "复制的展会ID不能为空")
    private Integer copyExpoId;
    /**
     * 表单id
     */
    @NotNull(message = "表单id不能为空")
    private List<Integer> formIds;

}
