package com.echronos.expo.param.booth;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @date 2025-08-04 16:00
 */
@Data
public class ImportExpoBoothParam {

    /**
     * 展会id
     */
    @NotNull(message = "展会id不能为空")
    private Integer expoId;
    /**
     * 导入的excel文件地址
     */
    @NotBlank(message = "导入的excel文件地址不能为空")
    private String fileUrl;

}
