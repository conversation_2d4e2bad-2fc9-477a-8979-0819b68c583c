package com.echronos.expo.param;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025/5/21 15:36
 * @ClassName ExpoAudienceListParam
 */
@Data
public class ExpoAudienceListParam {

    /**
     * 观众ID集合
     */
    @NotNull(message = "{NOT.NULL.EXPO.ID}")
    private List<Long> idList;

    /**
     * 展会ID
     */
    @NotNull(message = "{NOT.NULL.EXPO}")
    private Long expoId;
}
