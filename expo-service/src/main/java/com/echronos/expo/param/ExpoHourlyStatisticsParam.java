package com.echronos.expo.param;

import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotNull;
import java.time.LocalDate;

/**
 * 每小时互动分布统计参数
 *
 * <AUTHOR>
 * @date 2025/8/18 16:35
 */
@Data
public class ExpoHourlyStatisticsParam {

    /**
     * 展会ID
     */
    @NotNull(message = "{NOTNILL.EXPOID}")
    private Integer expoId;

    /**
     * 统计日期
     */
    @NotNull(message = "统计日期不能为空")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private LocalDate statisticsDate;
}
