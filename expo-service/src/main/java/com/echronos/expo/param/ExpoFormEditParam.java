/**
 * Copyright © 2020 ECHRONOS Corp. All rights reserved.
 * This software is proprietary to and embodies the confidential
 * technology of ECHRONOS Corp.  Possession, use, or copying
 * of this software and media is authorized only pursuant to a
 * valid written license from ECHRONOS Corp or an authorized sublicensor.
 */
package com.echronos.expo.param;


import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * EchExpoForm controller层返回值
 *
 * <AUTHOR>
 * @date 2025-05-13
 */
@Data
public class ExpoFormEditParam {
    @NotNull(message = "{NOT.NULL.EXPO.FORM.ID}")
    private Long id;
    /**
     * 展会id
     */
    @NotNull(message = "{NOT.NULL.EXPO}")
    private Long expoId;
    /**
     * 表单类型：1、观众注册表单，2、参展申请表单
     */
    private Integer formType = 1;
    /**
     * 表单名称
     */
    @NotBlank(message = "{NOT.NULL.EXPO.FORM.NAME}")
    private String formName;
    /**
     * 表单描述
     */
    private String description;
    /**
     * 发布站点id
     */
    private String publishTenantId;
    /**
     * 自定义表单系统code
     */
    @NotBlank(message = "自定义表单系统code不能为空")
    private String formCode;
    /**
     * 自定义表单版本号
     */
    private Long versionNumber;
    /**
     * 是否启用
     */
    private Boolean isEnable;
}