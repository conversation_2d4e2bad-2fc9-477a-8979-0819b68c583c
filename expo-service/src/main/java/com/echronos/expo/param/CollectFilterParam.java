package com.echronos.expo.param;

import com.echronos.iform.api.entity.CollectCondition;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025/7/1 17:52
 * @ClassName CollectFilterParam
 */
@Data
public class CollectFilterParam {
    /**
     * 条件集合
     */
    @NotNull(message = "至少添加一个筛选项")
    private List<CollectConditionParam> conditions;
}
