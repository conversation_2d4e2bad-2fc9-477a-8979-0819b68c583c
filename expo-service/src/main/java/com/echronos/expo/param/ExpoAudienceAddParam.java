package com.echronos.expo.param;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2025/5/20 11:37
 * @ClassName ExpoAudienceAddParam
 */
@Data
public class ExpoAudienceAddParam {

    /**
     * 所属公司id
     */
    @NotNull(message = "{NOT.NULL.EXPO.COMPANYID}")
    private Integer companyId;
    /**
     * 展会id
     */
    @NotNull(message = "{NOT.NULL.EXPO}")
    private Long expoId;
    /**
     * 渠道id
     */
    @NotNull(message = "{NOT.NULL.EXPO.CHANNEL}")
    private Long channelId;
    /**
     * 观众名称
     */
    @NotBlank(message = "{NOT.NULL.EXPO.NAME}")
    private String name;
    /**
     * 手机号
     */
    @NotBlank(message = "{NOT.NULL.EXPO.PHONE}")
//    @Pattern(regexp = "^((?:\\+|00)(\\d{1,4})[\\s-]*(\\d{6,15}))|((?:\\+86|86|0086)?[\\s-]*1[3-9]\\d{9})$", message = "{EXPO.PHONE.REGEXP.ERROR}")
    private String phone;
    /**
     * 电子邮箱
     */
    @NotBlank(message = "{NOT.NULL.EXPO.EMAIL}")
    @Pattern(regexp = "^[a-zA-Z0-9_!#$%&'*+/=?`{|}~^.-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,6}$", message = "{EXPO.AUDIENCE.EMAIL.REGEXP.ERROR}")
    private String email;
    /**
     * 公司名称
     */
    @NotBlank(message = "{NOT.NULL.EXPO.COMPANYNAME}")
    private String companyName;
    /**
     * 自定义表单code
     */
    private String formCode;
    /**
     * 自定义表单版本
     */
    private Long versionNumber;
    /**
     * 自定义字段提交
     */
    private Map<String, String> extMap;
    /**
     * 租户id
     */
    @NotBlank(message = "{NOT.NULL.EXPO.TENANTID}")
    private String tenantId;

    /**
     * 成员ID
     */
    @NotNull(message = "{NOT.NULL.EXPO.CHARGEUSER}")
    private Integer memberId;
}
