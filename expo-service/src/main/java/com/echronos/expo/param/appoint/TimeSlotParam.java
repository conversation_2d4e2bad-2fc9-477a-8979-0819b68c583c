package com.echronos.expo.param.appoint;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2025/8/8 10:04
 */
@Data
public class TimeSlotParam {
    /**
     * 时间段id
     */
    private Integer id;
    /**
     * 开始时间
     */
    @NotNull(message = "开始时间不能为空")
    private LocalDateTime startTime;
    /**
     * 结束时间
     */
    @NotNull(message = "结束时间不能为空")
    private LocalDateTime endTime;
}
