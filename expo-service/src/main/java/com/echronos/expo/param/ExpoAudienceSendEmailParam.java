package com.echronos.expo.param;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025/5/21 15:36
 * @ClassName ExpoAudienceSendEmailParam
 */
@Data
public class ExpoAudienceSendEmailParam {

    /**
     * 观众ID集合
     */
    @NotNull(message = "{NOT.NULL.EXPO.ID}")
    private List<Long> ids;
    /**
     * 展会id
     */
    @NotNull(message = "{NOT.NULL.EXPO}")
    private Long expoId;
    /**
     * 邮件模板ID
     */
    private String templateId;

    /**
     * 标题
     */
    @NotBlank(message = "{NOT.NULL.EXPO.EMAIL.SUBJECT}")
    private String subject;

    /**
     * 邮件内容
     */
    @NotBlank(message = "{NOT.NULL.EXPO.EMAIL.CONTENT}")
    private String content;

    /**
     * 邮件配置id
     */
    private Integer configId;
}
