package com.echronos.expo.param;

import com.echronos.commons.req.BasePageReq;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @Date 2025/5/15 16:46
 * @ClassName ExpoChannelConfigPageParam
 */
@Data
public class ExpoChannelConfigPageParam extends BasePageReq {

    /**
     * 展会id
     */
    @NotNull(message = "{NOT.NULL.EXPO}")
    private Long expoId;

    /**
     * 渠道类型
     * 1、观众
     * 2、参展商
     */
    private Integer channelType = 1;
}
