package com.echronos.expo.param;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.constraints.NotNull;

/**
 * 观众导入
 *
 * <AUTHOR>
 * @Date 2025/5/19 14:01
 * @ClassName ExpoAudienceImportParam
 */
@Data
public class ExpoAudienceImportParam {
    @JSONField(serialize = false)
    private MultipartFile file;

    /**
     * 展会id
     */
    @NotNull(message = "{NOT.NULL.EXPO}")
    private Integer expoId;

    /**
     * 浏览器时区
     */
    private String zoneId;
}
