/**
 * Copyright © 2020 ECHRONOS Corp. All rights reserved.
 * This software is proprietary to and embodies the confidential
 * technology of ECHRONOS Corp.  Possession, use, or copying
 * of this software and media is authorized only pursuant to a
 * valid written license from ECHRONOS Corp or an authorized sublicensor.
 */
package com.echronos.expo.param;


import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;

/**
 * ExpoInfoParam 实体类
 *
 * <AUTHOR>
 * @date 2025-05-13
 */
@Data
public class ExpoInfoParam {
    /**
     * ID
     */
    @NotNull(message = "{NOT.NULL.EXPO.ID}")
    private Long id;
}