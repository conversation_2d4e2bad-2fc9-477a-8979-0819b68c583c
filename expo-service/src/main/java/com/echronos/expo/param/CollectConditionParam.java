package com.echronos.expo.param;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025/7/1 17:49
 * @ClassName CollectCondition
 */
@Data
public class CollectConditionParam {
    /**
     * 列名
     */
    @NotBlank(message = "列名不能为空")
    private String colName;
    /**
     * 符号
     */
    @NotBlank(message = "条件符号不能为空")
    private String symbol;
    /**
     * 条件值
     */
    private List<Object> value;
    /**
     * 是否业务字段：0否 1是
     */
    private Integer isBusiness = 0;

    /**
     * 字段类型
     */
    private String componentType;

    /**
     * 列名
     */
    private String fieldName;

    /**
     * 展示
     */
    private String label;
}
