package com.echronos.expo.param.tenancysku;

import com.alibaba.fastjson.annotation.JSONField;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.echronos.commons.req.BasePageReq;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * date2025/8/9 14:35
 */
@Data
public class QueryTenancySkuParam extends BasePageReq {

    /**
     * 展会ID
     */
    @NotNull(message = "{NOTNILL.EXPOID}")
    private Integer expoId;

    /**
     * 搜索内容
     */
    private String keyword;

    /**
     * 最低价格
     */
    @JSONField(serialzeFeatures = SerializerFeature.WriteMapNullValue)
    private BigDecimal minPrice;

    /**
     * 最高价格
     */
    @JSONField(serialzeFeatures = SerializerFeature.WriteMapNullValue)
    private BigDecimal maxPrice;
}
