package com.echronos.expo.param.tenancysku;

import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * date2025/8/9 14:07
 */
@Data
public class EditTenancySkuParam {

    /**
     * 展会ID
     */
    @NotNull(message = "{NOTNILL.EXPOID}")
    private Integer expoId;

    /**
     * 店铺商品ID
     */
    @NotNull(message = "{NOTNULL.SHOP.SKU.ID}")
    private Integer shopSkuId;

    /**
     * 商品ID
     */
    @NotNull(message = "{NOTNULL.SKU.ID}")
    private Integer skuId;
}
