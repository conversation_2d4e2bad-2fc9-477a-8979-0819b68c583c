package com.echronos.expo.param;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025-08-04 14:09
 */
@Data
public class ExpoAttachmentFileListParam {

    /**
     * 展会ID
     */
    @NotNull(message = "展会ID不能为空")
    private Integer expoId;
    /**
     * 业务ID（展会ID/展位ID）等
     */
    @NotNull(message = "业务ID不能为空")
    private Integer businessId;
    /**
     * 文件
     */
    @NotNull(message = "文件不能为空")
    private List<ExpoAttachmentFileParam> fileList;

}
