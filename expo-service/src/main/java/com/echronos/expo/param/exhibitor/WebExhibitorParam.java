package com.echronos.expo.param.exhibitor;

import com.alibaba.fastjson.annotation.JSONField;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.echronos.commons.req.BasePageReq;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 建站查询展商入参
 * <AUTHOR>
 * date2025/8/6 17:03
 */
@Data
public class WebExhibitorParam extends BasePageReq {

    /**
     * 展会ID
     */
    @JSONField(serialzeFeatures= SerializerFeature.WriteMapNullValue)
    private Integer expoId;

    /**
     * 关键字搜索
     */
    private String keyword;

    /**
     * 排序类型：1.创建时间倒叙 2.创建时间正序 3.智能随机(默认创建时间倒叙)
     */
    @JSONField(serialzeFeatures= SerializerFeature.WriteMapNullValue)
    private Integer orderByType;

    /**
     * 展商ID集合
     */
    private List<Integer> idList;

}
