package com.echronos.expo.param;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @date 2025/8/15 17:41
 */
@Data
public class ExpoCommunicateParam {
    /**
     * 业务ID
     */
    @NotNull(message = "业务ID不能为空")
    private Integer businessId;
    /**
     * 业务类型：1展商公司 2需求方
     */
    @NotNull(message = "业务类型不能为空")
    private Integer businessType;
    /**
     * 租户ID
     */
    @NotBlank(message = "租户ID不能为空")
    private String tenantId;
}
