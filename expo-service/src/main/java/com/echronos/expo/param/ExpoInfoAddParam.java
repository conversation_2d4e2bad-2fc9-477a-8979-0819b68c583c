/**
 * Copyright © 2020 ECHRONOS Corp. All rights reserved.
 * This software is proprietary to and embodies the confidential
 * technology of ECHRONOS Corp.  Possession, use, or copying
 * of this software and media is authorized only pursuant to a
 * valid written license from ECHRONOS Corp or an authorized sublicensor.
 */
package com.echronos.expo.param;


import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.util.List;

/**
 * EchExpoInfo 实体类
 *
 * <AUTHOR>
 * @date 2025-05-13
 */
@Data
public class ExpoInfoAddParam {
    /**
     * 展会名称
     */
    @NotNull(message = "{NOT.NULL.EXPO.EXPONAME}")
    private String expoName;
    /**
     * 展会简称
     */
    private String shortName;
    /**
     * 国家code
     */
    @NotNull(message = "{NOT.NULL.EXPO.COUNTRYCODE}")
    private Integer countryCode;
    /**
     * 城市code
     */
    private Integer cityCode;
    /**
     * 展馆名称
     */
    @NotNull(message = "{NOT.NULL.EXPO.HALLNAME}")
    private String hallName;
    /**
     * 详细地址
     */
    private String address;
    /**
     * 举办方名称
     */
    @NotBlank(message = "{NOT.NULL.EXPO.ORGANIZER}")
    private String organizer;
    /**
     * 开始时间
     */
    @NotNull(message = "{NOT.NULL.EXPO.STARTTIME}")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime startTime;
    /**
     * 结束时间
     */
    @NotNull(message = "{NOT.NULL.EXPO.ENDTIME}")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime endTime;
    /**
     * remark
     */
    private String remark;
    /**
     * 参展手册
     */
    private List<ExpoAttachmentFileParam> handbookAttachmentFiles;
    /**
     * 海报模板
     */
    private List<ExpoAttachmentFileParam> templateAttachmentFiles;
}