package com.echronos.expo.param;

import com.echronos.commons.req.BasePageReq;
import com.echronos.iform.api.entity.CollectFieldSort;
import com.echronos.iform.api.entity.CollectFilter;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025-5-16 15:29:43
 */
@Data
public class BaseListFilterParam extends BasePageReq {

    /**
     * 搜索Key
     */
    private String keywords = "";

    /**
     * 筛选条件
     */
    private List<CollectFilterParam> filters;

    /**
     * 排序条件
     */
    private List<CollectFieldSort> sort;

    /**
     * 去除字符串两端的空格
     */
    public String getKeywords() {
        if (this.keywords != null) {
            return this.keywords.trim();
        }
        return null;
    }
}
