package com.echronos.expo.param.booth;

import com.echronos.expo.param.BaseListFilterParam;
import com.echronos.expo.param.CollectFilterParam;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025-08-04 20:04
 */
@Data
public class ExportBoothParam extends BaseListFilterParam {

    /**
     * 展会ID
     */
    @NotNull(message = "展会ID不能为空")
    private Integer expoId;
    /**
     * 展位ID集合
     */
    private List<Integer> boothIds;

}
