package com.echronos.expo.param;

import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @Date 2025/5/20 11:37
 * @ClassName ExpoAudienceSignParam
 */
@Data
public class ExpoAudienceSignParam {
    /**
     * 观众ID
     */
    @NotNull(message = "{NOT.NULL.EXPO.ID}")
    private Long id;
    /**
     * 展会id
     */
    @NotNull(message = "{NOT.NULL.EXPO}")
    private Long expoId;
    /**
     * 公司id
     */
    @NotNull(message = "{NOT.NULL.EXPO.COMPANYID}")
    private Integer companyId;
}
