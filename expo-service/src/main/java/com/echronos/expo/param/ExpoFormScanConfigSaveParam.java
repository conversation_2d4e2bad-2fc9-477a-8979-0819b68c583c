package com.echronos.expo.param;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025/5/15 9:33
 * @ClassName ExpoFormScanConfigSaveParam
 */
@Data
public class ExpoFormScanConfigSaveParam {

    /**
     * 表单ID
     */
    @NotNull(message = "{NOT.NULL.EXPO.ID}")
    private Long id;


    /**
     * 选择展示字段集合
     */
    @NotNull(message = "{NOT.NULL.EXPO.FIELD}")
    private List<String> fieldList;
}
