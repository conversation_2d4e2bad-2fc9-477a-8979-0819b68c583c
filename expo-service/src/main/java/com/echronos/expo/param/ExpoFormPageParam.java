package com.echronos.expo.param;

import com.echronos.commons.req.BasePageReq;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @Date 2025/5/13 19:54
 * @ClassName ExpoFormPageParam
 */
@Data
public class ExpoFormPageParam extends BasePageReq {

    /**
     * 展会id
     */
    @NotNull(message = "{NOT.NULL.EXPO}")
    private Long expoId;

    /**
     * 表单分组类型：1.观众相关表单  2.展商相关表单
     */
    private Integer formGroup;

    /**
     * 表单类型：1.观众注册表单，2.观众满意度调查 20.企业信息收集  21.会刊收集  22.满意度调查  23.其它
     */
    private Integer formType;


    /**
     * 查询关键字
     */
    private String keywords;
}
