package com.echronos.expo.param.exhibitor;

import com.echronos.expo.param.booth.ExportBoothParam;
import lombok.Data;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025-08-06 20:48
 */
@Data
public class EditExhibitorDetailParam {

    /**
     * 展商id
     */
    @NotNull(message = "{NOT.NULL.EXPO.ID}")
    private Integer id;
    /**
     * 业务员ID
     */
    @NotNull(message = "业务员ID不能为空")
    private Integer businessMemberId;
    /**
     *  租赁类型：10.未收集  20.不需要租赁  30.需要租赁
     */
    @NotNull(message = "租赁类型不能为空")
    private Integer leaseDemandType;
    /**
     * 展位ID集合
     */
    @NotNull(message = "展位ID不能为空")
    @Size(min = 1, message = "至少需要选择一个展位")
    private List<Integer> boothIds;
    /**
     * 备注
     */
    private String remark;

}
