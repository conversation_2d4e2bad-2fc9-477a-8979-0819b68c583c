package com.echronos.expo.param;

import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @Date 2025/5/15 16:46
 * @ClassName ExpoChannelConfigAddParam
 */
@Data
public class ExpoChannelConfigAddParam  {
    /**
     * 展会ID
     */
    @NotNull(message = "{NOT.NULL.EXPO}")
    private Long expoId;
    /**
     * 渠道ID
     */
    @NotNull(message = "{NOT.NULL.EXPO.CHANNEL}")
    private Long channelId;
    /**
     * 表单ID
     */
    @NotNull(message = "{NOT.NULL.EXPO.FORM}")
    private Long formId;
}
