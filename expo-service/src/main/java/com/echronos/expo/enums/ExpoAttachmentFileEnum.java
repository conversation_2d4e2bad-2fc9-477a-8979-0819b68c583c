package com.echronos.expo.enums;

import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2025-08-04 11:51
 */
public interface ExpoAttachmentFileEnum {

    /**
     * 展位类型
     */
    @Getter
    enum Type {
        EXPO_HANDBOOK(1, "展会手册"),
        EXPO_POSTER_TEMPLATE(2, "展会海报模板"),
        BOOTH_ORDER(3, "展位订单"),
        BOOTH_LAYOUT(4, "展位布局"),
        EXPO_APPOINT(5, "展会预约"),
        LEASE_ORDER(6, "租赁订单"),
        TRAVEL_ORDER(7, "商旅订单"),
        ;

        private Integer code;
        private String msg;

        Type(Integer code, String msg) {
            this.code = code;
            this.msg = msg;
        }
    }

}
