package com.echronos.expo.enums;

import com.echronos.commons.utils.MessageUtil;
import lombok.AllArgsConstructor;

/**
 * <AUTHOR>
 * @date 2025/8/9 11:01
 */
@AllArgsConstructor
public enum ExpoAppointmentStatusEnum {

    BE_CONFIRMED(10, "EXPO.APPOINTMENT.STATUS.BE_CONFIRMED"),
    ACCEPTED(20, "EXPO.APPOINTMENT.STATUS.ACCEPTED"),
    REJECTED(30, "EXPO.APPOINTMENT.STATUS.REJECTED"),
    CANCELLED(40, "EXPO.APPOINTMENT.STATUS.CANCELLED");

    private final Integer code;
    private final String name;

    public Integer getCode() {
        return code;
    }

    public String getName() {
        return MessageUtil.getMessage(this.name);
    }

    /**
     * 根据code获取name
     *
     * @param code
     * @return
     */
    public static String getNameByCode(Integer code) {
        for (ExpoAppointmentStatusEnum value : values()) {
            if (value.code.equals(code)) {
                return value.getName();
            }
        }
        return null;
    }
}
