package com.echronos.expo.enums;

import lombok.Getter;
import org.springframework.web.bind.annotation.GetMapping;

import java.util.Arrays;
import java.util.List;

/**
 * 表单枚举
 * <AUTHOR>
 * @date 2025-08-01 15:41
 */
public interface ExpoFormEnums {

    /**
     * 表单分组
     */
    @Getter
    enum FormGroup {
        AUDIENCE(1,"观众表单分组"),
        EXHIBITOR(2,"展商表单分组"),
        ;

        private Integer code;
        private String msg;

        FormGroup(Integer code, String msg) {
            this.code = code;
            this.msg = msg;
        }

        /**
         * 获取枚举
         * @return
         */
        public static FormGroup getByCode(Integer code) {
            return Arrays.stream(values()).filter(r -> r.getCode().equals(code)).findFirst().orElse(null);
        }
    }

    /**
     * 表单类型
     */
    @Getter
    enum FormType{
        AUDIENCE_REGISTER(1,1,"观众注册表单"),
        AUDIENCE_(1,2,"观众满意度调查"),

        EXHIBITOR_ENTERPRISE_INFO(2,20,"企业信息收集"),
        EXHIBITOR_JOURNAL_INFO(2,21,"会刊收集"),
        EXHIBITOR_SATISFACTION_SURVEY(2,22,"满意度调查"),
        EXHIBITOR_OTHER(2,23,"其它"),
        ;

        // 表单分组id
        private Integer groupId;
        private Integer code;
        private String msg;

        FormType(Integer groupId, Integer code, String msg) {
            this.groupId = groupId;
            this.code = code;
            this.msg = msg;
        }

        /**
         * 获取枚举
         * @return
         */
        public static FormType getByCode(Integer code) {
            return Arrays.stream(values()).filter(r -> r.getCode().equals(code)).findFirst().orElse(null);
        }
    }



}
