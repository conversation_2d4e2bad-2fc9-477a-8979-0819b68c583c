package com.echronos.expo.enums;

import com.echronos.commons.utils.MessageUtil;
import com.echronos.expo.vo.appoint.AppointmentPurposeVO;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2025/8/9 11:50
 */
public enum ExpoAppointPurposeEnum {

    // 展商预约观众目的类型
    EXHIBITOR_PRODUCT_DEMO(1, 1, "EXPO.EXHIBITOR.APPOINT.PURPOSE.PRODUCT_DEMO"),
    EXHIBITOR_BUSINESS_NEGOTIATION(2, 1, "EXPO.EXHIBITOR.APPOINT.PURPOSE.BUSINESS_NEGOTIATION"),
    EXHIBITOR_MARKET_EXCHANGE(3, 1, "EXPO.EXHIBITOR.APPOINT.PURPOSE.MARKET_EXCHANGE"),
    EXHIBITOR_SAMPLE_EXPERIENCE(4, 1, "EXPO.EXHIBITOR.APPOINT.PURPOSE.SAMPLE_EXPERIENCE"),
    EXHIBITOR_SUPPLY_CHAIN(5, 1, "EXPO.EXHIBITOR.APPOINT.PURPOSE.SUPPLY_CHAIN"),
    EXHIBITOR_OTHER(6, 1, "EXPO.EXHIBITOR.APPOINT.PURPOSE.OTHER"),

    // 观众预约展商目的类型
    AUDIENCE_PRODUCT_INQUIRY(1, 2, "EXPO.AUDIENCE.APPOINT.PURPOSE.PRODUCT_INQUIRY"),
    AUDIENCE_AGENT_APPLICATION(2, 2, "EXPO.AUDIENCE.APPOINT.PURPOSE.AGENT_APPLICATION"),
    AUDIENCE_SUPPLY_CHAIN(3, 2, "EXPO.AUDIENCE.APPOINT.PURPOSE.SUPPLY_CHAIN"),
    AUDIENCE_SAMPLE_COLLECTION(4, 2, "EXPO.AUDIENCE.APPOINT.PURPOSE.SAMPLE_COLLECTION"),
    AUDIENCE_MARKET_EXCHANGE(5, 2, "EXPO.AUDIENCE.APPOINT.PURPOSE.MARKET_EXCHANGE"),
    AUDIENCE_OTHER(6, 2, "EXPO.AUDIENCE.APPOINT.PURPOSE.OTHER");

    private final Integer code;
    /**
     * 1展商 2观众
     */
    private final Integer type;
    private final String name;

    ExpoAppointPurposeEnum(Integer code, Integer type, String name) {
        this.code = code;
        this.type = type;
        this.name = name;
    }

    public int getCode() {
        return code;
    }

    public Integer gettype() {
        return type;
    }

    public String getName() {
        return MessageUtil.getMessage(this.name);
    }

    /**
     * 根据类型和编码获取枚举值
     *
     * @param type
     * @param code
     * @return
     */
    public static ExpoAppointPurposeEnum getByTypeAndCode(Integer type, Integer code) {
        for (ExpoAppointPurposeEnum purpose : values()) {
            if (purpose.type.equals(type) && Objects.equals(purpose.code, code)) {
                return purpose;
            }
        }
        return null;
    }

    /**
     * 根据类型枚举值
     *
     * @param type
     * @return
     */
    public static List<AppointmentPurposeVO> getByType(Integer type) {
        List<AppointmentPurposeVO> list = new ArrayList<>();
        for (ExpoAppointPurposeEnum purpose : values()) {
            if (purpose.type.equals(type)) {
                list.add(new AppointmentPurposeVO(purpose.code, purpose.getName()));
            }
        }
        return list;
    }
}
