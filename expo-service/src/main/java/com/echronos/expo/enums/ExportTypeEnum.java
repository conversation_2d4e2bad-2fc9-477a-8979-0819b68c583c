package com.echronos.expo.enums;

import com.echronos.commons.utils.MessageUtil;
import lombok.Getter;

/**
 * <AUTHOR>
 * @create 2020/4/23 14:07
 */
public interface ExportTypeEnum {

    /**
     * 导出枚举
     */
    @Getter
    enum ExportType{
        EXPORT_BOOTH(1, "EXPO.EXPORT.BOOTH"),
        EXPORT_EXHIBITOR(2, "EXPO.EXPORT.EXHIBITOR"),
        ;

        private Integer code;
        private String desc;

        ExportType(Integer code, String desc){
            this.code = code;
            this.desc = desc;
        }

        public String getDesc() {
            return MessageUtil.getMessage(this.desc);
        }

    }

}
