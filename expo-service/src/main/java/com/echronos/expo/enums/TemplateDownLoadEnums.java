package com.echronos.expo.enums;

import com.echronos.commons.utils.MessageUtil;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @Date 2025/5/19 14:56
 * @ClassName TemplateDownLoadEnums
 */
@Getter
@AllArgsConstructor
public enum TemplateDownLoadEnums {
    TEMPLATE_IMPORT_AUDIENCE(10000, "EXPO.AUDIENCE.TEMPLATE.DOWNLOAD"),
    TEMPLATE_EXPORT_AUDIENCE(10001, "EXPO.AUDIENCE.EXPORT.DOWNLOAD"),
    ;

    private Integer code;
    private String message;

    public String getMessage() {
        return MessageUtil.getMessage(this.message);
    }

}
