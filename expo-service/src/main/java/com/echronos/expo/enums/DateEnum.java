package com.echronos.expo.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 *
 * <AUTHOR>
 * date2025/8/14 9:39
 */
public interface DateEnum {

    /**
     * 时间筛选类型枚举
     */
    @Getter
    @AllArgsConstructor
    enum ScreeTypeEnum {

        THIS_YEAR(10, "本年度"),

        LAST_YEAR(20, "去年"),

        THIS_MONTH(30, "本月"),
        ;

        private Integer code;

        private String value;
    }
}
