package com.echronos.expo.enums;

import com.echronos.commons.utils.MessageUtil;
import lombok.Getter;

import java.util.Arrays;

/**
 * 展位枚举
 * <AUTHOR>
 * @date 2025-08-01 18:01
 */
public interface ExpoBoothEnums {

    /**
     * 展位类型
     */
    @Getter
    enum BoothType implements BaseEnum<Integer, String>{
        STANDARD(1, "EXPO.BOOTH.TYPE.STANDARD"),
        LIGHT(2, "EXPO.BOOTH.TYPE.LIGHT"),
        CORNER(3, "EXPO.BOOTH.TYPE.CORNER"),
        HALF_OPEN(4, "EXPO.BOOTH.TYPE.HALF_OPEN"),
        ISLAND(5, "EXPO.BOOTH.TYPE.ISLAND"),
        DOUBLE_OPEN(6, "EXPO.BOOTH.TYPE.DOUBLE_OPEN")
        ;

        private Integer code;
        private String msg;

        BoothType(Integer code, String msg) {
            this.code = code;
            this.msg = msg;
        }

        public String getMsg() {
            return MessageUtil.getMessage(this.msg);
        }

        public static ExpoBoothEnums.BoothType getMsgByCode(Integer code) {
            return Arrays.stream(values()).filter(r -> r.code.equals(code)).findFirst().orElse(null);
        }


        public static ExpoBoothEnums.BoothType getByMsg(String msg) {
            return Arrays.stream(values()).filter(r -> MessageUtil.getMessage(r.getMsg()).equals(msg)).findFirst().orElse(null);
        }

        @Override
        public Integer code() {
            return code;
        }

        @Override
        public String msg() {
            return MessageUtil.getMessage(this.msg);
        }
    }


    /**
     * 出售状态
     */
    @Getter
    enum Status implements BaseEnum<Integer, String>{
        NOT_SOLD(10, "EXPO.BOOTH.STATUS.IDLE"),
        SOLD(20, "EXPO.BOOTH.STATUS.SOLD")
        ;

        private Integer code;
        private String msg;

        Status(Integer code, String msg) {
            this.code = code;
            this.msg = msg;
        }

        public String getMsg() {
            return MessageUtil.getMessage(this.msg);
        }

        /**
         * 获取枚举
         * @return
         */
        public static ExpoBoothEnums.Status getByCode(Integer code) {
            return Arrays.stream(values()).filter(r -> r.getCode().equals(code)).findFirst().orElse(null);
        }


        @Override
        public Integer code() {
            return code;
        }

        @Override
        public String msg() {
            return MessageUtil.getMessage(this.msg);
        }
    }

    /**
     * 商品规格类型
     */
    @Getter
    enum GoodsStandType{
        TYPE(1,"EXPO.BOOTH.GOODS.TYPE.TYPE"),
        SIZE(2,"EXPO.BOOTH.GOODS.TYPE.SIZE"),
        UNIT(3,"EXPO.BOOTH.GOODS.TYPE.UNIT"),

        ;

        private Integer code;
        private String msg;

        GoodsStandType(Integer code, String msg) {
            this.code = code;
            this.msg = msg;
        }

        public String getMsg() {
            return MessageUtil.getMessage(this.msg);
        }

        public static ExpoBoothEnums.GoodsStandType getMsgByCode(Integer code) {
            return Arrays.stream(values()).filter(r -> r.code.equals(code)).findFirst().orElse(null);
        }
    }
}
