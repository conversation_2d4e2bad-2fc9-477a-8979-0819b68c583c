package com.echronos.expo.enums;

import com.echronos.commons.utils.MessageUtil;
import lombok.Getter;

import java.util.Arrays;

/**
 * <AUTHOR>
 * @date 2025-08-06 19:25
 */
public interface ExpoExhibitorEnums {

    /**
     * 合同状态
     */
    @Getter
    enum ContractStatus implements BaseEnum<Integer, String> {
        TO_SIGN(10, "EXPO.EXHIBITOR.CONTRACT_STATUS.TO_SIGN"),
        SIGNED(20, "EXPO.EXHIBITOR.CONTRACT_STATUS.SIGNED");

        private final Integer code;
        private final String msg;

        ContractStatus(Integer code, String msg) {
            this.code = code;
            this.msg = msg;
        }

        public String getMsg() {
            return MessageUtil.getMessage(this.msg);
        }

        public static ContractStatus getByCode(Integer code) {
            return Arrays.stream(values()).filter(r -> r.code.equals(code)).findFirst().orElse(null);
        }

        @Override
        public Integer code() {
            return code;
        }

        @Override
        public String msg() {
            return MessageUtil.getMessage(this.msg);
        }
    }

    /**
     * 邮件发送状态
     */
    @Getter
    enum SendEmailStatus implements BaseEnum<Integer, String> {
        NOT_SENT(10, "EXPO.EXHIBITOR.SEND_EMAIL_STATUS.NOT_SENT"),
        SENT(20, "EXPO.EXHIBITOR.SEND_EMAIL_STATUS.SENT");

        private final Integer code;
        private final String msg;

        SendEmailStatus(Integer code, String msg) {
            this.code = code;
            this.msg = msg;
        }

        public String getMsg() {
            return MessageUtil.getMessage(this.msg);
        }

        public static SendEmailStatus getByCode(Integer code) {
            return Arrays.stream(values()).filter(r -> r.code.equals(code)).findFirst().orElse(null);
        }

        @Override
        public Integer code() {
            return code;
        }

        @Override
        public String msg() {
            return MessageUtil.getMessage(this.msg);
        }
    }

    /**
     * 企业信息收集状态
     */
    @Getter
    enum EnterpriseInfoStatus implements BaseEnum<Integer, String> {
        TO_SUBMIT(10, "EXPO.EXHIBITOR.ENTERPRISE_INFO_STATUS.TO_SUBMIT"),
        TO_AUDIT(20, "EXPO.EXHIBITOR.ENTERPRISE_INFO_STATUS.TO_AUDIT"),
        REJECTED(30, "EXPO.EXHIBITOR.ENTERPRISE_INFO_STATUS.REJECTED"),
        APPROVED(40, "EXPO.EXHIBITOR.ENTERPRISE_INFO_STATUS.APPROVED");

        private final Integer code;
        private final String msg;

        EnterpriseInfoStatus(Integer code, String msg) {
            this.code = code;
            this.msg = msg;
        }

        public String getMsg() {
            return MessageUtil.getMessage(this.msg);
        }

        public static EnterpriseInfoStatus getByCode(Integer code) {
            return Arrays.stream(values()).filter(r -> r.code.equals(code)).findFirst().orElse(null);
        }

        @Override
        public Integer code() {
            return code;
        }

        @Override
        public String msg() {
            return MessageUtil.getMessage(this.msg);
        }
    }

    /**
     * 会刊信息收集状态
     */
    @Getter
    enum JournalInfoStatus implements BaseEnum<Integer, String> {
        TO_SUBMIT(10, "EXPO.EXHIBITOR.JOURNAL_INFO_STATUS.TO_SUBMIT"),
        TO_AUDIT(20, "EXPO.EXHIBITOR.JOURNAL_INFO_STATUS.TO_AUDIT"),
        REJECTED(30, "EXPO.EXHIBITOR.JOURNAL_INFO_STATUS.REJECTED"),
        APPROVED(40, "EXPO.EXHIBITOR.JOURNAL_INFO_STATUS.APPROVED");

        private final Integer code;
        private final String msg;

        JournalInfoStatus(Integer code, String msg) {
            this.code = code;
            this.msg = msg;
        }

        public String getMsg() {
            return MessageUtil.getMessage(this.msg);
        }

        public static JournalInfoStatus getByCode(Integer code) {
            return Arrays.stream(values()).filter(r -> r.code.equals(code)).findFirst().orElse(null);
        }

        @Override
        public Integer code() {
            return code;
        }

        @Override
        public String msg() {
            return MessageUtil.getMessage(this.msg);
        }
    }

    /**
     * 租赁类型
     */
    @Getter
    enum LeaseDemandType implements BaseEnum<Integer, String> {
        NOT_COLLECTED(10, "EXPO.EXHIBITOR.LEASE_DEMAND_TYPE.NOT_COLLECTED"),
        NOT_NEEDED(20, "EXPO.EXHIBITOR.LEASE_DEMAND_TYPE.NOT_NEEDED"),
        NEEDED(30, "EXPO.EXHIBITOR.LEASE_DEMAND_TYPE.NEEDED");

        private final Integer code;
        private final String msg;

        LeaseDemandType(Integer code, String msg) {
            this.code = code;
            this.msg = msg;
        }

        public String getMsg() {
            return MessageUtil.getMessage(this.msg);
        }

        public static LeaseDemandType getByCode(Integer code) {
            return Arrays.stream(values()).filter(r -> r.code.equals(code)).findFirst().orElse(null);
        }

        @Override
        public Integer code() {
            return code;
        }

        @Override
        public String msg() {
            return MessageUtil.getMessage(this.msg);
        }
    }


}
