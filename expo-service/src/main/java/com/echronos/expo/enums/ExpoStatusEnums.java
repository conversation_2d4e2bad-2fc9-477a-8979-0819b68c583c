package com.echronos.expo.enums;

import com.echronos.commons.utils.MessageUtil;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

/**
 * 展会状态
 *
 * <AUTHOR>
 * @Date 2025/5/13 20:05
 * @ClassName ExpoStatusEnums
 */
@Getter
@AllArgsConstructor
public enum ExpoStatusEnums {
    UN_STARTED(0, "EXPO.STATUS.UNSTARTED"),
    STARTED(1, "EXPO.STATUS.STARTED"),
    ENDED(2, "EXPO.STATUS.ENDED"),
    ;
    private Integer code;
    private String status;

    /**
     * 获取枚举
     *
     * @param statusCode
     * @return
     */
    public static ExpoStatusEnums getByCode(Integer statusCode) {
        return Arrays.stream(values()).filter(r -> r.getCode().equals(statusCode)).findFirst().orElse(null);
    }

    /**
     * Returns this message object.
     *
     * @return this message
     */
    public String getMessage() {
        //return message;
        return MessageUtil.getMessage(this.status);
    }
}
