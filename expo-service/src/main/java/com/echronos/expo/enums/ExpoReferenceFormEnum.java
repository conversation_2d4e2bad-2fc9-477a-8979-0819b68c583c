package com.echronos.expo.enums;

import com.echronos.commons.utils.MessageUtil;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2025-08-08 17:41
 */
public interface ExpoReferenceFormEnum {

    /**
     * 审核状态：0.待审核 1.审核拒绝 2.审核通过
     */
    @Getter
    enum AuditStatus{
        WAIT_AUDIT(0, "EXPO.REFERENCE.FORM.AUDIT.STATUS.WAIT_AUDIT"),
        AUDIT_REJECT(1, "EXPO.REFERENCE.FORM.AUDIT.STATUS.REJECT"),
        AUDIT_PASS(2, "EXPO.REFERENCE.FORM.AUDIT.STATUS.PASS");

        private Integer code;
        private String msg;

        AuditStatus(Integer code, String msg){
            this.code = code;
            this.msg = msg;
        }

        public static AuditStatus getEnumByCode(Integer code) {
            for (AuditStatus auditStatus : AuditStatus.values()) {
                if (auditStatus.code.equals(code)) {
                    return auditStatus;
                }
            }
            return null;
        }

        public String getMsg() {
            return MessageUtil.getMessage(this.msg);
        }

    }

}
