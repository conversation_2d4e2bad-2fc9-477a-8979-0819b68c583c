package com.echronos.expo.enums;

import com.echronos.commons.enums.CommonResultCode;
import com.echronos.commons.utils.MessageUtil;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2025-5-13 15:54
 */
public interface ExpoResultCode extends CommonResultCode {

    @Getter
    @AllArgsConstructor
    public static enum ExpoResultEnum {
        EXPO_IS_NOT_EXIST(10001, "EXPO.IS.NOT.EXIST"),
        EXPO_CANT_EDIT(10002, "EXPO.CAN.NOT.EDIT"),
        EXPO_STATUS_UNSTART_EXPIRE(10003, "EXPO.STATUS.UNSTART.EXPIRE"),
        EXPO_FORM_IS_NOT_EXIST(20001, "EXPO.FORM.IS.NOT.EXIST"),
        EXPO_FORM_CAN_NOT_CREATE(20002, "EXPO.FORM.CAN.NOT.CREATE"),
        EXPO_FORM_CAN_NOT_DEL(20003, "EXPO.FORM.CAN.NOT.DEL"),
        EXPO_CHANNEL_IS_NOT_EXIST(30001, "EXPO.CHANNEL.IS.NOT.EXIST"),
        EXPO_CHANNEL_CONFIG_IS_EXIST(30002, "EXPO.CHANNEL.CONFIG.IS.EXIST"),
        EXPO_CHANNEL_CONFIG_NOT_EXIST(30003, "EXPO.CHANNEL.CONFIG.IS.NOT.EXIST"),
        EXPO_CHANNEL_CONFIG_CANT_DEL(30004, "EXPO.CHANNEL.CONFIG.CANT.DEL"),
        EXPO_CHANNEL_IS_EXIST(30005, "EXPO.CHANNEL.IS.EXIST"),
        EXPO_AUDIENCE_IS_NOT_EXIST(40001, "EXPO.AUDIENCE.IS.NOT.EXIST"),
        EXPO_AUDIENCE_NAME_IS_NOT_NULL(40002, "EXPO.AUDIENCE.NAME.IS.NOT.NULL"),
        EXPO_AUDIENCE_EMAIL_IS_NOT_NULL(40003, "EXPO.AUDIENCE.EMAIL.IS.NOT.NULL"),
        EXPO_AUDIENCE_PHONE_IS_NOT_NULL(40004, "EXPO.AUDIENCE.PHONE.IS.NOT.NULL"),
        EXPO_AUDIENCE_EMAIL_FORMAT_ERROR(40004, "EXPO.AUDIENCE.EMAIL.FORMAT.ERROR"),
        EXPO_AUDIENCE_COMPANY_IS_NOT_NULL(40005, "EXPO.AUDIENCE.COMPANY.IS.NOT.NULL"),
        EXPO_AUDIENCE_INDUSTRY_IS_NOT_NULL(40006, "EXPO.AUDIENCE.INDUSTRY.IS.NOT.NULL"),
        EXPO_AUDIENCE_PRODUCT_SERVICE_IS_NOT_NULL(40007, "EXPO.AUDIENCE.PRODUCT_SERVICE.IS.NOT.NULL"),
        EXPO_AUDIENCE_POSITION_IS_NOT_NULL(40008, "EXPO.AUDIENCE.POSITION.IS.NOT.NULL"),
        EXPO_AUDIENCE_REVENUE_IS_NOT_NULL(40009, "EXPO.AUDIENCE.revenue.IS.NOT.NULL"),
        EXPO_AUDIENCE_EMAIL_REPEAT_ERROR(40010, "EXPO.AUDIENCE.EMAIL.REPEAT.ERROR"),
        EXPO_AUDIENCE_IS_EXIST(40011, "EXPO.AUDIENCE.IS.EXIST"),
        EXPO_AUDIENCE_PHONE_FORMAT_ERROR(40012, "EXPO.PHONE.FORMAT.ERROR"),
        EXPO_AUDIENCE_ASBUYER_VALUES(40013, "EXPO.AUDIENCE.ASBUYER.VALUES"),
        EXPO_AUDIENCE_EMAIL_LENGTH_TOO_LONG(40014, "EXPO.AUDIENCE.EMAIL.LENGTH.TOO.LONG"),
        EXPO_FROM_TYPE_ERROR(40015, "EXPO.FROM.TYPE.ERROR"),
        EXPO_FROM_GROUP_TYPE_ERROR(40016, "EXPO.FROM.GROUP.TYPE.ERROR"),
        EXPO_CHANNEL_RELATION_FORM_TYPE_ERROR(40017, "EXPO.CHANNEL.RELATION.FORM.TYPE.ERROR"),
        EXPO_BOOTH_INIT_SKU_ERROR(40018, "EXPO.BOOTH.INIT.SKU.ERROR"),
        //展商不存在
        EXPO_EXHIBITOR_IS_NOT_EXIST(40019, "EXPO.EXHIBITOR.IS.NOT.EXIST"),
        //租赁商品信息不存在
        EXPO_TENANCY_PRODUCT_IS_NOT_EXIST(40020, "EXPO.TENANCY.PRODUCT.IS.NOT.EXIST"),
        //展会ID或展商ID不能为空
        EXPO_EXHIBITOR_ID_OR_EXPO_ID_IS_NULL(40021, "EXPO.EXHIBITOR.ID.OR.EXPO.ID.IS.NULL"),
        EXPO_INVALID_PRODUCT(40022, "EXPO.INVALID.PRODUCT"),
        EXPO_CUSTOMER_NOT_FOUND(40023, "EXPO.CUSTOMER.NOT.FOUND"),
        EXPO_DISCOUNT_AMOUNT_EXCEED(40024, "EXPO.DISCOUNT.AMOUNT.EXCEED"),
        EXPO_BOOTH_NOT_FOUND(40024, "EXPO.BOOTH.NOT.FOUND"),
        EXPO_BOOTH_ALREADY_SOLD(40024, "EXPO.BOOTH.ALREADY.SOLD"),
        EXPO_FORM_TYPE_EXIST(40025, "EXPO.FORM.TYPE.EXIST"),
        EXPO_FORM_IS_DISABLED(40026, "EXPO.FORM.IS.DISABLED"),
        EXPO_FORM_ALREADY_EXISTS(40027, "EXPO.FORM.ALREADY.EXISTS"),
        EXPO_AUDIENCE_NOT_EXIST(40028, "EXPO.AUDIENCE.NOT.EXIST"),
        EXPO_EXHIBITOR_NOT_EXIST(40029, "EXPO.EXHIBITOR.NOT.EXIST"),
        EXPO_DATA_NO_PERMISSION(40030, "EXPO.DATA.NO.PERMISSION"),
        EXPO_FORM_NOT_EXIST_OR_UNBOUND(40031, "EXPO.FORM.NOT.EXIST.OR.UNBOUND"),
        EXPO_FORM_NOT_BELONG_TO_EXPO(40032, "EXPO.FORM.NOT.BELONG.TO.EXPO"),
        //当前登陆公司不属于该展会的展商
        EXPO_COMPANY_NOT_BELONG_TO_EXHIBITOR(40033, "EXPO.COMPANY.NOT.BELONG.TO.EXHIBITOR"),
        

        ;

        private Integer code;

        private String message;

        /**
         * Returns this message object.
         *
         * @return this message
         */
        public String getMessage() {
            //return message;
            return MessageUtil.getMessage(this.message);
        }

    }

}
