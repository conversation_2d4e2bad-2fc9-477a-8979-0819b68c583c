package com.echronos.expo.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 数据属性信息
 *
 * <AUTHOR>
 * @date 2025-5-16 17:28
 */
@Getter
@AllArgsConstructor
public enum FormFieldEnums {
    IMAGES("image", "图片"),
    DATE("date", "日期"),
    RADIO("radio", "单选框"),
    SELECT("select", "选项框"),
    MONEY("money", "金额"),
    NUMBER("number", "数值"),
    TEXT("text", "文本"),
    CHECKBOX("checkbox", "复选框"),
    TEXTAREA("textarea", "文本域"),
    PHONE("phone", "手机号"),
    CHILD_FORM("childForm", "子表单"),
    AREA("area", "区域"),
    ATTACHMENT("attachment", "附件"),
    WAREHOUSE("warehouse", "仓库"),
    PROCUREMENT_TYPE("procurementType", "采购类型"),
    INSTRUCTION("instruction", "用法说明");
    private String type;
    private String desc;


    public static FormFieldEnums getByType(String type) {
        for (FormFieldEnums enums : FormFieldEnums.values()) {
            if (enums.getType().equals(type)) {
                return enums;
            }
        }
        return TEXT;
    }
}
