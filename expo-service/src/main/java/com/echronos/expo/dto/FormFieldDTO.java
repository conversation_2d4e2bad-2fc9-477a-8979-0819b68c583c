package com.echronos.expo.dto;

import lombok.Data;

/**
 * <AUTHOR>
 * @date 2022/9/21
 */
@Data
public class FormFieldDTO {
    /**
     * 字段
     */
    private String colName;

    /**
     * 字段名称
     */
    private String label;

    /**
     * 是否必填
     */
    private Boolean required;

    /**
     * 是否选中
     */
    private Boolean selected;

    /**
     * [
     * "text",
     * "textarea",
     * "number",
     * "date",
     * "radio",
     * "checkbox",
     * "image",
     * "attachment",
     * "childForm",
     * "area",
     * "select",
     * "empty",
     * "instruction"
     * ]
     *
     * @return
     */
    private String type;

    /**
     * 分组信息(1:基本信息，2：其它资料，3:归属资料,4:系统信息）
     */
    private Integer group;
    /**
     * 父级字段
     *
     * @return
     */
    private String parentColName;

}
