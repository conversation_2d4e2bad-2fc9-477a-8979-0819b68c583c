package com.echronos.expo.dto;

import com.echronos.expo.annotation.FormField;
import lombok.Data;

/**
 * 观众表单设置
 *
 * <AUTHOR>
 * @date 2025-5-14 16:16:52
 */
@Data
public class AudienceFieldDTO {
    /**
     * 姓名
     */
    @FormField(label = "Name", selected = true, required = true)
    private String name;
    /**
     * 手机号
     */
    @FormField(label = "Mobile", selected = true, required = true)
    private String phone;
    /**
     * 邮箱
     */
    @FormField(label = "E-mail", selected = true, required = true)
    private String eMail;
    /**
     * 公司
     */
    @FormField(label = "Company", selected = true, required = true)
    private String companyName;
    /**
     * 公司官网
     */
    @FormField(label = "Company Website")
    private String companyWebsite;
    /**
     * 贵公司的主要行业是什么？
     */
    @FormField(label = "What is your company's main industry?", selected = true, required = true, type = "radio")
    private String industryCode;

    /**
     * 您想注册成为专业买家吗？
     */
    @FormField(label = "Would you like to register as a professional buyer?", selected = true, type = "radio")
    private String asBuyer;

    /**
     * 您在展会上扮演什么角色？
     */
    @FormField(label = "What is your role at the exhibition?", selected = true, required = true, type = "radio")
    private String expoRole;
    /**
     * 您最感兴趣的是哪个展区？（可多选）
     */
    @FormField(label = "Which exhibition area interests you most? (Multiple choice)", selected = true, required = true, type = "checkbox")
    private String interests;

    /**
     * 您想参加哪些活动？（可多选）
     */
    @FormField(label = "Which activities would you like to participate in? (Multiple choice)", selected = true, required = true, type = "checkbox")
    private String participate;


    /**
     * 您有特定的采购或合作需求吗？
     */
    @FormField(label = "Do you have specific procurement or cooperation needs?", selected = true, required = true, type = "radio")
    private String procurementNeeds;


    /**
     * 您的公司规模是多少？
     */
    @FormField(label = "What is your company size?", selected = true, parentFiled = "asBuyer", required = true, type = "radio")
    private String companySize;

    /**
     * 您的职位是什么？
     */
    @FormField(label = "What is your position?", selected = true, required = true, parentFiled = "asBuyer", type = "radio")
    private String position;

    /**
     * 您在寻找什么产品或解决方案？（可多选）
     */
    @FormField(label = "What products or solutions are you looking for? (Multiple choice)", selected = true, parentFiled = "asBuyer", type = "checkbox")
    private String productService;
    /**
     * 您的采购时间表是怎样的？
     */
    @FormField(label = "What is your procurement timeline?", selected = true, required = true, parentFiled = "asBuyer", type = "radio")
    private String procurementTimeline;
    /**
     * 您的项目预算范围是多少？（美元）
     */
    @FormField(label = "What is your project budget range? (USD)", selected = true, parentFiled = "asBuyer", type = "radio")
    private String budget;
    /**
     * 您在采购决策中扮演什么角色？
     */
    @FormField(label = "What is your role in procurement decisions?", selected = true, parentFiled = "asBuyer", type = "radio")
    private String procurementRole;

    /**
     * 您对供应商的主要要求是什么？（可多选）？
     */
    @FormField(label = "What are your main requirements for suppliers? (Multiple choice)", selected = true, parentFiled = "asBuyer", type = "checkbox")
    private String requirements;
    /**
     * 您首选的合作模式是什么？
     */
    @FormField(label = "What is your preferred cooperation model?", selected = true, parentFiled = "asBuyer", type = "radio")
    private String cooperationModel;
    /**
     * 请简要描述您的具体需求或项目情况
     */
    @FormField(label = "Please briefly describe your specific needs or project situation", selected = true, parentFiled = "asBuyer")
    private String specificNeeds;
    /**
     * 您希望我们如何联系您？
     */
    @FormField(label = "How would you prefer us to contact you?", selected = true, parentFiled = "asBuyer", type = "radio")
    private String contactType;
    /**
     * 您在展会期间有什么计划？（可多选）
     */
    @FormField(label = "What are your plans during the exhibition? (Multiple choice)", selected = true, parentFiled = "asBuyer", type = "checkbox")
    private String expoPlans;
}
