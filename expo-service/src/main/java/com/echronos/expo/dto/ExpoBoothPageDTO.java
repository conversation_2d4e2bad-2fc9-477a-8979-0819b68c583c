package com.echronos.expo.dto;

import com.echronos.iform.api.entity.CollectFieldSort;
import com.echronos.iform.api.entity.CollectFilter;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025-08-01 18:46
 */
@Data
public class ExpoBoothPageDTO extends ExpoBoothDTO{

    /**
     * 分页页码
     */
    private Integer pageNo;
    /**
     * 显示数量
     */
    private Integer pageSize;
    /**
     * 搜索关键字
     */
    private String keywords;
    /**
     * 登录公司ID
     */
    private Integer companyId;
    /**
     * 筛选条件
     */
    private List<CollectFilter> filters;
    /**
     * 排序条件
     */
    private List<CollectFieldSort> sort;
    /**
     * 查询列名
     */
    private List<String> fieldList;
    /**
     * 查询条件
     */
    private String whereSqlStr;
    /**
     * 排序条件
     */
    private String sortStr;

}
