/**
 * Copyright © 2020 ECHRONOS Corp. All rights reserved.
 * This software is proprietary to and embodies the confidential
 * technology of ECHRONOS Corp.  Possession, use, or copying
 * of this software and media is authorized only pursuant to a
 * valid written license from ECHRONOS Corp or an authorized sublicensor.
 */
package com.echronos.expo.dto;


import com.echronos.expo.model.ExpoLeaseOrder;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * ExpoLeaseOrder  Dto 对象
 * 
 * <AUTHOR>
 * @date 2025-08-13
 */
@Data
public class ExpoLeaseOrderDTO extends ExpoLeaseOrder {

    /**
     *  主键id
     */
    private Integer id;
    /**
     *  展会id
     */
    private Integer expoId;
    /**
     *  展商id
     */
    private Integer exhibitorId;
    /**
     *  客户id
     */
    private Integer customerId;
    /**
     *  客户公司id
     */
    private Integer customerCompanyId;
    /**
     *  客户联系人id
     */
    private Integer customerContactsId;
    /**
     *  开单时间
     */
    private LocalDateTime orderTime;
    /**
     *  付款方式编号
     */
    private String payWayNo;
    /**
     *  业务员id
     */
    private Integer businessMemberId;
    /**
     *  项目id
     */
    private Integer projectId;
    /**
     *  备注
     */
    private String remark;
    /**
     *  订单编号
     */
    private Long orderNo;
    /**
     *  优惠金额
     */
    private BigDecimal discountAmount;
    /**
     * 附件
     */
    private List<ExpoAttachmentFileDTO> attachmentList;
    /**
     * 订单详情
     */
    private List<ExpoLeaseOrderDetailDTO> orderDetailList;
    /**
     * 公司ID
     */
    private Integer companyId;
    /**
     * 用户ID
     */
    private Integer userId;
    /**
     * 成员ID
     */
    private Integer memberId;

}