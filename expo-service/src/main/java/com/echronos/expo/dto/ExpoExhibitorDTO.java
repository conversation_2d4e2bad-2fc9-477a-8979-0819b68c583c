/**
 * Copyright © 2020 ECHRONOS Corp. All rights reserved.
 * This software is proprietary to and embodies the confidential
 * technology of ECHRONOS Corp.  Possession, use, or copying
 * of this software and media is authorized only pursuant to a
 * valid written license from ECHRONOS Corp or an authorized sublicensor.
 */
package com.echronos.expo.dto;


import com.echronos.expo.model.ExpoExhibitor;
import com.echronos.iform.api.entity.CollectFieldSort;
import com.echronos.iform.api.entity.CollectFilter;
import lombok.Data;
import java.math.BigDecimal;
import java.util.List;

/**
 * ExpoExhibitor  Dto 对象
 * 
 * <AUTHOR>
 * @date 2025-08-01
 */
@Data
public class ExpoExhibitorDTO extends ExpoExhibitor {

    /**
     * 页码
     */
    private Integer pageNo;
    /**
     * 每页数量
     */
    private Integer pageSize;
    /**
     * 关键字搜索
     */
    private String keyword;
    /**
     * 筛选条件
     */
    private List<CollectFilter> filters;
    /**
     * 排序条件
     */
    private List<CollectFieldSort> sort;
    /**
     * 查询列名
     */
    private List<String> fieldList;
    /**
     * 查询条件
     */
    private String whereSqlStr;
    /**
     * 排序条件
     */
    private String sortStr;
    /**
     * 公司ID
     */
    private Integer companyId;
    /**
     * 用户ID
     */
    private Integer userId;
    /**
     * 成员ID
     */
    private Integer memberId;
    /**
     *  客户名称
     */
    private String customerName;
    /**
     *  客户公司id
     */
    private Integer customerCompanyId;
    /**
     * 联系人名称
     */
    private String customerContactsName;
    /**
     * 联系人电话
     */
    private String customerContactsPhone;
    /**
     * 联系人邮箱
     */
    private String customerContactsEmail;
    /**
     * 跟进业务员名称
     */
    private String businessMemberName;
    /**
     * 展位ID集合
     */
    private List<Integer> boothIds;
    /**
     * 观众ID
     */
    private Integer expoAudienceId;
    /**
     * 排序类型：1.创建时间倒叙 2.创建时间正序 3.智能随机
     */
    private Integer orderByType;


    /**
     * 展商ID集合
     */
    private List<Integer> idList;

    /**
     * 租户ID
     */
    private String tenantId;
}