/**
 * Copyright © 2020 ECHRONOS Corp. All rights reserved.
 * This software is proprietary to and embodies the confidential
 * technology of ECHRONOS Corp.  Possession, use, or copying
 * of this software and media is authorized only pursuant to a
 * valid written license from ECHRONOS Corp or an authorized sublicensor.
 */
package com.echronos.expo.dto;


import com.echronos.expo.model.ExpoTenancySku;
import lombok.Data;

import java.math.BigDecimal;

/**
 * ExpoTenancySkuConfigure  Dto 对象
 * 
 * <AUTHOR>
 * @date 2025-08-09
 */
@Data
public class ExpoTenancySkuDTO extends ExpoTenancySku {

    /**
     * 用户ID
     */
    private Integer userId;

    /**
     * 成员ID
     */
    private Integer memberId;

    /**
     * 搜索内容
     */
    private String keyword;

    /**
     * 最低价格
     */
    private BigDecimal minPrice;

    /**
     * 最高价格
     */
    private BigDecimal maxPrice;

    /**
     * 页码
     */
    private Integer pageNo;

    /**
     * 条数
     */
    private Integer pageSize;
}