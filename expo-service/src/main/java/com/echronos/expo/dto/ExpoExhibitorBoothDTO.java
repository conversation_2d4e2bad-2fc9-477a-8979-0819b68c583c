/**
 * Copyright © 2020 ECHRONOS Corp. All rights reserved.
 * This software is proprietary to and embodies the confidential
 * technology of ECHRONOS Corp.  Possession, use, or copying
 * of this software and media is authorized only pursuant to a
 * valid written license from ECHRONOS Corp or an authorized sublicensor.
 */
package com.echronos.expo.dto;


import com.echronos.expo.model.ExpoExhibitorBooth;
import lombok.Data;

/**
 * ExpoExhibitorBooth  Dto 对象
 * 
 * <AUTHOR>
 * @date 2025-08-06
 */
@Data
public class ExpoExhibitorBoothDTO extends ExpoExhibitorBooth {

    /**
     * 展位ID
     */
    private Integer boothId;
    /**
     * 展位名称
     */
    private String boothName;
    /**
     * 展位楼层
     */
    private String boothFloor;
    /**
     * 展位区域
     */
    private String boothZone;
    /**
     * 展位号
     */
    private String boothNumber;
    /**
     * 展位类型
     */
    private Integer boothType;
    /**
     * 展位尺寸
     */
    private String dimensions;

}