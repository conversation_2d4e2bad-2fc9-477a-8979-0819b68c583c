package com.echronos.expo.dto;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025/5/16 10:59
 * @ClassName ExpoAudienceListDTO
 */
@Data
public class ExpoAudienceListDTO extends ExpoAudienceDTO {

    /**
     * 关键字
     */
    private String keywords;
    /**
     * 高级筛选返回的条件SQL
     */
    private String whereSqlStr = "";

    /**
     * 高级筛选返回的排序SQL
     */
    private String sortSqlStr = "";

    /**
     * 观众ID集合
     */
    private List<Integer> idList;


}
