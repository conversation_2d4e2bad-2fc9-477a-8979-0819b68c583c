package com.echronos.expo.dto;

import com.echronos.commons.req.BasePageReq;
import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 展会互动统计DTO
 *
 * <AUTHOR>
 * @date 2025/8/15 14:56
 */
@Data
public class ExpoInteractionStatisticsDTO extends BasePageReq {
    /**
     * 展会ID
     */
    private Integer expoId;
    /**
     * 展商ID
     */
    private Integer exhibitorId;
    /**
     * 观众ID
     */
    private Integer audienceId;
    /**
     * 业务ID
     */
    private Integer businessId;
    /**
     * 业务类型：1展商 2观众
     */
    private Integer businessType;
    /**
     * 预约人员ID
     */
    private Integer appointedPersonnelId;
    /**
     * 预约目的（展商/观众）
     */
    private Integer purposeType;
    /**
     * 备注
     */
    private String remark;
    /**
     * 状态：10.待确认 20.已接受 30.已拒绝 40.已取消
     */
    private Integer status;
    /**
     * 成员ID
     */
    private Integer memberId;
    /**
     * 公司ID
     */
    private Integer companyId;
    /**
     * 创建人
     */
    private Integer createUser;
    /**
     * 更新人
     */
    private Integer updateUser;
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    /**
     * 修改时间
     */
    private LocalDateTime updateTime;
    /**
     * 统计日期 (用于每小时互动分布统计)
     */
    private LocalDate statisticsDate;
}
