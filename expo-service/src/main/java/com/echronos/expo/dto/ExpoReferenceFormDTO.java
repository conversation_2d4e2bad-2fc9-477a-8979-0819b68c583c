/**
 * Copyright © 2020 ECHRONOS Corp. All rights reserved.
 * This software is proprietary to and embodies the confidential
 * technology of ECHRONOS Corp.  Possession, use, or copying
 * of this software and media is authorized only pursuant to a
 * valid written license from ECHRONOS Corp or an authorized sublicensor.
 */
package com.echronos.expo.dto;


import com.echronos.expo.manager.ExpoReferenceFormExtendManager;
import com.echronos.expo.model.ExpoReferenceForm;
import com.echronos.expo.model.ExpoReferenceFormExtend;
import lombok.Data;

import java.util.List;

/**
 * ExpoReferenceForm  Dto 对象
 * 
 * <AUTHOR>
 * @date 2025-08-08
 */
@Data
public class ExpoReferenceFormDTO extends ExpoReferenceForm {

    /**
     * 表单值
     */
    private List<ExpoReferenceFormExtendDTO> extendList;
    /**
     * 公司id
     */
    private Integer companyId;
    /**
     * 用户id
     */
    private Integer userId;

}