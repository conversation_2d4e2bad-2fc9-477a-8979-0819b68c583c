package com.echronos.expo.dto;

import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2025-08-13 19:31
 */
@Data
public class AddExhibitorOrderDetailDTO {

    /**
     *  主键id
     */
    private Integer id;
    /**
     *  订单id
     */
    private Integer orderId;
    /**
     *  数量
     */
    private BigDecimal quantity;
    /**
     *  报价价格（含税单价）
     */
    private BigDecimal price;
    /**
     *  未税单价
     */
    private BigDecimal noTaxPrice;
    /**
     *  税率
     */
    private BigDecimal taxRate;
    /**
     *  关联店铺商品id
     */
    private Integer shopSkuId;
    /**
     *  关联商品skuId
     */
    private Integer skuId;
    /**
     *  商品名称快照
     */
    private String name;
    /**
     *  商品编码快照
     */
    private String skuCode;
    /**
     *  商品规格快照
     */
    private String standardJson;
    /**
     *  商品市场价快照
     */
    private BigDecimal marketPrice;
    /**
     *  商品单位快照
     */
    private String unit;

}
