package com.echronos.expo.dto;

import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025-08-13 19:31
 */
@Data
public class AddExhibitorOrderDTO {

    /**
     * 展商ID
     */
    private Integer id;
    /**
     * 展会id
     */
    private Integer expoId;
    /**
     * 展商id
     */
    private Integer exhibitorId;
    /**
     *  客户id
     */
    private Integer customerId;
    /**
     *  客户公司id
     */
    private Integer customerCompanyId;
    /**
     *  客户联系人id
     */
    private Integer customerContactsId;
    /**
     *  开单时间
     */
    private LocalDateTime orderTime;
    /**
     *  付款方式编号
     */
    private String payWayNo;
    /**
     *  业务员id
     */
    private Integer businessMemberId;
    /**
     *  项目id
     */
    private Integer projectId;
    /**
     *  备注
     */
    private String remark;
    /**
     *  订单编号
     */
    private Long orderNo;
    /**
     *  总金额
     */
    private BigDecimal totalAmount;
    /**
     *  优惠金额
     */
    private BigDecimal discountAmount;

    /**
     * 附件
     */
    private List<ExpoAttachmentFileDTO> attachmentList;

    /**
     * 订单明细列表
     */
    private List<AddExhibitorOrderDetailDTO> orderDetailList;

    /**
     * 公司ID
     */
    private Integer companyId;
    /**
     * 用户ID
     */
    private Integer userId;
    /**
     * 成员ID
     */
    private Integer memberId;

}
