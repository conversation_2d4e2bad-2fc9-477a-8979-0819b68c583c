/**
 * Copyright © 2020 ECHRONOS Corp. All rights reserved.
 * This software is proprietary to and embodies the confidential
 * technology of ECHRONOS Corp.  Possession, use, or copying
 * of this software and media is authorized only pursuant to a
 * valid written license from ECHRONOS Corp or an authorized sublicensor.
 */
package com.echronos.expo.dto;


import com.echronos.expo.model.ExpoInfo;
import com.echronos.expo.param.ExpoAttachmentFileParam;
import lombok.Data;

import java.util.List;

/**
 * EchExpoInfo  Dto 对象
 * 
 * <AUTHOR>
 * @date 2025-05-13
 */
@Data
public class ExpoInfoDTO extends ExpoInfo {

    /**
     * 参展手册
     */
    private List<ExpoAttachmentFileDTO> handbookAttachmentFiles;
    /**
     * 海报模板
     */
    private List<ExpoAttachmentFileDTO> templateAttachmentFiles;

    /**
     * 页码
     */
    private Integer pageNo;

    /**
     * 条数
     */
    private Integer pageSize;
}