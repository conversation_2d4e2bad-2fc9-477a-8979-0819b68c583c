package com.echronos.expo.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025-08-04 16:33
 */
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
public class ExpoBoothImportVO {

    /**
     * 总计
     */
    private Integer totalCount;
    /**
     * 校验成功数量
     */
    private Integer successCount;
    /**
     * 校验失败数量
     */
    private Integer errorCount;
    /**
     * 执行校验类型：1.数据为空  2.存在异常数据-导入不执行  3.存在异常数据-导入执行成功  4.执行成功
     */
    private Integer executeType;
    /**
     * 错误信息
     */
    private List<ImportErrorVO> errorMsgList;

}
