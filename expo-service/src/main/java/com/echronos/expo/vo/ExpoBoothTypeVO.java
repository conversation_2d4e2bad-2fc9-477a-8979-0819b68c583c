package com.echronos.expo.vo;

import lombok.Data;

/**
 * <AUTHOR>
 * date2025/8/11 16:01
 */
@Data
public class ExpoBoothTypeVO {

    /**
     * 展位类型：1.标准展位  2.光地 3.角位 4.半岛位 5.岛位 6.双开口位
     */
    private Integer boothType;

    /**
     * 展位类型名称
     */
    private String boothTypeName;

    /**
     * 未售展位数量
     */
    private Integer noSaleNumber;

    /**
     * 已售展位数量
     */
    private Integer saleNumber;
}
