/**
 * Copyright © 2020 ECHRONOS Corp. All rights reserved.
 * This software is proprietary to and embodies the confidential
 * technology of ECHRONOS Corp.  Possession, use, or copying
 * of this software and media is authorized only pursuant to a
 * valid written license from ECHRONOS Corp or an authorized sublicensor.
 */
package com.echronos.expo.vo;


import lombok.Data;

import java.time.LocalDateTime;

/**
 * ExpoCommunicateGroup controller层返回值
 * 
 * <AUTHOR>
 * @date 2025-08-15
 */
@Data
public class ExpoCommunicateGroupVO{


    /**
     *  主键 
     */
    private Integer id;
    /**
     *  业务ID 
     */
    private Integer businessId;
    /**
     *  业务类型：1展商公司 2需求方 
     */
    private Integer businessType;
    /**
     *  群聊ID 
     */
    private Integer groupId;
    /**
     *  会话ID 
     */
    private Integer sessionId;
    /**
     *  公司ID 
     */
    private Integer companyId;
    /**
     *  创建人 
     */
    private Integer createUser;
    /**
     *  更新人 
     */
    private Integer updateUser;
    /**
     *  创建时间 
     */
    private LocalDateTime createTime;
    /**
     *  修改时间 
     */
    private LocalDateTime updateTime;
    /**
     *  是否已删除：0否 1是 
     */
    private Integer isDeleted;
}