package com.echronos.expo.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * ExpoPageVO
 *
 * <AUTHOR>
 * date2025/8/6 19:39
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ExpoPageVO<T> {

    /**
     * 总条数
     */
    private long totalElements;

    /**
     * 总页数
     */
    private long totalPages;

    /**
     * 列表
     */
    protected List<T> records;

}
