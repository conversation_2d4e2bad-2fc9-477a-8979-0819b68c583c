/**
 * Copyright © 2020 ECHRONOS Corp. All rights reserved.
 * This software is proprietary to and embodies the confidential
 * technology of ECHRONOS Corp.  Possession, use, or copying
 * of this software and media is authorized only pursuant to a
 * valid written license from ECHRONOS Corp or an authorized sublicensor.
 */
package com.echronos.expo.vo.appoint;


import lombok.Data;

import java.time.LocalDateTime;

/**
 * ExpoAppointmentTime controller层返回值
 *
 * <AUTHOR>
 * @date 2025-08-07
 */
@Data
public class ExpoAppointmentTimeVO {


    /**
     * 主键
     */
    private Integer id;
    /**
     * 预约ID
     */
    private Integer appointmentId;
    /**
     * 预约时间ID
     */
    private Integer appointedTimeId;
    /**
     * 开始时间
     */
    private LocalDateTime startTime;
    /**
     * 结束时间
     */
    private LocalDateTime endTime;
}