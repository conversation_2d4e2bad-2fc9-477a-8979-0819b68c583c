package com.echronos.expo.vo;

import com.echronos.expo.dto.ExpoAudienceDTO;
import com.echronos.expo.model.ExpoAudience;
import lombok.Data;

import java.util.List;

/**
 * @Description 客户导入返回对象
 * <AUTHOR>
 * @Date 2021/8/16 16:50
 * @Version 1.0
 **/
@Data
public class AudienceImportResultVO {
    /**
     * 错误条数
     **/
    private Integer errorNum;
    /**
     * 成功条数
     **/
    private Integer successNum;
    /**
     * 总数
     **/
    private Integer total;

    /**
     * 校验结果提示
     */
    private List<ImportCheckMsgVO> msgList;

    /**
     * 观众信息
     */
    private List<ExpoAudience> audienceList;
}
