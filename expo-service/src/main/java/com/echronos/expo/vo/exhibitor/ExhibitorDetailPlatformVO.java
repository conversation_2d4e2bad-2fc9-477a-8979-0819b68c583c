package com.echronos.expo.vo.exhibitor;

import com.echronos.expo.vo.ExpoBoothDetailVO;
import com.echronos.expo.vo.ExpoExhibitorVO;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * 展商信息（平台查看）
 * <AUTHOR>
 * @date 2025-08-08 18:08
 */
@Data
public class ExhibitorDetailPlatformVO extends ExpoExhibitorVO {

    /**
     * 展位列表
     */
    private List<ExpoBoothDetailVO> boothList;

    /**
     * 展商表单信息
     */
    private List<ExhibitorFormVO> exhibitorFormList;

}
