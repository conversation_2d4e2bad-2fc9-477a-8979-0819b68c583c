package com.echronos.expo.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.annotation.security.DenyAll;

/**
 * <AUTHOR>
 * @date 2025-08-05 18:01
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class FieldFilterVO {

    /**
     * 筛选名称
     */
    private Integer label;
    /**
     * 筛选值
     */
    private String value;

}
