package com.echronos.expo.vo.tenancysku;

import com.alibaba.fastjson.annotation.JSONField;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.echronos.commons.utils.FilePathSerializer;
import com.echronos.search.api.resp.StandardResp;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * date2025/8/9 14:56
 */
@Data
public class ExpoTenancySkuVO {

    /**
     * 主键ID
     */
    private Integer id;

    /**
     * 店铺商品ID
     */
    private Integer shopSkuId;

    /**
     * 商品ID
     */
    private Integer skuId;

    /**
     * 规格列表
     */
    private List<StandardResp> standards;

    /**
     *  图片,json格式存储
     */
    @JSONField(serializeUsing = FilePathSerializer.class)
    private String images;

    /**
     *  商品名称
     */
    private String name;

    /**
     *  固定价格(市场价)
     */
    @JSONField(serialzeFeatures= SerializerFeature.WriteMapNullValue)
    private BigDecimal marketPrice;

    /**
     * 单位名称
     */
    private String unit;
}
