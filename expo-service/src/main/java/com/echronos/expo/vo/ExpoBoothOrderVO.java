/**
 * Copyright © 2020 ECHRONOS Corp. All rights reserved.
 * This software is proprietary to and embodies the confidential
 * technology of ECHRONOS Corp.  Possession, use, or copying
 * of this software and media is authorized only pursuant to a
 * valid written license from ECHRONOS Corp or an authorized sublicensor.
 */
package com.echronos.expo.vo;


import lombok.Data;
import lombok.EqualsAndHashCode;
import java.time.LocalDateTime;
import java.math.BigDecimal;

/**
 * ExpoBoothOrder controller层返回值
 * 
 * <AUTHOR>
 * @date 2025-08-01
 */
@Data
public class ExpoBoothOrderVO{


    /**
     *  主键id 
     */
    private Integer id;
    /**
     *  客户id 
     */
    private Integer customerId;
    /**
     *  客户公司id 
     */
    private Integer customerCompanyId;
    /**
     *  客户联系人id 
     */
    private Integer customerContactsId;
    /**
     *  开单时间 
     */
    private LocalDateTime orderTime;
    /**
     *  付款方式编号 
     */
    private String payWayNo;
    /**
     *  业务员id 
     */
    private Integer businessMemberId;
    /**
     *  项目id 
     */
    private Integer projectId;
    /**
     *  备注 
     */
    private String remark;
    /**
     *  订单编号 
     */
    private String orderNo;
    /**
     *  总金额 
     */
    private BigDecimal totalAmount;
    /**
     *  优惠金额 
     */
    private BigDecimal discountAmount;
    /**
     *  创建人 
     */
    private Integer createUser;
    /**
     *  创建时间 
     */
    private LocalDateTime createTime;
    /**
     *  更新人 
     */
    private Integer updateUser;
    /**
     *  更新时间 
     */
    private LocalDateTime updateTime;
    /**
     *  是否删除：0.否 1.是 
     */
    private Integer isDeleted;
}