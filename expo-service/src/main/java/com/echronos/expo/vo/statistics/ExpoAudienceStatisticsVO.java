package com.echronos.expo.vo.statistics;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * 观众注册统计
 * <AUTHOR>
 * @date 2025-08-15 17:34
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ExpoAudienceStatisticsVO {

    /**
     * 观众数量
     */
    private Integer audienceCount;
    /**
     * 签到观众数量
     */
    private Integer signInCount;
    /**
     * 签到率
     */
    private BigDecimal signInRate;
    /**
     * 签到观众数量（累计）
     */
    private Integer accumulatedSignInCount;

}
