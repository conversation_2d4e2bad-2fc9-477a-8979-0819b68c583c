package com.echronos.expo.vo.appoint;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * ExpoAppointedPersonnelTime controller层返回值
 *
 * <AUTHOR>
 * @date 2025-08-07
 */
@Data
public class ExpoAppointedPersonnelTimeVO {
    /**
     * 主键
     */
    private Integer id;
    /**
     * 预约人员ID
     */
    private Integer personnelId;
    /**
     * 开始时间
     */
    private LocalDateTime startTime;
    /**
     * 结束时间
     */
    private LocalDateTime endTime;
    /**
     * 是否可预约：0-不可预约，1-可预约
     */
    private Integer isAvailable;
}