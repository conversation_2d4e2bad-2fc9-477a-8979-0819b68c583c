/**
 * Copyright © 2020 ECHRONOS Corp. All rights reserved.
 * This software is proprietary to and embodies the confidential
 * technology of ECHRONOS Corp.  Possession, use, or copying
 * of this software and media is authorized only pursuant to a
 * valid written license from ECHRONOS Corp or an authorized sublicensor.
 */
package com.echronos.expo.vo;


import lombok.Data;

import java.time.LocalDateTime;

/**
 * ExpoChannel controller层返回值
 * 
 * <AUTHOR>
 * @date 2025-05-13
 */
@Data
public class ExpoChannelVO {

    /**
     *  ID 
     */
    private Long id;
    /**
     *  所属公司id 
     */
    private Integer companyId;
    /**
     *  渠道名称 
     */
    private String channelName;

    /**
     * 渠道类型
     * 1、观众
     * 2、参展商
     */
    private Integer channelType;
}