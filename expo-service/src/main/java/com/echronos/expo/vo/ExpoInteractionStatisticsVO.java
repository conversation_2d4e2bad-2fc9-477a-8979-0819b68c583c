package com.echronos.expo.vo;

import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2025/8/15 14:56
 */
@Data
public class ExpoInteractionStatisticsVO {

    /**
     * 总互动次数
     */
    private Integer totalInteractionCount;
    /**
     * 展商扫码次数
     */
    private Integer exhibitorScanCount;
    /**
     * 观众预约次数
     */
    private Integer audienceAppointmentCount;
    /**
     * 展商扫码转化率
     */
    private BigDecimal exhibitorScanConversionRate;
    /**
     * 观众预约转化率
     */
    private BigDecimal audienceAppointmentConversionRate;
}
