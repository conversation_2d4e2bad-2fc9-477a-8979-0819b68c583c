package com.echronos.expo.vo.statistics;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * 观众签到及未签到比率
 * <AUTHOR>
 * @date 2025-08-15 18:04
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ExpoAudienceSignNotSignInRateVO {

    /**
     * 签到比率
     */
    private BigDecimal signInRate;
    /**
     * 未签到比率
     */
    private BigDecimal notSignInRate;

}
