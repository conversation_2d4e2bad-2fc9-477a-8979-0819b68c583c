/**
 * Copyright © 2020 ECHRONOS Corp. All rights reserved.
 * This software is proprietary to and embodies the confidential
 * technology of ECHRONOS Corp.  Possession, use, or copying
 * of this software and media is authorized only pursuant to a
 * valid written license from ECHRONOS Corp or an authorized sublicensor.
 */
package com.echronos.expo.vo;


import com.alibaba.fastjson.annotation.JSONField;
import com.echronos.commons.utils.TimeSerializerUtils;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * ExpoAudience controller层返回值
 *
 * <AUTHOR>
 * @date 2025-05-15
 */
@Data
public class ExpoAudienceVO {


    /**
     * ID
     */
    private Long id;
    /**
     * 所属公司id
     */
    private Integer companyId;
    /**
     * 展会id
     */
    private Long expoId;
    /**
     * 渠道ID
     */
    private Long channelId;
    /**
     * 观众名称
     */
    private String name;
    /**
     * 手机号
     */
    private String phone;
    /**
     * 电子邮箱
     */
    private String email;
    /**
     * 公司名称
     */
    private String companyName;
    /**
     * 关联客户系统id
     */
    private Integer customerId;
    /**
     * 自定义表单code
     */
    private String formCode;
    /**
     * 自定义表单版本
     */
    private Integer versionNumber;
    /**
     * 创建时间
     */
    @JSONField(serializeUsing = TimeSerializerUtils.class)
    private LocalDateTime createTime;
    /**
     * 更新时间
     */
    @JSONField(serializeUsing = TimeSerializerUtils.class)
    private LocalDateTime updateTime;
    /**
     * 渠道名称
     */
    private String channelName;
    /**
     * 是否发送邮件
     * 0否
     * 1是
     */
    private String isSend;
    /**
     * 邮件发送次数
     */
    private Integer sendCount;
    /**
     * 最近发送时间
     */
    @JSONField(serializeUsing = TimeSerializerUtils.class)
    private LocalDateTime lastSendTime;
    /**
     * 是否签到
     * 0否
     * 1是
     */
    private String isSign;
    /**
     * 最近签到时间
     */
    @JSONField(serializeUsing = TimeSerializerUtils.class)
    private LocalDateTime lastSignTime;
    /**
     * 签到次数
     */
    private Integer signCount;
    /**
     * 扩展字段
     */
    private Map<String, String> extMap;

    /**
     * 配置展示字段
     */
    private List<String> fieldList;
}