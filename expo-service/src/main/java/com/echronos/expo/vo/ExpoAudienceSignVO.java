/**
 * Copyright © 2020 ECHRONOS Corp. All rights reserved.
 * This software is proprietary to and embodies the confidential
 * technology of ECHRONOS Corp.  Possession, use, or copying
 * of this software and media is authorized only pursuant to a
 * valid written license from ECHRONOS Corp or an authorized sublicensor.
 */
package com.echronos.expo.vo;


import lombok.Data;

import java.time.LocalDateTime;

/**
 * EchExpoAudienceSign controller层返回值
 *
 * <AUTHOR>
 * @date 2025-05-15
 */
@Data
public class ExpoAudienceSignVO {


    /**
     * ID
     */
    private Long id;
    /**
     * 所属公司ID
     */
    private Integer companyId;
    /**
     * 展会ID
     */
    private Long expoId;
    /**
     * 观众ID
     */
    private Long audienceId;
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
}