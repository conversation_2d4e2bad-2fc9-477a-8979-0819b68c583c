/**
 * Copyright © 2020 ECHRONOS Corp. All rights reserved.
 * This software is proprietary to and embodies the confidential
 * technology of ECHRONOS Corp.  Possession, use, or copying
 * of this software and media is authorized only pursuant to a
 * valid written license from ECHRONOS Corp or an authorized sublicensor.
 */
package com.echronos.expo.vo;


import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * ExpoAudience controller层返回值
 *
 * <AUTHOR>
 * @date 2025-05-15
 */
@Data
public class ExpoAudienceScanVO {
    /**
     * 前端生成二维码地址
     */
    private String qrcodeUrl;
    /**
     * 二维码图片base64
     */
    private String qrcodeImage;

    /**
     * ID
     */
    private Long id;
    /**
     * 展会id
     */
    private Long expoId;

    /**
     * 展会名称
     */
    private String expoName;
    /**
     * 展馆名称
     */
    private String hallName;
    /**
     * 开始时间
     */
    private LocalDateTime startTime;
    /**
     * 结束时间
     */
    private LocalDateTime endTime;
    /**
     * 观众名称
     */
    private String name;
    /**
     * 手机号
     */
    private String phone;
    /**
     * 电子邮箱
     */
    private String email;
    /**
     * 公司名称
     */
    private String companyName;
    /**
     * 扩展字段
     */
    private Map<String, String> extMap;
    /**
     * 配置展示字段
     */
    private List<String> fieldList;
}