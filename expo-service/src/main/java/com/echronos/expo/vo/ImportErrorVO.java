package com.echronos.expo.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025-08-04 16:42
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ImportErrorVO {

    /**
     * 行号
     */
    private Integer rowNum;
    /**
     * 字段对应的值
     */
    private Map<String, String> fieldValueMap;
    /**
     * 错误信息
     */
    private List<String> errorMsgList;

}
