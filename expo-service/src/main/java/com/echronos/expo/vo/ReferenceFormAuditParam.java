package com.echronos.expo.vo;

import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * 表单审核
 * <AUTHOR>
 * @date 2025-08-09 14:42
 */
@Data
public class ReferenceFormAuditParam {

    /**
     * 表单id
     */
    @NotNull(message = "表单id不能为空")
    private Integer id;
    /**
     * 审核状态：0-待审核，1-审核通过，2-审核未通过
     */
    @NotNull(message = "审核状态不能为空")
    private Integer auditStatus;
    /**
     * 审核备注
     */
    private String auditRemark;

}
