package com.echronos.expo.vo;

import lombok.Data;

import java.util.List;

/**
 * 注册页面词典
 *
 * <AUTHOR>
 * @Date 2025/5/29 9:16
 * @ClassName ExpoDictCodeValueVO
 */
@Data
public class ExpoDictCodeValueVO {
    /**
     * 行业列表
     *
     * @return
     */
    private List<ExpoCodeValueVO> industryList;


    /**
     * 展会角色列表
     *
     * @return
     */
    private List<ExpoCodeValueVO> expoRoleList;

    /**
     * 兴趣列表
     *
     * @return
     */
    private List<ExpoCodeValueVO> interestsList;

    /**
     * 活动列表
     *
     * @return
     */
    private List<ExpoCodeValueVO> activitiesList;


    /**
     * 采购需求列表
     *
     * @return
     */
    private List<ExpoCodeValueVO> procurementNeedsList;


    /**
     * 公司规模列表
     *
     * @return
     */
    private List<ExpoCodeValueVO> companySizeList;

    /**
     * 职位列表
     *
     * @return
     */
    private List<ExpoCodeValueVO> positionsList;


    /**
     * 产品或解决方案列表
     *
     * @return
     */
    private List<ExpoCodeValueVO> productsList;

    /**
     * 采购时间表
     *
     * @return
     */
    private List<ExpoCodeValueVO> procurementTimelineList;

    /**
     * 预算列表
     *
     * @return
     */
    private List<ExpoCodeValueVO> budgetList;


    /**
     * 采购角色
     *
     * @return
     */
    private List<ExpoCodeValueVO> procurementRoleList;

    /**
     * 主要要求
     *
     * @return
     */
    private List<ExpoCodeValueVO> requirementsList;

    /**
     * 合作模式列表
     *
     * @return
     */
    private List<ExpoCodeValueVO> cooperationModelList;

    /**
     * 联系方式列表
     */
    private List<ExpoCodeValueVO> contactTypeList;
    /**
     * 计划列表
     *
     * @return
     */
    private List<ExpoCodeValueVO> plansList;
}
