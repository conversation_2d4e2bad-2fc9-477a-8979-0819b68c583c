package com.echronos.expo.vo;

import lombok.Data;

import java.util.List;

/**
 * 导入数据校验结果
 *
 * <AUTHOR>
 * @date 2025-5-19 15:07:27
 */
@Data
public class ImportCheckMsgVO {
    /**
     * 行号
     **/
    private Integer rowNo;

    /**
     * 观众名称
     */
    private String name;
    /**
     * 邮箱
     **/
    private String email;
    /**
     * 手机号
     **/
    private String phone;
    /**
     * 校验结果列表
     **/
    private List<String> msgList;

    /**
     * 是否通过
     * true通过
     * false失败
     */
    private Boolean isOk;
}
