/**
 * Copyright © 2020 ECHRONOS Corp. All rights reserved.
 * This software is proprietary to and embodies the confidential
 * technology of ECHRONOS Corp.  Possession, use, or copying
 * of this software and media is authorized only pursuant to a
 * valid written license from ECHRONOS Corp or an authorized sublicensor.
 */
package com.echronos.expo.vo;


import lombok.Data;
import lombok.EqualsAndHashCode;
import java.time.LocalDateTime;
import java.math.BigDecimal;

/**
 * ExpoBooth controller层返回值
 * 
 * <AUTHOR>
 * @date 2025-08-01
 */
@Data
public class ExpoBoothVO{


    /**
     *  展位ID 
     */
    private Integer id;
    /**
     *  展会id 
     */
    private Integer expoId;
    /**
     *  展馆号/名称 
     */
    private String boothName;
    /**
     *  楼层 
     */
    private String boothFloor;
    /**
     *  区号/区域 
     */
    private String boothZone;
    /**
     *  展位号 
     */
    private String boothNumber;
    /**
     *  展位类型：1.标准展位  2.光地 3.角位 4.半岛位 5.岛位 6.双开口位 
     */
    private Integer boothType;
    /**
     *  展位类型（文本）
     */
    private String boothTypeName;
    /**
     *  尺寸 
     */
    private String dimensions;
    /**
     *  价格 
     */
    private BigDecimal price;
    /**
     *  出售状态：10.空闲  20.已售 
     */
    private Integer status;
    /**
     * 出售状态（文本）
     */
    private String statusName;
    /**
     * 所属展商名称
     */
    private String exhibitorName;

}