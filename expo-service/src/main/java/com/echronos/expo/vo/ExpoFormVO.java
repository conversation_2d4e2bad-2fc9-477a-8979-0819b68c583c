/**
 * Copyright © 2020 ECHRONOS Corp. All rights reserved.
 * This software is proprietary to and embodies the confidential
 * technology of ECHRONOS Corp.  Possession, use, or copying
 * of this software and media is authorized only pursuant to a
 * valid written license from ECHRONOS Corp or an authorized sublicensor.
 */
package com.echronos.expo.vo;


import com.alibaba.fastjson.annotation.JSONField;
import com.echronos.commons.utils.TimeSerializerUtils;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * EchExpoForm controller层返回值
 *
 * <AUTHOR>
 * @date 2025-05-13
 */
@Data
public class ExpoFormVO {

    /**
     * ID
     */
    private Integer id;
    /**
     * 所属公司id
     */
    private Integer companyId;
    /**
     * 展会id
     */
    private Integer expoId;
    /**
     * 表单类型：1、观众注册表单，2、参展申请表单
     */
    private Integer formType;
    /**
     * 表单名称
     */
    private String formName;
    /**
     * 表单描述
     */
    private String description;
    /**
     * 发布站点id
     */
    private String publishTenantId;
    /**
     * 自定义表单系统code
     */
    private String formCode;
    /**
     * 自定义表单版本号
     */
    private Long versionNumber;
    /**
     * 表单控件
     */
    private String fieldJson;

    /**
     * 是否启用
     */
    private Boolean isEnable;

    /**
     * 创建时间
     */
    @JSONField(serializeUsing = TimeSerializerUtils.class)
    private LocalDateTime createTime;
    /**
     * 表单字段
     */
    private List<FormFieldVO> formFieldList;

    /**
     * 选择展示字段集合
     */
    private List<String> fieldList;
}