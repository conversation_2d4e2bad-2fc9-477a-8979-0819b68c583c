package com.echronos.expo.vo;

import lombok.Data;

/**
 * <AUTHOR>
 * @date 2022/9/21
 */
@Data
public class FormFieldVO {
    /**
     * 字段属性
     */
    private String field;

    /**
     * 字段名称
     */
    private String fieldName;

    /**
     * 是否必填
     */
    private Boolean required;

    /**
     * 是否选中展示
     */
    private Boolean selected;

    /**
     * [
     * "text",
     * "textarea",
     * "number",
     * "date",
     * "radio",
     * "checkbox",
     * "image",
     * "attachment",
     * "childForm",
     * "area",
     * "select",
     * "empty",
     * "instruction"
     * ]
     *
     * @return
     */
    private String type;

    /**
     * 分组信息(1:基本信息，2：其它资料，3:归属资料,4:系统信息）
     */
    private Integer group;
}
