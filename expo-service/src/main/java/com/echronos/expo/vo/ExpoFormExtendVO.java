/**
 * Copyright © 2020 ECHRONOS Corp. All rights reserved.
 * This software is proprietary to and embodies the confidential
 * technology of ECHRONOS Corp.  Possession, use, or copying
 * of this software and media is authorized only pursuant to a
 * valid written license from ECHRONOS Corp or an authorized sublicensor.
 */
package com.echronos.expo.vo;


import lombok.Data;
import lombok.EqualsAndHashCode;
import java.time.LocalDateTime;

/**
 * ExpoFormExtend controller层返回值
 * 
 * <AUTHOR>
 * @date 2025-08-01
 */
@Data
public class ExpoFormExtendVO{


    /**
     *  主键id 
     */
    private Integer id;
    /**
     *  关联表单配置id 
     */
    private Integer formId;
    /**
     *  业务id：观众ID/展商ID 
     */
    private Integer businessId;
    /**
     *  列名 
     */
    private String colName;
    /**
     *  列值 
     */
    private String colValue;
    /**
     *  排序 
     */
    private String colSort;
    /**
     *  创建人 
     */
    private Integer createUser;
    /**
     *  创建时间 
     */
    private LocalDateTime createTime;
    /**
     *  更新人 
     */
    private Integer updateUser;
    /**
     *  更新时间 
     */
    private LocalDateTime updateTime;
    /**
     *  是否删除：0.否  1.是 
     */
    private Integer isDeleted;
}