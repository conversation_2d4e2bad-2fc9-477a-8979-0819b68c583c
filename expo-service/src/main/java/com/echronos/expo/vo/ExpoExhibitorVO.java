/**
 * Copyright © 2020 ECHRONOS Corp. All rights reserved.
 * This software is proprietary to and embodies the confidential
 * technology of ECHRONOS Corp.  Possession, use, or copying
 * of this software and media is authorized only pursuant to a
 * valid written license from ECHRONOS Corp or an authorized sublicensor.
 */
package com.echronos.expo.vo;


import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.math.BigDecimal;
import java.util.List;

/**
 * ExpoExhibitor controller层返回值
 * 
 * <AUTHOR>
 * @date 2025-08-01
 */
@Data
public class ExpoExhibitorVO{

    /**
     *  主键id 
     */
    private Integer id;
    /**
     * 展会id
     */
    private Integer expoId;
    /**
     *  客户id 
     */
    private Integer customerId;
    /**
     *  客户名称
     */
    private String customerName;
    /**
     *  客户公司id 
     */
    private Integer customerCompanyId;
    /**
     *  客户联系人id 
     */
    private Integer customerContactsId;
    /**
     * 联系人名称
     */
    private String customerContactsName;
    /**
     * 联系人电话
     */
    private String customerContactsPhone;
    /**
     * 联系人邮箱
     */
    private String customerContactsEmail;
    /**
     *  业务员id 
     */
    private Integer businessMemberId;
    /**
     *  业务员名称 
     */
    private String businessMemberName;
    /**
     * 展区
     */
    private String boothZones;
    /**
     * 展位号
     */
    private String boothNumbers;
    /**
     * 展位类型名称
     */
    private String boothTypeNames;
    /**
     * 展位尺寸
     */
    private String dimensions;
    /**
     *  合同状态：10.待签署  20.已签署 
     */
    private Integer contractStatus;
    /**
     *  合同状态名称
     */
    private String contractStatusName;
    /**
     *  邮件发送状态：10.未发送  20.已发送 
     */
    private Integer sendEmailStatus;
    /**
     *  邮件发送状态名称
     */
    private String sendEmailStatusName;
    /**
     *  企业信息收集状态：10.待提交  20.待审核 30.审核拒绝 40.审核通过 
     */
    private Integer enterpriseInfoStatus;
    /**
     *  企业信息收集状态名称
     */
    private String enterpriseInfoStatusName;
    /**
     *  会刊信息收集状态： 10.待提交  20.待审核  30.审核拒绝  40.审核通过 
     */
    private Integer journalInfoStatus;
    /**
     *  会刊信息收集状态名称
     */
    private String journalInfoStatusName;
    /**
     *  租赁类型：10.未收集  20.不需要租赁  30.需要租赁 
     */
    private Integer leaseDemandType;
    /**
     *  租赁类型名称
     */
    private String leaseDemandTypeName;
    /**
     *  备注 
     */
    private String remark;
    /**
     *  展位费用
     */
    private BigDecimal boothTotalAmount;
    /**
     *  租赁费用
     */
    private BigDecimal leaseTotalAmount;
    /**
     * 已支付金额
     */
    private BigDecimal paidAmount = BigDecimal.ZERO;
    /**
     * 未支付金额
     */
    private BigDecimal unpaidAmount = BigDecimal.ZERO;
    /**
     * 总金额
     */
    private BigDecimal totalAmount = BigDecimal.ZERO;

}