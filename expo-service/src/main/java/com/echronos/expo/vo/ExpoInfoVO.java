/**
 * Copyright © 2020 ECHRONOS Corp. All rights reserved.
 * This software is proprietary to and embodies the confidential
 * technology of ECHRONOS Corp.  Possession, use, or copying
 * of this software and media is authorized only pursuant to a
 * valid written license from ECHRONOS Corp or an authorized sublicensor.
 */
package com.echronos.expo.vo;


import com.alibaba.fastjson.annotation.JSONField;
import com.echronos.commons.utils.TimeSerializerUtils;
import com.echronos.expo.param.ExpoAttachmentFileParam;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * EchExpoInfo controller层返回值
 *
 * <AUTHOR>
 * @date 2025-05-13
 */
@Data
public class ExpoInfoVO {


    /**
     * ID
     */
    private Long id;
    /**
     * 所属公司id
     */
    private Integer companyId;
    /**
     * 展会名称
     */
    private String expoName;
    /**
     * 展会简称
     */
    private String shortName;
    /**
     * 国家code
     */
    private Integer countryCode;
    /**
     * 城市code
     */
    private Integer cityCode;
    /**
     * 展馆名称
     */
    private String hallName;
    /**
     * 开始时间
     */
    private LocalDateTime startTime;
    /**
     * 结束时间
     */
    private LocalDateTime endTime;
    /**
     * 展会状态：0、未开始；1、进行中；2、已结束
     */
    private Integer expoStatus;
    /**
     * 详细地址
     */
    private String address;
    /**
     * 举办方名称
     */
    private String organizer;
    /**
     * 展会状态：0、未开始；1、进行中；2、已结束
     */
    private String expoStatusStr;
    /**
     * remark
     */
    private String remark;
    /**
     * 展商ID
     */
    private Integer exhibitorId;
    /**
     * 参展商数量
     */
    private Integer exhibitorCount;
    /**
     * 参展手册
     */
    private List<ExpoAttachmentFileVO> handbookAttachmentFiles;
    /**
     * 海报模板
     */
    private List<ExpoAttachmentFileVO> templateAttachmentFiles;
}