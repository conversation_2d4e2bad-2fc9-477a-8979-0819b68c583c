package com.echronos.expo.vo.referenform;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 添加表单或编辑表单
 * <AUTHOR>
 * @date 2025-08-09 14:51
 */
@Data
public class AddOrEditReferenceFormParam {

    /**
     * 表单id（编辑时需要传）
     */
    private Integer id;
    /**
     * 表单编码
     */
    @NotBlank(message = "表单编码不能为空")
    private String formCode;
    /**
     * 表单版本
     */
    @NotBlank(message = "表单版本不能为空")
    private String formVersion;
    /**
     * 业务id：观众id/展商id
     */
    @NotNull(message = "业务id不能为空")
    private Integer businessId;
    /**
     * 表单值
     */
    private List<AddReferenceFormExtendParam> extendList;

}
