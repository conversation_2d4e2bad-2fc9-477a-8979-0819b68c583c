package com.echronos.expo.vo;

import lombok.Data;

import java.util.List;

/**
 * 互动展商排行统计VO
 *
 * <AUTHOR>
 * @date 2025/8/18 17:30
 */
@Data
public class ExpoExhibitorInteractionRankingVO {
    
    /**
     * 展商排行列表
     */
    private List<ExhibitorRankingItem> exhibitorRankingList;
    
    /**
     * 展商排行项
     */
    @Data
    public static class ExhibitorRankingItem {
        /**
         * 排名
         */
        private Integer ranking;
        
        /**
         * 展商ID
         */
        private Integer exhibitorId;
        
        /**
         * 展商名称
         */
        private String exhibitorName;
        
        /**
         * 扫码次数
         */
        private Integer scanCount;
        
        /**
         * 预约次数
         */
        private Integer appointmentCount;
        
        /**
         * 总互动次数
         */
        private Integer totalInteractionCount;
    }
}
