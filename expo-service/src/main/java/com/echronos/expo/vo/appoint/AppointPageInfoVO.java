package com.echronos.expo.vo.appoint;

import com.echronos.expo.vo.ExpoBoothDetailVO;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/8/9 17:02
 */
@Data
public class AppointPageInfoVO {
    /**
     * ID
     */
    private Integer id;
    /**
     * 展会ID
     */
    private Integer expoId;
    /**
     * 展会开始时间
     */
    private LocalDateTime startTime;
    /**
     * 展会结束时间
     */
    private LocalDateTime endTime;
    /**
     * 展馆名称
     */
    private String hallName;
    /**
     * 详细地址
     */
    private String address;
    /**
     * 展位信息
     */
    private List<ExpoBoothDetailVO> boothList;
}
