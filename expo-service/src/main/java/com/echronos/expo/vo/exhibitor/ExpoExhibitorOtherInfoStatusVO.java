package com.echronos.expo.vo.exhibitor;

import lombok.Data;

import java.math.BigDecimal;

/**
 * 展商其他业务状态转换率对象
 *
 * <AUTHOR>
 * date2025/8/11 17:09
 */
@Data
public class ExpoExhibitorOtherInfoStatusVO {

    /**
     * 未提交展商的转换率
     */
    private BigDecimal toSubmitConversionRate;

    /**
     * 待审核展商数据转换率
     */
    private BigDecimal toAuditConversionRate;

    /**
     * 审核通过展商数据转换率
     */
    private BigDecimal approvedConversionRate;

    /**
     * 审核不通过展商数据转换率
     */
    private BigDecimal rejectedConversionRate;
}
