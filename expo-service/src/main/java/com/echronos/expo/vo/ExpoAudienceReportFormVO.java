package com.echronos.expo.vo;

import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * date2025/8/14 11:27
 */
@Data
public class ExpoAudienceReportFormVO {

    /**
     * 总报名人数
     */
    private Integer audienceTotal;

    /**
     * 实际到场人数
     */
    private Integer actualAttendanceTotal;

    /**
     * 到场率
     */
    private BigDecimal attendanceRate;

    /**
     * 复访观众
     */
    private Integer followAudienceTotal;

    /**
     * 复访率
     */
    private BigDecimal followAudienceRate;

    /**
     * 总报名人数比去年同期百分比
     */
    private BigDecimal audienceDifferenceRate;

    /**
     * 到场率比去年同期百分比
     */
    private BigDecimal attendanceDifferenceRate;

    /**
     *未到场人数
     */
    private Integer noAttendanceTotal;

    /**
     * 未到场率
     */
    private BigDecimal noAttendanceRate;

    /**
     * 首次观众
     */
    private Integer firstAudienceTotal;

    /**
     * 首次观众占比率
     */
    private BigDecimal firstAudienceRate;
}
