package com.echronos.expo.vo.exhibitor;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * 展商引用表单
 * <AUTHOR>
 * @date 2025-08-07 14:30
 */
@Data
public class ExhibitorFormVO {

    /**
     * 表单类型：1.观众注册表单，2.观众满意度调查 20.企业信息收集  21.会刊收集  22.满意度调查  23.其它
     */
    private Integer formType;
    /**
     * 表单名称
     */
    private String formName;
    /**
     * 表单描述
     */
    private String description;
    /**
     * 当前类型配置的自定义表单code
     */
    private String formCode;
    /**
     * 当前类型配置的自定义表单版本号
     */
    private Long versionNumber;
    /**
     * 展商已经填写的表单id，未填写为null
     */
    private Integer exhibitorFormId;
    /**
     * 提交时间
     */
    private LocalDateTime submitTime;
    /**
     * 审核时间
     */
    private LocalDateTime auditTime;
    /**
     * 审核状态：0.待审核 1.审核拒绝 2.审核通过
     */
    private Integer auditStatus;
    /**
     * 审核状态名称
     */
    private String auditStatusName;
    /**
     * 审核原因
     */
    private String auditRemark;

}
