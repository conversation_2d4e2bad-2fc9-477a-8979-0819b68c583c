package com.echronos.expo.vo.appoint;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2025/8/11 17:42
 */
@Data
public class AppointTimeListVO {
    /**
     * 主键
     */
    private Integer id;
    /**
     * 展会ID
     */
    private Integer expoId;
    /**
     * 业务ID
     */
    private Integer businessId;
    /**
     * 业务类型：1展商 2观众
     */
    private Integer businessType;
    /**
     * 成员ID
     */
    private Integer memberId;
    /**
     * 成员名称
     */
    private String memberName;
    /**
     * 状态：10.待确认 20.已接受 30.已拒绝 40.已取消
     */
    private Integer status;
    /**
     * 状态名称
     */
    private String statusName;
    /**
     * 预约类型：1.我预约的 2.预约我的
     */
    private Integer appointType;
    /**
     * 开始时间
     */
    private LocalDateTime startTime;
    /**
     * 结束时间
     */
    private LocalDateTime endTime;
}
