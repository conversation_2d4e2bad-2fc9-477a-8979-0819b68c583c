package com.echronos.expo.vo;

import lombok.Data;

import java.math.BigDecimal;

/**
 * 预约状态分布统计VO
 *
 * <AUTHOR>
 * @date 2025/8/18 10:42
 */
@Data
public class ExpoAppointStatusVO {
    /**
     * 待确认比率
     */
    private BigDecimal toConfirmRate;
    /**
     * 已接受比率
     */
    private BigDecimal acceptedRate;
    /**
     * 已拒绝比率
     */
    private BigDecimal rejectedRate;
    /**
     * 已取消比率
     */
    private BigDecimal cancelledRate;
}
