package com.echronos.expo.vo;

import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2025-08-04 14:53
 */
@Data
public class ExpoBoothDetailVO {

    /**
     *  展位ID
     */
    private Integer id;
    /**
     *  展会id
     */
    private Integer expoId;
    /**
     *  展馆号/名称
     */
    private String boothName;
    /**
     *  楼层
     */
    private String boothFloor;
    /**
     *  区号/区域
     */
    private String boothZone;
    /**
     *  展位号
     */
    private String boothNumber;
    /**
     *  展位类型：1.标准展位  2.光地 3.角位 4.半岛位 5.岛位 6.双开口位
     */
    private Integer boothType;
    /**
     *  展位类型（文本）
     */
    private String boothTypeName;
    /**
     *  尺寸
     */
    private String dimensions;
    /**
     *  价格
     */
    private BigDecimal price;
    /**
     *  出售状态：10.空闲  20.已售
     */
    private Integer status;
    /**
     * 出售状态（文本）
     */
    private String statusName;

}
