package com.echronos.expo.vo.appoint;

import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/8/7 14:21
 */
@Data
public class AppointmentSetVO {
    /**
     * 主键ID
     */
    private Integer id;
    /**
     * 展会ID
     */
    private Integer expoId;
    /**
     * 是否开启预约：0-否 1-是
     */
    private Integer isAppoint;
    /**
     * 展会开始时间
     */
    private LocalDateTime expoStartTime;
    /**
     * 展会结束时间
     */
    private LocalDateTime expoEndTime;
    /**
     * 可预约人员
     */
    private List<ExpoAppointedPersonnelVO> personnelList;
}
