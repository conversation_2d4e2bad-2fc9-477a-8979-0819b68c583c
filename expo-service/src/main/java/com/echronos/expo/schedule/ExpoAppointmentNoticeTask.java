package com.echronos.expo.schedule;

import com.dangdang.ddframe.job.api.ShardingContext;
import com.echronos.expo.enums.ExpoBusinessTypeEnum;
import com.echronos.expo.manager.ExpoAppointmentManager;
import com.echronos.expo.manager.FeignCommonManager;
import com.echronos.expo.model.ext.ExpoAppointmentExt;
import com.echronos.job.annotation.ElasticSimpleJob;
import com.echronos.job.base.SimpleJobAbstract;
import com.echronos.system.resp.member.MemberSimpleResp;
import com.echronos.user.api.resp.company.QueryCompanyResp;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 展前24H预约未响应通知任务
 *
 * <AUTHOR>
 * @date 2025/8/14 16:45
 */
@Slf4j
@ElasticSimpleJob(corn = "0 0 9 * * ?", disabled = false, overwrite = true)
public class ExpoAppointmentNoticeTask extends SimpleJobAbstract {

    @Resource
    private ExpoAppointmentManager expoAppointmentManager;
    @Resource
    private FeignCommonManager feignCommonManager;

    @Override
    public void execute(ShardingContext shardingContext) {
        log.info("开始执行展前24H预约未响应通知任务");
        // 查询展前24H未响应的预约
        List<ExpoAppointmentExt> list = expoAppointmentManager.queryBeforeExpoNoResponse();
        if (!CollectionUtils.isEmpty(list)) {
            List<Integer> expoCompanyIds = list.stream().map(ExpoAppointmentExt::getExpoCompanyId).distinct().collect(Collectors.toList());
            // todo 根据权限查接收人
            Map<Integer, List<MemberSimpleResp>> map = feignCommonManager.queryMemberListBy(expoCompanyIds, "");
            // 查询公司信息
            Map<Integer, QueryCompanyResp> companyMap = feignCommonManager.queryCompanyByIds(expoCompanyIds);
            // 根据展会分组
            Map<Integer, List<ExpoAppointmentExt>> expoMap = list.stream().collect(Collectors.groupingBy(ExpoAppointmentExt::getExpoId));
            expoMap.forEach((expoId, expoList) -> {
                Integer expoCompanyId = expoList.get(0).getExpoCompanyId();
                // 总数
                int total = expoList.size();
                // 展商预约观众数
                long exhibitorTotal = expoList.stream().filter(v -> ExpoBusinessTypeEnum.EXHIBITOR.getCode().equals(v.getBusinessType())).count();
                // 观众预约展商数
                long audienceTotal = expoList.stream().filter(v -> ExpoBusinessTypeEnum.AUDIENCE.getCode().equals(v.getBusinessType())).count();
                // todo 发im通知

            });
        }
    }
}
