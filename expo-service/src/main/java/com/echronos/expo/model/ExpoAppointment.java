/**
 * Copyright © 2020 ECHRONOS Corp. All rights reserved.
 * This software is proprietary to and embodies the confidential
 * technology of ECHRONOS Corp.  Possession, use, or copying
 * of this software and media is authorized only pursuant to a
 * valid written license from ECHRONOS Corp or an authorized sublicensor.
 */
package com.echronos.expo.model;


import lombok.Data;
import com.baomidou.mybatisplus.annotation.*;
import java.time.LocalDateTime;

/**
 * ExpoAppointment 实体类
 * 
 * <AUTHOR>
 * @date 2025-08-07
 */
@Data
@TableName("ech_expo_appointment")
public class ExpoAppointment{


    /**
     *  主键 
     */
    @TableId(value = "id",type = IdType.AUTO)
    private Integer id;
    /**
     *  展会ID 
     */
    private Integer expoId;
    /**
     *  业务ID 
     */
    private Integer businessId;
    /**
     *  业务类型：1展商 2观众 
     */
    private Integer businessType;
    /**
     *  预约人员ID 
     */
    private Integer appointedPersonnelId;
    /**
     *  预约目的（展商/观众） 
     */
    private Integer purposeType;
    /**
     *  备注 
     */
    private String remark;
    /**
     *  状态：10.待确认 20.已接受 30.已拒绝 40.已取消 
     */
    private Integer status;
    /**
     *  成员ID
     */
    private Integer memberId;
    /**
     *  公司ID 
     */
    private Integer companyId;
    /**
     *  是否发通知：0否 1是
     */
    private Integer isNotice;
    /**
     *  创建人 
     */
    @TableField(fill = FieldFill.INSERT)
    private Integer createUser;
    /**
     *  更新人 
     */
    @TableField(fill = FieldFill.UPDATE)
    private Integer updateUser;
    /**
     *  创建时间 
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;
    /**
     *  修改时间 
     */
    @TableField(fill = FieldFill.UPDATE)
    private LocalDateTime updateTime;
    /**
     *  是否已删除：0否 1是 
     */
    private Integer isDeleted;
}