/**
 * Copyright © 2020 ECHRONOS Corp. All rights reserved.
 * This software is proprietary to and embodies the confidential
 * technology of ECHRONOS Corp.  Possession, use, or copying
 * of this software and media is authorized only pursuant to a
 * valid written license from ECHRONOS Corp or an authorized sublicensor.
 */
package com.echronos.expo.model;


import lombok.Data;
import com.baomidou.mybatisplus.annotation.*;
import java.time.LocalDateTime;
import java.math.BigDecimal;

/**
 * ExpoLeaseOrderDetail 实体类
 * 
 * <AUTHOR>
 * @date 2025-08-13
 */
@Data
@TableName("ech_expo_lease_order_detail")
public class ExpoLeaseOrderDetail extends BaseNotTenantEntity{


    /**
     *  主键id 
     */
    @TableId(value = "id",type = IdType.AUTO)
    private Integer id;
    /**
     *  租赁订单id
     */
    private Integer orderId;
    /**
     *  数量 
     */
    private BigDecimal quantity;
    /**
     *  报价价格（含税单价） 
     */
    private BigDecimal price;
    /**
     *  未税单价 
     */
    private BigDecimal noTaxPrice;
    /**
     *  税率 
     */
    private BigDecimal taxRate;
    /**
     *  关联店铺商品id 
     */
    private Integer shopSkuId;
    /**
     *  关联商品skuId 
     */
    private Integer skuId;
    /**
     *  商品名称快照 
     */
    private String name;
    /**
     *  商品编码快照 
     */
    private String skuCode;
    /**
     *  商品规格快照 
     */
    private String standardJson;
    /**
     *  商品市场价快照 
     */
    private BigDecimal marketPrice;
    /**
     *  商品单位快照 
     */
    private String unit;
}