/**
 * Copyright © 2020 ECHRONOS Corp. All rights reserved.
 * This software is proprietary to and embodies the confidential
 * technology of ECHRONOS Corp.  Possession, use, or copying
 * of this software and media is authorized only pursuant to a
 * valid written license from ECHRONOS Corp or an authorized sublicensor.
 */
package com.echronos.expo.model;


import lombok.Data;
import com.baomidou.mybatisplus.annotation.*;
import java.time.LocalDateTime;

/**
 * ExpoExhibitorBooth 实体类
 * 
 * <AUTHOR>
 * @date 2025-08-06
 */
@Data
@TableName("ech_expo_exhibitor_booth")
public class ExpoExhibitorBooth extends BaseNotTenantEntity{


    /**
     *  主键id 
     */
    @TableId(value = "id",type = IdType.AUTO)
    private Integer id;
    /**
     *  展商id 
     */
    private Integer exhibitorId;
    /**
     *  展位id 
     */
    private Integer boothId;

}