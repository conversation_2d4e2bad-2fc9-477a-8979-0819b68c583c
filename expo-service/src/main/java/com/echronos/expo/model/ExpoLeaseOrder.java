/**
 * Copyright © 2020 ECHRONOS Corp. All rights reserved.
 * This software is proprietary to and embodies the confidential
 * technology of ECHRONOS Corp.  Possession, use, or copying
 * of this software and media is authorized only pursuant to a
 * valid written license from ECHRONOS Corp or an authorized sublicensor.
 */
package com.echronos.expo.model;


import lombok.Data;
import com.baomidou.mybatisplus.annotation.*;
import java.time.LocalDateTime;
import java.math.BigDecimal;

/**
 * ExpoLeaseOrder 实体类
 * 
 * <AUTHOR>
 * @date 2025-08-13
 */
@Data
@TableName("ech_expo_lease_order")
public class ExpoLeaseOrder extends BaseNotTenantEntity{


    /**
     *  主键id 
     */
    @TableId(value = "id",type = IdType.AUTO)
    private Integer id;
    /**
     *  展会id 
     */
    private Integer expoId;
    /**
     *  展商id 
     */
    private Integer exhibitorId;
    /**
     *  客户id 
     */
    private Integer customerId;
    /**
     *  客户公司id 
     */
    private Integer customerCompanyId;
    /**
     *  客户联系人id 
     */
    private Integer customerContactsId;
    /**
     *  开单时间 
     */
    private LocalDateTime orderTime;
    /**
     *  付款方式编号 
     */
    private String payWayNo;
    /**
     *  业务员id 
     */
    private Integer businessMemberId;
    /**
     *  项目id 
     */
    private Integer projectId;
    /**
     *  备注 
     */
    private String remark;
    /**
     *  订单编号 
     */
    private Long orderNo;
    /**
     *  总金额 
     */
    private BigDecimal totalAmount;
    /**
     *  优惠金额 
     */
    private BigDecimal discountAmount;

}