/**
 * Copyright © 2020 ECHRONOS Corp. All rights reserved.
 * This software is proprietary to and embodies the confidential
 * technology of ECHRONOS Corp.  Possession, use, or copying
 * of this software and media is authorized only pursuant to a
 * valid written license from ECHRONOS Corp or an authorized sublicensor.
 */
package com.echronos.expo.model;


import lombok.Data;
import com.baomidou.mybatisplus.annotation.*;
import java.time.LocalDateTime;

/**
 * ExpoPrintConfig 实体类
 * 
 * <AUTHOR>
 * @date 2025-08-15
 */
@Data
@TableName("ech_expo_print_config")
public class ExpoPrintConfig{


    /**
     *  主键 
     */
    @TableId(value = "id",type = IdType.AUTO)
    private Integer id;
    /**
     *  展会ID 
     */
    private Integer expoId;
    /**
     *  表单ID 
     */
    private Integer formId;
    /**
     *  是否包含观众二维码：0否 1是 
     */
    private Integer isContainQr;
    /**
     *  字段集 
     */
    private String fieldJson;
    /**
     *  创建人 
     */
    @TableField(fill = FieldFill.INSERT)
    private Integer createUser;
    /**
     *  更新人 
     */
    @TableField(fill = FieldFill.UPDATE)
    private Integer updateUser;
    /**
     *  创建时间 
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;
    /**
     *  修改时间 
     */
    @TableField(fill = FieldFill.UPDATE)
    private LocalDateTime updateTime;
    /**
     *  是否已删除：0否 1是 
     */
    private Integer isDeleted;
}