/**
 * Copyright © 2020 ECHRONOS Corp. All rights reserved.
 * This software is proprietary to and embodies the confidential
 * technology of ECHRONOS Corp.  Possession, use, or copying
 * of this software and media is authorized only pursuant to a
 * valid written license from ECHRONOS Corp or an authorized sublicensor.
 */
package com.echronos.expo.model;


import lombok.Data;
import com.baomidou.mybatisplus.annotation.*;
import java.time.LocalDateTime;

/**
 * ExpoAppointedPersonnelTime 实体类
 * 
 * <AUTHOR>
 * @date 2025-08-07
 */
@Data
@TableName("ech_expo_appointed_personnel_time")
public class ExpoAppointedPersonnelTime{


    /**
     *  主键 
     */
    @TableId(value = "id",type = IdType.AUTO)
    private Integer id;
    /**
     *  预约人员ID 
     */
    private Integer personnelId;
    /**
     *  开始时间 
     */
    private LocalDateTime startTime;
    /**
     *  结束时间 
     */
    private LocalDateTime endTime;
    /**
     *  创建人 
     */
    @TableField(fill = FieldFill.INSERT)
    private Integer createUser;
    /**
     *  更新人 
     */
    @TableField(fill = FieldFill.UPDATE)
    private Integer updateUser;
    /**
     *  创建时间 
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;
    /**
     *  修改时间 
     */
    @TableField(fill = FieldFill.UPDATE)
    private LocalDateTime updateTime;
    /**
     *  是否已删除：0否 1是 
     */
    private Integer isDeleted;
}