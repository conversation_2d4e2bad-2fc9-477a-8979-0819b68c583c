package com.echronos.expo.model;

import com.baomidou.mybatisplus.annotation.*;
import com.echronos.commons.model.BaseModel;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @Date 2023/6/29 20:45
 * @ClassName BaseEntity
 */
@Data
public class BaseEntity extends BaseModel implements Serializable {

    /**
     * ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;
    /**
     * 创建人id
     */
    @TableField(fill = FieldFill.INSERT)
    private Integer createUser;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;
    /**
     * 修改者id
     */
    @TableField(fill = FieldFill.UPDATE)
    private Integer updateUser;
    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.UPDATE)
    private LocalDateTime updateTime;
    /**
     * 是否删除0否1是
     */
    @TableLogic(
            value = "0",
            delval = "1"
    )
    private Boolean isDeleted;
}
