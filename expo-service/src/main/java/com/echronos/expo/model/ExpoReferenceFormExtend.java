/**
 * Copyright © 2020 ECHRONOS Corp. All rights reserved.
 * This software is proprietary to and embodies the confidential
 * technology of ECHRONOS Corp.  Possession, use, or copying
 * of this software and media is authorized only pursuant to a
 * valid written license from ECHRONOS Corp or an authorized sublicensor.
 */
package com.echronos.expo.model;


import lombok.Data;
import com.baomidou.mybatisplus.annotation.*;
import java.time.LocalDateTime;

/**
 * ExpoReferenceFormExtend 实体类
 * 
 * <AUTHOR>
 * @date 2025-08-08
 */
@Data
@TableName("ech_expo_reference_form_extend")
public class ExpoReferenceFormExtend extends BaseNotTenantEntity{


    /**
     *  主键id 
     */
    @TableId(value = "id",type = IdType.AUTO)
    private Integer id;
    /**
     *  展商观众关联使用表单 
     */
    private Integer referenceFormId;
    /**
     *  列名 
     */
    private String colName;
    /**
     *  列值 
     */
    private String colValue;
    /**
     *  排序 
     */
    private String colSort;

}