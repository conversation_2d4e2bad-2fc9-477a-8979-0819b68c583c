/**
 * Copyright © 2020 ECHRONOS Corp. All rights reserved.
 * This software is proprietary to and embodies the confidential
 * technology of ECHRONOS Corp.  Possession, use, or copying
 * of this software and media is authorized only pursuant to a
 * valid written license from ECHRONOS Corp or an authorized sublicensor.
 */
package com.echronos.expo.model;


import lombok.Data;
import com.baomidou.mybatisplus.annotation.*;
import java.time.LocalDateTime;

/**
 * ExpoFormExtend 实体类
 * 
 * <AUTHOR>
 * @date 2025-08-01
 */
@Data
@TableName("ech_expo_form_extend")
public class ExpoFormExtend extends BaseNotTenantEntity{

    /**
     *  关联表单配置id 
     */
    private Integer formId;
    /**
     *  业务id：观众ID/展商ID 
     */
    private Integer businessId;
    /**
     *  列名 
     */
    private String colName;
    /**
     *  列值 
     */
    private String colValue;
    /**
     *  排序 
     */
    private String colSort;

}