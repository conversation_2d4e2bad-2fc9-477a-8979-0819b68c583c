/**
 * Copyright © 2020 ECHRONOS Corp. All rights reserved.
 * This software is proprietary to and embodies the confidential
 * technology of ECHRONOS Corp.  Possession, use, or copying
 * of this software and media is authorized only pursuant to a
 * valid written license from ECHRONOS Corp or an authorized sublicensor.
 */
package com.echronos.expo.model;


import com.echronos.commons.model.BaseModel;
import lombok.Data;
import com.baomidou.mybatisplus.annotation.*;
import java.time.LocalDateTime;

/**
 * ExpoExhibitorScanRecord 实体类
 * 
 * <AUTHOR>
 * @date 2025-08-15
 */
@Data
@TableName("ech_expo_exhibitor_scan_record")
public class ExpoExhibitorScanRecord extends BaseModel {


    /**
     *  主键ID 
     */
    @TableId(value = "id",type = IdType.AUTO)
    private Integer id;
    /**
     *  展会ID 
     */
    private Integer expoId;
    /**
     *  展商ID 
     */
    private Integer exhibitorId;
    /**
     *  观众ID 
     */
    private Integer audienceId;
    /**
     * 公司ID
     */
    private Integer companyId;
    /**
     *  创建人 
     */
    @TableField(fill = FieldFill.INSERT)
    private Integer createUser;
    /**
     *  创建时间 
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;
    /**
     *  修改人 
     */
    @TableField(fill = FieldFill.UPDATE)
    private Integer updateUser;
    /**
     *  修改时间 
     */
    @TableField(fill = FieldFill.UPDATE)
    private LocalDateTime updateTime;
    /**
     *  是否删除 0 否 1 是 
     */
    private Integer isDeleted;
}