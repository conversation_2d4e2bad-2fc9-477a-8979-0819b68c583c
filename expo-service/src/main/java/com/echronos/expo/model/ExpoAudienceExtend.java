/**
 * Copyright © 2020 ECHRONOS Corp. All rights reserved.
 * This software is proprietary to and embodies the confidential
 * technology of ECHRONOS Corp.  Possession, use, or copying
 * of this software and media is authorized only pursuant to a
 * valid written license from ECHRONOS Corp or an authorized sublicensor.
 */
package com.echronos.expo.model;


import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * ExpoAudienceExtend 实体类
 *
 * <AUTHOR>
 * @date 2025-05-14
 */
@Data
@TableName("ech_expo_audience_extend")
public class ExpoAudienceExtend extends BaseEntity {

    /**
     * 所属公司id
     */
    private Integer companyId;
    /**
     * 展会id
     */
    private Integer expoId;
    /**
     * 申请表单id
     */
    private Integer formId;
    /**
     * 观众ID
     */
    private Integer audienceId;
    /**
     * 控件名称
     */
    private String colName;
    /**
     * 控件值
     */
    private String colValue;
    /**
     * 自定义表单系统code
     */
    private String formCode;
    /**
     * 自定义表单版本
     */
    private Integer versionNumber;
}