/**
 * Copyright © 2020 ECHRONOS Corp. All rights reserved.
 * This software is proprietary to and embodies the confidential
 * technology of ECHRONOS Corp.  Possession, use, or copying
 * of this software and media is authorized only pursuant to a
 * valid written license from ECHRONOS Corp or an authorized sublicensor.
 */
package com.echronos.expo.model;


import com.echronos.commons.model.BaseModel;
import lombok.Data;
import com.baomidou.mybatisplus.annotation.*;
import java.time.LocalDateTime;

/**
 * ExpoTenancySkuConfigure 实体类
 * 
 * <AUTHOR>
 * @date 2025-08-09
 */
@Data
@TableName("ech_expo_tenancy_sku")
public class ExpoTenancySku extends BaseModel {


    /**
     *  主键ID 
     */
    @TableId(value = "id",type = IdType.AUTO)
    private Integer id;
    /**
     *  展会ID 
     */
    private Integer expoId;
    /**
     *  公司ID 
     */
    private Integer companyId;
    /**
     *  店铺商品ID 
     */
    private Integer shopSkuId;
    /**
     *  商品ID 
     */
    private Integer skuId;
    /**
     *  创建人 
     */
    @TableField(fill = FieldFill.INSERT)
    private Integer createUser;
    /**
     *  创建时间 
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;
    /**
     *  修改人 
     */
    @TableField(fill = FieldFill.UPDATE)
    private Integer updateUser;
    /**
     *  修改时间 
     */
    @TableField(fill = FieldFill.UPDATE)
    private LocalDateTime updateTime;
    /**
     *  是否删除 0 否 1 是 
     */
    @TableLogic(
            value = "0",
            delval = "1"
    )
    private Integer isDeleted;
}