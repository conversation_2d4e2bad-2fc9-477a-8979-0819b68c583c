/**
 * Copyright © 2020 ECHRONOS Corp. All rights reserved.
 * This software is proprietary to and embodies the confidential
 * technology of ECHRONOS Corp.  Possession, use, or copying
 * of this software and media is authorized only pursuant to a
 * valid written license from ECHRONOS Corp or an authorized sublicensor.
 */
package com.echronos.expo.model;


import lombok.Data;
import com.baomidou.mybatisplus.annotation.*;
import java.time.LocalDateTime;

/**
 * ExpoAudienceSign 实体类
 * 
 * <AUTHOR>
 * @date 2025-05-14
 */
@Data
@TableName("ech_expo_audience_sign")
public class ExpoAudienceSign extends BaseEntity{
    /**
     *  所属公司ID 
     */
    private Integer companyId;
    /**
     *  展会ID 
     */
    private Integer expoId;
    /**
     *  观众ID 
     */
    private Integer audienceId;
}