/**
 * Copyright © 2020 ECHRONOS Corp. All rights reserved.
 * This software is proprietary to and embodies the confidential
 * technology of ECHRONOS Corp.  Possession, use, or copying
 * of this software and media is authorized only pursuant to a
 * valid written license from ECHRONOS Corp or an authorized sublicensor.
 */
package com.echronos.expo.manager;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.echronos.commons.enums.CommonStatus;
import com.google.common.collect.Lists;
import org.springframework.stereotype.Component;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.echronos.expo.model.ExpoLeaseOrder;
import com.echronos.expo.dao.ExpoLeaseOrderDao;

import java.util.List;


/**
 * ExpoLeaseOrder Manager
 * 
 * <AUTHOR>
 * @date 2025-08-13
 */
@Component
public class ExpoLeaseOrderManager extends ServiceImpl<ExpoLeaseOrderDao, ExpoLeaseOrder> {

    /**
     * 根据展商ID查询租赁订单
     * @param exhibitorId 展商ID
     * @return
     */
    public List<ExpoLeaseOrder> getListByExhibitorId(Integer exhibitorId){
        return getListByExhibitorIds(Lists.newArrayList(exhibitorId));
    }

    /**
     * 批量根据展商ID查询租赁订单
     * @param exhibitorIds 展商ID
     * @return
     */
    public List<ExpoLeaseOrder> getListByExhibitorIds(List<Integer> exhibitorIds){
        LambdaQueryWrapper<ExpoLeaseOrder> queryWrapper = new LambdaQueryWrapper<ExpoLeaseOrder>()
                .in(ExpoLeaseOrder::getExhibitorId, exhibitorIds)
                .eq(ExpoLeaseOrder::getIsDeleted, CommonStatus.DeleteEnum.NO.getValue());
        return list(queryWrapper);
    }


}
