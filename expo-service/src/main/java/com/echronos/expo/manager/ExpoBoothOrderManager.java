/**
 * Copyright © 2020 ECHRONOS Corp. All rights reserved.
 * This software is proprietary to and embodies the confidential
 * technology of ECHRONOS Corp.  Possession, use, or copying
 * of this software and media is authorized only pursuant to a
 * valid written license from ECHRONOS Corp or an authorized sublicensor.
 */
package com.echronos.expo.manager;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.echronos.commons.enums.CommonStatus;
import com.echronos.expo.dto.ExpoExhibitorBoothDTO;
import com.echronos.expo.model.BaseNotTenantEntity;
import com.echronos.expo.model.ExpoExhibitor;
import com.google.common.collect.Lists;
import org.springframework.stereotype.Component;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.echronos.expo.model.ExpoBoothOrder;
import com.echronos.expo.dao.ExpoBoothOrderDao;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;


/**
 * ExpoBoothOrder Manager
 * 
 * <AUTHOR>
 * @date 2025-08-01
 */
@Component
public class ExpoBoothOrderManager extends ServiceImpl<ExpoBoothOrderDao, ExpoBoothOrder> {

    /**
     * 根据展商ID查询展位订单
     *
     * @param expoExhibitorId 展商ID
     * @return 展位订单列表
     */
    public List<ExpoBoothOrder> getByExpoExhibitorId(Integer expoExhibitorId){
        return getByExpoExhibitorIds(Lists.newArrayList(expoExhibitorId));
    }

    /**
     * 批量根据展商ID查询展位订单
     *
     * @param expoExhibitorIds 展商ID
     * @return 展位订单列表
     */
    public List<ExpoBoothOrder> getByExpoExhibitorIds(List<Integer> expoExhibitorIds){
        LambdaQueryWrapper<ExpoBoothOrder> queryWrapper = new LambdaQueryWrapper<ExpoBoothOrder>()
                .in(ExpoBoothOrder::getExhibitorId, expoExhibitorIds)
                .eq(BaseNotTenantEntity::getIsDeleted, CommonStatus.DeleteEnum.NO.getValue());
        return this.list(queryWrapper);
    }

    /**
     * 删除展商订单信息
     */
    public void removeByExhibitorId(Integer exhibitorId, Integer userId, LocalDateTime updateTime){
        LambdaUpdateWrapper updateWrapper = new LambdaUpdateWrapper<ExpoBoothOrder>()
                .eq(ExpoBoothOrder::getExhibitorId, exhibitorId)
                .eq(ExpoBoothOrder::getIsDeleted, CommonStatus.DeleteEnum.NO.getValue())
                .set(ExpoBoothOrder::getIsDeleted, CommonStatus.DeleteEnum.YES.getValue())
                .set(BaseNotTenantEntity::getUpdateUser, userId)
                .set(BaseNotTenantEntity::getUpdateTime, updateTime);
        this.update(updateWrapper);
    }

}
