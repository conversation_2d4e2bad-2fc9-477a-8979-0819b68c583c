/**
 * Copyright © 2020 ECHRONOS Corp. All rights reserved.
 * This software is proprietary to and embodies the confidential
 * technology of ECHRONOS Corp.  Possession, use, or copying
 * of this software and media is authorized only pursuant to a
 * valid written license from ECHRONOS Corp or an authorized sublicensor.
 */
package com.echronos.expo.manager;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.echronos.commons.enums.CommonStatus;
import org.springframework.stereotype.Component;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.echronos.expo.model.ExpoAppointmentTime;
import com.echronos.expo.dao.ExpoAppointmentTimeDao;

import java.util.List;


/**
 * ExpoAppointmentTime Manager
 * 
 * <AUTHOR>
 * @date 2025-08-07
 */
@Component
public class ExpoAppointmentTimeManager extends ServiceImpl<ExpoAppointmentTimeDao, ExpoAppointmentTime> {

    /**
     * 根据预约ID查询时间段
     *
     * @param appointmentIds
     * @return
     */
    public List<ExpoAppointmentTime> queryByAppointmentId(List<Integer> appointmentIds) {
        LambdaQueryWrapper<ExpoAppointmentTime> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(ExpoAppointmentTime::getAppointmentId, appointmentIds)
                .eq(ExpoAppointmentTime::getIsDeleted, CommonStatus.DeleteEnum.NO.getValue())
                .orderByAsc(ExpoAppointmentTime::getStartTime);
        return this.list(queryWrapper);
    }
}
