/**
 * Copyright © 2020 ECHRONOS Corp. All rights reserved.
 * This software is proprietary to and embodies the confidential
 * technology of ECHRONOS Corp.  Possession, use, or copying
 * of this software and media is authorized only pursuant to a
 * valid written license from ECHRONOS Corp or an authorized sublicensor.
 */
package com.echronos.expo.manager;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.echronos.commons.enums.CommonStatus;
import org.springframework.stereotype.Component;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.echronos.expo.model.ExpoTenancySku;
import com.echronos.expo.dao.ExpoTenancySkuDao;

import java.awt.*;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;


/**
 * ExpoTenancySkuConfigure Manager
 * 
 * <AUTHOR>
 * @date 2025-08-09
 */
@Component
public class ExpoTenancySkuManager extends ServiceImpl<ExpoTenancySkuDao, ExpoTenancySku> {

    /**
     * 根据主键ID查询商品信息
     * @param id
     * @param companyId
     * @return
     */
    public ExpoTenancySku queryExpoTenancySkuById(Integer id, Integer companyId) {
        LambdaQueryWrapper<ExpoTenancySku> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(ExpoTenancySku::getId, id)
                .eq(ExpoTenancySku::getIsDeleted, CommonStatus.DeleteEnum.NO.getValue())
                .eq(Objects.nonNull(companyId), ExpoTenancySku::getCompanyId, companyId);
        return getOne(lambdaQueryWrapper);
    }


    /**
     * 删除商品信息
     * @param id
     * @param userId
     */
    public void updateDel(Integer id, Integer userId){
        LambdaUpdateWrapper<ExpoTenancySku> lambdaUpdateWrapper = new LambdaUpdateWrapper<>();
        lambdaUpdateWrapper.eq(ExpoTenancySku::getId, id)
                .eq(ExpoTenancySku::getIsDeleted, CommonStatus.DeleteEnum.NO.getValue())
                .set(ExpoTenancySku::getIsDeleted, CommonStatus.DeleteEnum.YES.getValue())
                .set(ExpoTenancySku::getUpdateTime, LocalDateTime.now())
                .set(ExpoTenancySku::getUpdateUser, userId);
        update(lambdaUpdateWrapper);
    }

    /**
     * 根据展会ID查询商品信息
     * @param expoId
     * @param companyId
     * @return
     */
    public List<ExpoTenancySku> queryTenancySkuByExpoId(Integer expoId, Integer companyId) {
        LambdaQueryWrapper<ExpoTenancySku> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(ExpoTenancySku::getIsDeleted, CommonStatus.DeleteEnum.NO.getValue())
                .eq(ExpoTenancySku::getExpoId, expoId)
                .eq(Objects.nonNull(companyId), ExpoTenancySku::getCompanyId, companyId);
        return list(lambdaQueryWrapper);
    }


}
