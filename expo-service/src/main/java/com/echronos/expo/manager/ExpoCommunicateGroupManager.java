/**
 * Copyright © 2020 ECHRONOS Corp. All rights reserved.
 * This software is proprietary to and embodies the confidential
 * technology of ECHRONOS Corp.  Possession, use, or copying
 * of this software and media is authorized only pursuant to a
 * valid written license from ECHRONOS Corp or an authorized sublicensor.
 */
package com.echronos.expo.manager;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.echronos.commons.enums.CommonStatus;
import com.echronos.expo.dto.ExpoCommunicateGroupDTO;
import org.springframework.stereotype.Component;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.echronos.expo.model.ExpoCommunicateGroup;
import com.echronos.expo.dao.ExpoCommunicateGroupDao;


/**
 * ExpoCommunicateGroup Manager
 * 
 * <AUTHOR>
 * @date 2025-08-15
 */
@Component
public class ExpoCommunicateGroupManager extends ServiceImpl<ExpoCommunicateGroupDao, ExpoCommunicateGroup> {

    /**
     * 查询建群记录
     *
     * @param dto
     * @return
     */
    public ExpoCommunicateGroup queryOne(ExpoCommunicateGroupDTO dto) {
        LambdaQueryWrapper<ExpoCommunicateGroup> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ExpoCommunicateGroup::getIsDeleted, CommonStatus.DeleteEnum.NO.getValue())
                .eq(ExpoCommunicateGroup::getBusinessId, dto.getBusinessId())
                .eq(ExpoCommunicateGroup::getBusinessType, dto.getBusinessType())
                .eq(ExpoCommunicateGroup::getCreateUser, dto.getCreateUser())
                .eq(ExpoCommunicateGroup::getCompanyId, dto.getCompanyId());
        return this.getOne(queryWrapper);
    }
}
