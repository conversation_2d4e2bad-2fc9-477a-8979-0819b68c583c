/**
 * Copyright © 2020 ECHRONOS Corp. All rights reserved.
 * This software is proprietary to and embodies the confidential
 * technology of ECHRONOS Corp.  Possession, use, or copying
 * of this software and media is authorized only pursuant to a
 * valid written license from ECHRONOS Corp or an authorized sublicensor.
 */
package com.echronos.expo.manager;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.echronos.commons.utils.CopyObjectUtils;
import com.echronos.expo.dto.ExpoAttachmentFileDTO;
import com.echronos.expo.enums.ExpoAttachmentFileEnum;
import com.echronos.expo.vo.ExpoAttachmentFileVO;
import org.springframework.stereotype.Component;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.echronos.expo.model.ExpoAttachmentFile;
import com.echronos.expo.dao.ExpoAttachmentFileDao;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;


/**
 * ExpoAttachmentFile Manager
 * 
 * <AUTHOR>
 * @date 2025-08-01
 */
@Component
public class ExpoAttachmentFileManager extends ServiceImpl<ExpoAttachmentFileDao, ExpoAttachmentFile> {

    /**
     * 获取附件列表
     * @param expoId 展会ID
     * @param businessId 业务ID
     * @param type 类型
     * @return
     */
    public List<ExpoAttachmentFileVO> getExpoAttachmentFileList(Integer expoId, Integer businessId, ExpoAttachmentFileEnum.Type type) {
        LambdaQueryWrapper<ExpoAttachmentFile> queryWrapper = new LambdaQueryWrapper<ExpoAttachmentFile>()
                .eq(null != expoId, ExpoAttachmentFile::getExpoId, expoId)
                .eq(ExpoAttachmentFile::getBusinessId, businessId)
                .eq(ExpoAttachmentFile::getType, type.getCode());
        List<ExpoAttachmentFile> list = list(queryWrapper);
        List<ExpoAttachmentFileVO> voList = null;
        if(CollectionUtil.isNotEmpty(list)){
            voList = CopyObjectUtils.copyAlistToBlist(list, ExpoAttachmentFileVO.class);
        }
        return voList;
    }


    /**
     * 添加或更新附件
     * @param expoId 展会ID
     * @param businessId 业务ID
     * @param dtoList 附件列表
     * @param type 附件类型
     */
    public void addOrUpdateAttachmentFile(Integer expoId, Integer businessId, List<ExpoAttachmentFileDTO> dtoList, ExpoAttachmentFileEnum.Type type) {
        List<ExpoAttachmentFileVO> existFileList = getExpoAttachmentFileList(expoId, businessId, type);
        List<Integer> removeIds = new ArrayList<>();
        if(CollectionUtil.isNotEmpty(existFileList)){
            removeIds = existFileList.stream().map(ExpoAttachmentFileVO::getId).collect(Collectors.toList());
        }
        List<ExpoAttachmentFile> addOrUpdateList = new ArrayList<>();
        if(CollectionUtil.isNotEmpty(dtoList)){
            for(ExpoAttachmentFileDTO file : dtoList){
                if(null != file.getId() && removeIds.contains(file.getId())){
                    removeIds.remove(file.getId());
                }
                file.setExpoId(expoId);
                file.setBusinessId(businessId);
                file.setType(type.getCode());
                addOrUpdateList.add(file);
            }
        }
        if(CollectionUtil.isNotEmpty(removeIds)){
            // 移除附件
            this.removeByIds(removeIds);
        }
        if(CollectionUtil.isNotEmpty(addOrUpdateList)){
            this.saveOrUpdateBatch(addOrUpdateList);
        }
    }


    /**
     * 添加附件
     * @param expoId 展会ID
     * @param businessId 业务ID
     * @param type  类型
     * @param list 附件集合
     */
    public void addExpoAttachmentFile(Integer expoId, Integer businessId, ExpoAttachmentFileEnum.Type type, List<ExpoAttachmentFileDTO> list) {
        if(CollectionUtil.isEmpty(list)){
            return;
        }
        List<ExpoAttachmentFile> expoAttachmentFileList = new ArrayList<>();
        for(ExpoAttachmentFileDTO attachmentFileDTO : list){
            ExpoAttachmentFile attachmentFile = CopyObjectUtils.copyAtoB(attachmentFileDTO, ExpoAttachmentFile.class);
            attachmentFile.setExpoId(expoId);
            attachmentFile.setBusinessId(businessId);
            attachmentFile.setType(type.getCode());
            expoAttachmentFileList.add(attachmentFile);
        }
        saveBatch(expoAttachmentFileList);
    }


}
