/**
 * Copyright © 2020 ECHRONOS Corp. All rights reserved.
 * This software is proprietary to and embodies the confidential
 * technology of ECHRONOS Corp.  Possession, use, or copying
 * of this software and media is authorized only pursuant to a
 * valid written license from ECHRONOS Corp or an authorized sublicensor.
 */
package com.echronos.expo.manager;

import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Component;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.echronos.expo.model.ExpoBoothOrderDetail;
import com.echronos.expo.dao.ExpoBoothOrderDetailDao;

import javax.annotation.Resource;
import java.time.LocalDateTime;


/**
 * ExpoBoothOrderDetail Manager
 * 
 * <AUTHOR>
 * @date 2025-08-01
 */
@Component
public class ExpoBoothOrderDetailManager extends ServiceImpl<ExpoBoothOrderDetailDao, ExpoBoothOrderDetail> {

    @Resource
    private ExpoBoothOrderDetailDao expoBoothOrderDetailDao;

    /**
     * 根据展商ID删除订单明细
     * @param exhibitorId 展商ID
     * @param userId 用户ID
     * @param updateTime 更新时间
     */
    public void removeByExhibitorId(Integer exhibitorId, Integer userId, LocalDateTime updateTime){
        expoBoothOrderDetailDao.removeByExhibitorId(exhibitorId, userId, updateTime);
    }

}
