/**
 * Copyright © 2020 ECHRONOS Corp. All rights reserved.
 * This software is proprietary to and embodies the confidential
 * technology of ECHRONOS Corp.  Possession, use, or copying
 * of this software and media is authorized only pursuant to a
 * valid written license from ECHRONOS Corp or an authorized sublicensor.
 */
package com.echronos.expo.manager;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.echronos.expo.dao.ExpoAudienceExtendDao;
import com.echronos.expo.model.ExpoAudienceExtend;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Set;


/**
 * EchExpoAudienceExtend Manager
 *
 * <AUTHOR>
 * @date 2025-05-14
 */
@Component
public class ExpoAudienceExtendManager extends ServiceImpl<ExpoAudienceExtendDao, ExpoAudienceExtend> {
    @Resource
    private ExpoAudienceExtendDao expoAudienceExtendDao;

    /**
     * 查询展会自定义表单字段
     *
     * @param expoId
     * @param formCode
     * @return
     */
    public Set<String> fieldList(Integer expoId, String formCode) {
        return expoAudienceExtendDao.fieldList(expoId, formCode);
    }
}
