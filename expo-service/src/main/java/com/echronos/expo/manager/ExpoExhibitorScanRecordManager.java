/**
 * Copyright © 2020 ECHRONOS Corp. All rights reserved.
 * This software is proprietary to and embodies the confidential
 * technology of ECHRONOS Corp.  Possession, use, or copying
 * of this software and media is authorized only pursuant to a
 * valid written license from ECHRONOS Corp or an authorized sublicensor.
 */
package com.echronos.expo.manager;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.echronos.commons.enums.CommonStatus;
import com.echronos.expo.dao.ExpoExhibitorScanRecordDao;
import com.echronos.expo.model.ExpoExhibitorScanRecord;
import com.echronos.expo.model.ext.ExpoExhibitorScanCountExt;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.util.List;


/**
 * ExpoExhibitorScanRecord Manager
 *
 * <AUTHOR>
 * @date 2025-08-15
 */
@Component
public class ExpoExhibitorScanRecordManager extends ServiceImpl<ExpoExhibitorScanRecordDao, ExpoExhibitorScanRecord> {

    @Resource
    private ExpoExhibitorScanRecordDao expoExhibitorScanRecordDao;

    /**
     * 统计展商扫码次数
     *
     * @param expoId 展会ID
     * @return 扫码次数
     */
    public Integer getExhibitorScanRecordsCount(Integer expoId) {
        LambdaQueryWrapper<ExpoExhibitorScanRecord> queryWrapper = new LambdaQueryWrapper<ExpoExhibitorScanRecord>()
                .eq(ExpoExhibitorScanRecord::getExpoId, expoId)
                .eq(ExpoExhibitorScanRecord::getIsDeleted, CommonStatus.DeleteEnum.NO);
        return this.count(queryWrapper);
    }

    /**
     * 按日期统计展商扫码次数
     *
     * @param expoId 展会ID
     * @param companyId 公司ID
     * @return 日期和扫码次数的映射
     */
    public List<ExpoExhibitorScanCountExt> getScanRecordsByDate(Integer expoId, Integer companyId) {
        return expoExhibitorScanRecordDao.countScanRecordsByDate(expoId, companyId);
    }

    /**
     * 按小时统计展商扫码次数
     *
     * @param expoId 展会ID
     * @param companyId 公司ID
     * @param statisticsDate 统计日期
     * @return 小时和扫码次数的映射
     */
    public List<ExpoExhibitorScanCountExt> getScanRecordsByHour(Integer expoId, Integer companyId, LocalDate statisticsDate) {
        return expoExhibitorScanRecordDao.countScanRecordsByHour(expoId, companyId, statisticsDate);
    }

    /**
     * 按展商统计扫码次数
     *
     * @param expoId 展会ID
     * @param companyId 公司ID
     * @return 展商ID和扫码次数的映射
     */
    public List<ExpoExhibitorScanCountExt> getScanRecordsByExhibitor(Integer expoId, Integer companyId) {
        return expoExhibitorScanRecordDao.countScanRecordsByExhibitor(expoId, companyId);
    }
}
