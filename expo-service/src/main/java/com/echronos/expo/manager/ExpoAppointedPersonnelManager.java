/**
 * Copyright © 2020 ECHRONOS Corp. All rights reserved.
 * This software is proprietary to and embodies the confidential
 * technology of ECHRONOS Corp.  Possession, use, or copying
 * of this software and media is authorized only pursuant to a
 * valid written license from ECHRONOS Corp or an authorized sublicensor.
 */
package com.echronos.expo.manager;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.echronos.commons.enums.CommonStatus;
import com.echronos.expo.dao.ExpoAppointedPersonnelDao;
import com.echronos.expo.dto.ExpoAppointedPersonnelDTO;
import com.echronos.expo.model.ExpoAppointedPersonnel;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;


/**
 * ExpoAppointedPersonnel Manager
 *
 * <AUTHOR>
 * @date 2025-08-07
 */
@Component
public class ExpoAppointedPersonnelManager extends ServiceImpl<ExpoAppointedPersonnelDao, ExpoAppointedPersonnel> {

    @Resource
    private ExpoAppointedPersonnelDao expoAppointedPersonnelDao;

    /**
     * 查询已设置可预约人员
     *
     * @param dto 参数
     * @return List<ExpoAppointedPersonnel>
     */
    public List<ExpoAppointedPersonnel> queryList(ExpoAppointedPersonnelDTO dto) {
        dto.setIsDeleted(CommonStatus.DeleteEnum.NO.getValue());
        return expoAppointedPersonnelDao.queryList(dto);
    }

    /**
     * 根据id查询
     *
     * @param id
     * @return
     */
    public ExpoAppointedPersonnel getById(Integer id) {
        LambdaQueryWrapper<ExpoAppointedPersonnel> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ExpoAppointedPersonnel::getId, id)
                .eq(ExpoAppointedPersonnel::getIsDeleted, CommonStatus.DeleteEnum.NO.getValue());
        return this.getOne(queryWrapper);
    }

    /**
     * 根据参数查询
     *
     * @param dto
     * @return
     */
    public List<ExpoAppointedPersonnel> queryBy(ExpoAppointedPersonnelDTO dto) {
        LambdaQueryWrapper<ExpoAppointedPersonnel> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ExpoAppointedPersonnel::getIsDeleted, CommonStatus.DeleteEnum.NO.getValue())
                .eq(ExpoAppointedPersonnel::getExpoId, dto.getExpoId())
                .eq(null != dto.getBusinessId(), ExpoAppointedPersonnel::getBusinessId, dto.getBusinessId())
                .eq(ExpoAppointedPersonnel::getBusinessType, dto.getBusinessType());
        return this.list(queryWrapper);
    }

    /**
     * 根据id删除
     *
     * @param id
     */
    public void delById(Integer id) {
        LambdaUpdateWrapper<ExpoAppointedPersonnel> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(ExpoAppointedPersonnel::getId, id)
                .eq(ExpoAppointedPersonnel::getIsDeleted, CommonStatus.DeleteEnum.NO.getValue())
                .set(ExpoAppointedPersonnel::getIsDeleted, CommonStatus.DeleteEnum.YES.getValue());
        this.update(updateWrapper);
    }

    /**
     * 查询可预约人员
     *
     * @param dto
     * @return
     */
    public ExpoAppointedPersonnel queryOne(ExpoAppointedPersonnelDTO dto) {
        LambdaQueryWrapper<ExpoAppointedPersonnel> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ExpoAppointedPersonnel::getIsDeleted, CommonStatus.DeleteEnum.NO.getValue())
                .eq(ExpoAppointedPersonnel::getExpoId, dto.getExpoId())
                .eq(ExpoAppointedPersonnel::getId, dto.getId())
                .eq(ExpoAppointedPersonnel::getBusinessType, dto.getBusinessType());
        return this.getOne(queryWrapper);
    }
}
