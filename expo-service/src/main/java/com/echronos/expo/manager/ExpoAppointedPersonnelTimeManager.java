/**
 * Copyright © 2020 ECHRONOS Corp. All rights reserved.
 * This software is proprietary to and embodies the confidential
 * technology of ECHRONOS Corp.  Possession, use, or copying
 * of this software and media is authorized only pursuant to a
 * valid written license from ECHRONOS Corp or an authorized sublicensor.
 */
package com.echronos.expo.manager;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.echronos.commons.enums.CommonStatus;
import com.echronos.expo.model.ext.ExpoAppointedPersonnelTimeExt;
import org.springframework.stereotype.Component;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.echronos.expo.model.ExpoAppointedPersonnelTime;
import com.echronos.expo.dao.ExpoAppointedPersonnelTimeDao;

import javax.annotation.Resource;
import java.util.List;


/**
 * ExpoAppointedPersonnelTime Manager
 * 
 * <AUTHOR>
 * @date 2025-08-07
 */
@Component
public class ExpoAppointedPersonnelTimeManager extends ServiceImpl<ExpoAppointedPersonnelTimeDao, ExpoAppointedPersonnelTime> {

    @Resource
    private ExpoAppointedPersonnelTimeDao expoAppointedPersonnelTimeDao;

    /**
     * 查询已设置的时间段（包含是否被预约标识）
     *
     * @param personnelId
     * @return
     */
    public List<ExpoAppointedPersonnelTimeExt> queryBy(Integer personnelId) {
        return expoAppointedPersonnelTimeDao.queryBy(personnelId);
    }

    /**
     * 根据ID删除
     *
     * @param ids
     */
    public void delById(List<Integer> ids) {
        LambdaUpdateWrapper<ExpoAppointedPersonnelTime> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.in(ExpoAppointedPersonnelTime::getId, ids)
                .set(ExpoAppointedPersonnelTime::getIsDeleted, CommonStatus.DeleteEnum.YES.getValue());
        this.update(updateWrapper);
    }

    /**
     * 根据人员ID删除
     *
     * @param personnelId
     */
    public void delByPersonnelId(Integer personnelId) {
        LambdaUpdateWrapper<ExpoAppointedPersonnelTime> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(ExpoAppointedPersonnelTime::getPersonnelId, personnelId)
                .set(ExpoAppointedPersonnelTime::getIsDeleted, CommonStatus.DeleteEnum.YES.getValue());
        this.update(updateWrapper);
    }
}
