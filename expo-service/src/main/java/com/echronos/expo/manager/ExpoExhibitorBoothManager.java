/**
 * Copyright © 2020 ECHRONOS Corp. All rights reserved.
 * This software is proprietary to and embodies the confidential
 * technology of ECHRONOS Corp.  Possession, use, or copying
 * of this software and media is authorized only pursuant to a
 * valid written license from ECHRONOS Corp or an authorized sublicensor.
 */
package com.echronos.expo.manager;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.echronos.commons.enums.CommonStatus;
import com.echronos.expo.dto.ExpoExhibitorBoothDTO;
import com.echronos.expo.model.BaseNotTenantEntity;
import com.google.common.collect.Lists;
import org.springframework.stereotype.Component;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.echronos.expo.model.ExpoExhibitorBooth;
import com.echronos.expo.dao.ExpoExhibitorBoothDao;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;


/**
 * ExpoExhibitorBooth Manager
 * 
 * <AUTHOR>
 * @date 2025-08-06
 */
@Component
public class ExpoExhibitorBoothManager extends ServiceImpl<ExpoExhibitorBoothDao, ExpoExhibitorBooth> {

    @Resource
    private ExpoExhibitorBoothDao expoExhibitorBoothDao;

    /**
     * 获取展商展位信息
     * @param exhibitorId 展商ID
     * @return 展商展位信息
     */
    public List<ExpoExhibitorBooth> getListByExhibitorId(Integer exhibitorId) {
        LambdaQueryWrapper<ExpoExhibitorBooth> queryWrapper = new LambdaQueryWrapper<ExpoExhibitorBooth>()
                .eq(ExpoExhibitorBooth::getExhibitorId, exhibitorId)
                .eq(BaseNotTenantEntity::getIsDeleted, CommonStatus.DeleteEnum.NO.getValue());
        return this.list(queryWrapper);
    }

    /**
     * 获取展商展位详情
     * @param exhibitorId 展商ID
     * @return 展商展位详情
     */
    public List<ExpoExhibitorBoothDTO> getDetailByExhibitorId(Integer exhibitorId) {
        Map<Integer, List<ExpoExhibitorBoothDTO>> map = getDetailByExhibitorIdList(Lists.newArrayList(exhibitorId));
        return Optional.ofNullable(map.get(exhibitorId)).orElseGet(ArrayList::new);
    }

    /**
     * 获取展商展位详情
     * @param exhibitorIdList 展商ID列表
     * @return 展商展位详情
     */
    public Map<Integer, List<ExpoExhibitorBoothDTO>> getDetailByExhibitorIdList(List<Integer> exhibitorIdList) {
        List<ExpoExhibitorBoothDTO> list = expoExhibitorBoothDao.getDetailByExhibitorIdList(exhibitorIdList);
        Map<Integer, List<ExpoExhibitorBoothDTO>> map = list.stream().collect(
                Collectors.groupingBy(ExpoExhibitorBoothDTO::getExhibitorId));
        return Optional.ofNullable(map).orElseGet(HashMap::new);
    }

    /**
     * 删除展商展位
     * @param exhibitorId 展商ID
     * @param boothIds 展位ID列表
     * @param userId 用户ID
     * @param updateTime 更新时间
     */
    public void removeExhibitorBooth(Integer exhibitorId, List<Integer> boothIds, Integer userId, LocalDateTime updateTime) {
        LambdaUpdateWrapper<ExpoExhibitorBooth> updateWrapper = new LambdaUpdateWrapper<ExpoExhibitorBooth>()
                .eq(ExpoExhibitorBooth::getExhibitorId, exhibitorId)
                .in(ExpoExhibitorBooth::getId, boothIds)
                .set(BaseNotTenantEntity::getIsDeleted, CommonStatus.DeleteEnum.YES.getValue())
                .set(ExpoExhibitorBooth::getUpdateUser, userId)
                .set(ExpoExhibitorBooth::getUpdateTime, updateTime);
        this.update(updateWrapper);
    }

    /**
     * 删除展商展位
     * @param exhibitorId
     * @param userId
     * @param updateTime
     */
    public void removeByExhibitorId(Integer exhibitorId, Integer userId, LocalDateTime updateTime) {
        LambdaUpdateWrapper<ExpoExhibitorBooth> updateWrapper = new LambdaUpdateWrapper<ExpoExhibitorBooth>()
                .eq(ExpoExhibitorBooth::getExhibitorId, exhibitorId)
                .set(BaseNotTenantEntity::getIsDeleted, CommonStatus.DeleteEnum.YES.getValue())
                .set(ExpoExhibitorBooth::getUpdateUser, userId)
                .set(ExpoExhibitorBooth::getUpdateTime, updateTime);
        this.update(updateWrapper);
    }


}
