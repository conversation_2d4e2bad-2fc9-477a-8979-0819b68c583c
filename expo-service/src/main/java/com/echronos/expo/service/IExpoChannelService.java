package com.echronos.expo.service;

import com.echronos.commons.Result;
import com.echronos.expo.dto.ExpoChannelDTO;
import com.echronos.expo.dto.ExpoChannelPageDTO;
import com.echronos.expo.vo.ExpoChannelVO;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025/5/15 14:02
 * @ClassName IExpoChannelService
 */
public interface IExpoChannelService {


    /**
     * 分页查询渠道列表
     *
     * @param dto
     * @return
     */
    Result<List<ExpoChannelVO>> pageFor(ExpoChannelPageDTO dto);

    /**
     * 渠道列表
     *
     * @param dto
     * @return
     */
    Result<List<ExpoChannelVO>> channelList(ExpoChannelDTO dto);

    /**
     * 保存渠道
     *
     * @param dto
     * @return
     */
    Result save(ExpoChannelDTO dto);


    /**
     * 删除渠道
     *
     * @param dto
     * @return
     */
    Result del(ExpoChannelDTO dto);
}
