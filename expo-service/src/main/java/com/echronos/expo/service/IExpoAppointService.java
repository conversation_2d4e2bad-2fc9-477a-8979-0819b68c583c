package com.echronos.expo.service;

import com.echronos.commons.page.PageVO;
import com.echronos.expo.dto.ExpoAppointedPersonnelDTO;
import com.echronos.expo.dto.ExpoAppointedPersonnelTimeDTO;
import com.echronos.expo.dto.ExpoAppointmentDTO;
import com.echronos.expo.dto.ExpoExhibitorDTO;
import com.echronos.expo.model.ExpoAppointedPersonnel;
import com.echronos.expo.vo.appoint.*;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/8/7 11:19
 */
public interface IExpoAppointService {

    /**
     * 查询预约设置
     *
     * @param dto 参数
     * @return AppointmentSetVO
     */
    AppointmentSetVO getAppointSet(ExpoAppointedPersonnelDTO dto);

    /**
     * 设置时间
     *
     * @param dto 参数
     */
    void appointSetTime(ExpoAppointedPersonnelDTO dto);

    /**
     * 可预约人员回显
     *
     * @param dto 参数
     * @return List<ExpoAppointedPersonnelVO>
     */
    List<ExpoAppointedPersonnelVO> appointPersonnelEcho(ExpoAppointedPersonnelDTO dto);

    /**
     * 添加可预约人员
     *
     * @param dto 参数
     */
    void appointAddPersonnel(ExpoAppointedPersonnelDTO dto);

    /**
     * 删除可预约人员
     *
     * @param dto 参数
     */
    void appointDelPersonnel(ExpoAppointedPersonnelDTO dto);

    /**
     * 查询可预约人员
     *
     * @param dto 参数
     * @return List<AppointedPersonnelListVO>
     */
    List<AppointedPersonnelListVO> personnelList(ExpoAppointedPersonnelDTO dto);

    /**
     * 根据展商ID查询预约页面信息
     *
     * @param dto 参数
     * @return AppointPageInfoVO
     */
    AppointPageInfoVO pageInfo(ExpoExhibitorDTO dto);

    /**
     * 提交预约申请
     *
     * @param dto 参数
     */
    void appointSubmit(ExpoAppointmentDTO dto);

    /**
     * 查询预约人员的预约时间段
     *
     * @param dto 参数
     * @return List<ExpoAppointedPersonnelTimeVO>
     */
    List<ExpoAppointedPersonnelTimeVO> personnelTime(ExpoAppointedPersonnelTimeDTO dto);

    /**
     * 待处理预约列表
     *
     * @param dto 参数
     * @return List<AppointPendingListVO>
     */
    PageVO<AppointPendingListVO> pendingList(ExpoAppointmentDTO dto);

    /**
     * 展会预约时间列表
     *
     * @param dto 参数
     * @return List<AppointTimeListVO>
     */
    List<AppointTimeListVO> timeList(ExpoAppointmentDTO dto);

    /**
     * 预约详情
     *
     * @param dto 参数
     * @return AppointDetailVO
     */
    AppointDetailVO appointDetail(ExpoAppointmentDTO dto);

    /**
     * 处理预约
     *
     * @param dto
     */
    void appointHandle(ExpoAppointmentDTO dto);
}
