package com.echronos.expo.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.echronos.expo.dto.ExpoBoothDTO;
import com.echronos.expo.dto.ExpoBoothOrderDTO;
import com.echronos.expo.dto.ExpoBoothPageDTO;
import com.echronos.expo.vo.*;

import java.util.List;

/**
 * 展位服务接口
 *
 * <AUTHOR>
 * @date 2025-08-01
 */
public interface IExpoBoothService {

    /**
     * 展位统计
     * @param dto 查询参数
     * @return 结果
     */
    ExpoBoothStatisticsVO boothStatistics(ExpoBoothDTO dto);

    /**
     * 查询展位附件列表
     * @param dto 查询参数
     * @return 结果
     */
    List<ExpoAttachmentFileVO> getBoothAttachmentFiles(ExpoBoothDTO dto);

    /**
     * 展位列表（分页）
     * @param dto 查询参数
     * @return 结果
     */
    IPage<ExpoBoothVO> pageList(ExpoBoothPageDTO dto);

    /**
     * 展位详情
     * @param dto 查询参数
     * @return  结果
     */
    ExpoBoothDetailVO detail(ExpoBoothPageDTO dto);

    /**
     * 新增/编辑展位
     *
     * @param dto 展位参数
     * @return 结果
     */
    void saveOrUpdateBooth(ExpoBoothDTO dto);

    /**
     * 删除展位
     * @param dto 展位参数
     */
    void delBooth(ExpoBoothDTO dto);

    /**
     * 添加展位订单
     * @param dto
     */
    void addOrder(ExpoBoothOrderDTO dto);

    /**
     * 导入展位数据
     * @param dto
     * @return
     */
    ExpoBoothImportVO importTemplate(ExpoBoothDTO dto);

    /**
     * 展位销售率
     */
    ExpoBoothConversionRateVO expoBoothSale(ExpoBoothDTO dto);

    /**
     * 展位类型分布
     * @param dto
     */
    List<ExpoBoothTypeVO> expoBootType(ExpoBoothDTO dto);

    /**
     * 展位预定率
     * @param dto
     */
    ExpoBoothReserveRateVO expoBoothReserveRate(ExpoBoothDTO dto);
}
