package com.echronos.expo.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.echronos.expo.manager.ExpoAudienceManager;
import com.echronos.expo.manager.ExpoBoothManager;
import com.echronos.expo.manager.ExpoExhibitorManager;
import com.echronos.expo.manager.ExpoInfoManager;
import com.echronos.expo.model.ext.ExpoChannelAudienceExt;
import com.echronos.expo.model.ext.ExpoIndexCountExt;
import com.echronos.expo.service.ExpoStatisticsService;
import com.echronos.expo.vo.statistics.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025-08-15 15:15
 */
@Slf4j
@Service
public class ExpoStatisticsServiceImpl implements ExpoStatisticsService {

    @Resource
    private ExpoInfoManager expoInfoManager;
    @Resource
    private ExpoExhibitorManager expoExhibitorManager;
    @Resource
    private ExpoAudienceManager expoAudienceManager;
    @Resource
    private ExpoBoothManager expoBoothManager;

    @Override
    public ExpoIndexCountVO getIndexExpoCount(Integer companyId) {
        ExpoIndexCountExt indexCountExt = expoInfoManager.getExpoIndexCount(companyId);
        BigDecimal growthTotal = indexCountExt.getCalculateTotalTwo().subtract(indexCountExt.getCalculateTotalOne());
        ExpoIndexCountVO expoIndexCountVO = new ExpoIndexCountVO();
        expoIndexCountVO.setTotal(indexCountExt.getTotal());
        expoIndexCountVO.setGrowthTotal(growthTotal);
        return expoIndexCountVO;
    }

    @Override
    public ExpoIndexCountVO getIndexExhibitorCount(Integer companyId) {
        ExpoIndexCountExt indexCountExt = expoExhibitorManager.getIndexExhibitorCount(companyId);
        BigDecimal growthTotal = indexCountExt.getCalculateTotalTwo().subtract(indexCountExt.getCalculateTotalOne());
        ExpoIndexCountVO expoIndexCountVO = new ExpoIndexCountVO();
        expoIndexCountVO.setTotal(indexCountExt.getTotal());
        expoIndexCountVO.setGrowthTotal(growthTotal);
        return expoIndexCountVO;
    }

    @Override
    public ExpoIndexCountVO getIndexAudienceCount(Integer companyId) {
        ExpoIndexCountExt indexCountExt = expoAudienceManager.getIndexAudienceCount(companyId);
        BigDecimal growthTotal = indexCountExt.getCalculateTotalTwo().subtract(indexCountExt.getCalculateTotalOne());
        ExpoIndexCountVO expoIndexCountVO = new ExpoIndexCountVO();
        expoIndexCountVO.setTotal(indexCountExt.getTotal());
        expoIndexCountVO.setGrowthTotal(growthTotal);
        return expoIndexCountVO;
    }

    @Override
    public ExpoIndexCountVO getIndexBoothCount(Integer companyId) {
        ExpoIndexCountExt indexCountExt = expoBoothManager.getIndexBoothCount(companyId);
        BigDecimal growthTotal = indexCountExt.getCalculateTotalOne().divide(indexCountExt.getTotal()).multiply(BigDecimal.valueOf(100));
        ExpoIndexCountVO expoIndexCountVO = new ExpoIndexCountVO();
        expoIndexCountVO.setTotal(indexCountExt.getTotal());
        expoIndexCountVO.setGrowthTotal(growthTotal);
        return expoIndexCountVO;
    }

    @Override
    public ExpoAudienceStatisticsVO getExpoAudienceStatistics(Integer expoId) {
        Integer audienceCount = expoAudienceManager.getAudienceCount(expoId);
        Integer signInCount = expoAudienceManager.getAudienceSignInCount(expoId);
        BigDecimal signInRate = BigDecimal.valueOf(signInCount).divide(BigDecimal.valueOf(audienceCount), 2, BigDecimal.ROUND_HALF_UP);
        Integer accumulatedSignInCount = expoAudienceManager.getAudienceCumulativeSignInCount(expoId);
        return ExpoAudienceStatisticsVO.builder()
                .audienceCount(audienceCount)
                .signInCount(signInCount)
                .signInRate(signInRate)
                .accumulatedSignInCount(accumulatedSignInCount)
                .build();
    }

    @Override
    public ExpoAudienceSignNotSignInRateVO getExpoAudienceSignInNotSignInCount(Integer expoId) {
        Integer audienceCount = expoAudienceManager.getAudienceCount(expoId);
        Integer signInCount = expoAudienceManager.getAudienceSignInCount(expoId);
        BigDecimal signInRate = BigDecimal.valueOf(signInCount).divide(BigDecimal.valueOf(audienceCount), 2, BigDecimal.ROUND_HALF_UP);
        return ExpoAudienceSignNotSignInRateVO
                .builder()
                .signInRate(signInRate)
                .notSignInRate(new BigDecimal("100").subtract(signInRate))
                .build();
    }

    @Override
    public List<ExpoAudienceChannelVO> getExpoAudienceChannelList(Integer expoId) {
        List<ExpoAudienceChannelVO> voList = new ArrayList<>();
        List<ExpoChannelAudienceExt> channelAudienceCountList = expoAudienceManager.getChannelAudienceCountList(expoId);
        if(CollectionUtil.isNotEmpty(channelAudienceCountList)){
            // 总观众人数
            Integer audienceCount = expoAudienceManager.getAudienceCount(expoId);
            channelAudienceCountList.forEach(cac -> {
                ExpoAudienceChannelVO vo = new ExpoAudienceChannelVO();
                // 签到率：渠道签到人数/渠道观众人数
                BigDecimal signInRate = new BigDecimal(cac.getSignInCount())
                        .divide(BigDecimal.valueOf(cac.getRegisterCount()), 2, BigDecimal.ROUND_HALF_UP);
                // 注册比率：渠道观众人数/总人数
                BigDecimal registerRate = new BigDecimal(cac.getRegisterCount())
                        .divide(BigDecimal.valueOf(audienceCount), 2, BigDecimal.ROUND_HALF_UP);
                vo.setChannelName(cac.getChannelName());
                vo.setRegisterCount(cac.getRegisterCount());
                vo.setRegisterRate(registerRate);
                vo.setSignInRate(signInRate);
            });
        }
        return voList;
    }

}
