package com.echronos.expo.service;

import com.echronos.expo.dto.ExpoTenancySkuDTO;
import com.echronos.expo.vo.ExpoPageVO;
import com.echronos.expo.vo.tenancysku.ExpoTenancySkuVO;

/**
 * <AUTHOR>
 * date2025/8/9 14:01
 */
public interface IExpoTenancySkuService {

    /**
     * 列表查询
     *
     * @param dto
     */
    ExpoPageVO<ExpoTenancySkuVO> page(ExpoTenancySkuDTO dto);

    /**
     * 新增商品
     *
     * @param dto
     */
    void add(ExpoTenancySkuDTO dto);

    /**
     * 删除商品
     *
     * @param dto
     */
    void del(ExpoTenancySkuDTO dto);
}
