package com.echronos.expo.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.echronos.commons.Result;
import com.echronos.commons.exception.BusinessException;
import com.echronos.commons.utils.CopyObjectUtils;
import com.echronos.expo.constants.GatewayRoutingConstants;
import com.echronos.expo.dto.*;
import com.echronos.expo.enums.ExpoAttachmentFileEnum;
import com.echronos.expo.enums.ExpoFormEnums;
import com.echronos.expo.enums.ExpoResultCode;
import com.echronos.expo.enums.ExpoStatusEnums;
import com.echronos.expo.manager.*;
import com.echronos.expo.model.*;
import com.echronos.expo.model.ext.ExpoInfoExt;
import com.echronos.expo.service.IExpoService;
import com.echronos.expo.util.CityTimezoneFinder;
import com.echronos.expo.vo.*;
import com.echronos.imc.api.resp.CscCustomerServiceResp;
import com.echronos.imc.api.resp.ImcSessionResp;
import com.echronos.tenant.api.feign.TenantInfoFeignClient;
import com.echronos.tenant.api.req.BatchTenantDetailReq;
import com.echronos.tenant.api.resp.TenantInfoResp;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZoneOffset;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2025/5/13 16:09
 * @ClassName ExpoService
 */
@Slf4j
@Service
public class ExpoService implements IExpoService {

    @Resource
    private ExpoInfoManager expoInfoManager;
    @Resource
    private ExpoChannelConfigManager expoChannelConfigManager;
    @Resource
    private ExpoChannelManager expoChannelManager;
    @Resource
    private ExpoFormManager expoFormManager;
    @Resource
    private TenantInfoFeignClient tenantInfoFeignClient;
    @Resource
    private ExpoAttachmentFileManager expoAttachmentFileManager;
    @Resource
    private ExpoCommunicateGroupManager expoCommunicateGroupManager;
    @Resource
    private FeignCommonManager feignCommonManager;

    @Value("${remote.h5.domain}")
    private String gateway;

    @Override
    public Result edit(ExpoInfoDTO dto) {
        if (Objects.nonNull(dto.getId())) {
            ExpoInfo expoInfo = checkAndExpo(dto.getId(), dto.getCompanyId());
            if (!expoInfo.getExpoStatus().equals(ExpoStatusEnums.UN_STARTED.getCode())) {
                throw new BusinessException(ExpoResultCode.ExpoResultEnum.EXPO_CANT_EDIT.getCode(),
                        ExpoResultCode.ExpoResultEnum.EXPO_CANT_EDIT.getMessage());
            }
        }
        ZoneId zoneId = LocaleContextHolder.getTimeZone().toZoneId();
        if (Objects.nonNull(dto.getCityCode()) && Objects.nonNull(CityTimezoneFinder.CITY_TO_ZONE.get(dto.getCityCode().toString()))) {
            zoneId = CityTimezoneFinder.CITY_TO_ZONE.get(dto.getCityCode().toString());
        }
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime startTime = dto.getStartTime().atZone(zoneId).toInstant().atOffset(ZoneOffset.UTC).toLocalDateTime();
        LocalDateTime endTime = dto.getEndTime().atZone(zoneId).toInstant().atOffset(ZoneOffset.UTC).toLocalDateTime();
        System.out.println(startTime);
        if (startTime.compareTo(now) > 0) {
            dto.setExpoStatus(ExpoStatusEnums.UN_STARTED.getCode());
        } else if (startTime.compareTo(now) <= 0 && endTime.compareTo(now) >= 0) {
            dto.setExpoStatus(ExpoStatusEnums.STARTED.getCode());
        } else {
            dto.setExpoStatus(ExpoStatusEnums.ENDED.getCode());
        }
        dto.setZoneId(zoneId.getId());
        dto.setZoneIdEndTime(endTime);
        dto.setZoneIdStartTime(startTime);
        expoInfoManager.saveOrUpdate(dto);
        // 添加附件
        expoAttachmentFileManager.addOrUpdateAttachmentFile(dto.getId(), dto.getId(), dto.getHandbookAttachmentFiles(),  ExpoAttachmentFileEnum.Type.EXPO_HANDBOOK);
        expoAttachmentFileManager.addOrUpdateAttachmentFile(dto.getId(), dto.getId(), dto.getTemplateAttachmentFiles(),  ExpoAttachmentFileEnum.Type.EXPO_POSTER_TEMPLATE);
        return Result.build();
    }

    @Override
    public Result<List<ExpoInfoVO>> pageFor(ExpoInfoPageDTO dto) {
        Page<ExpoInfo> infoPage = new Page<>(dto.getPageNo(), dto.getPageSize());
        Page<ExpoInfo> page = expoInfoManager.page(infoPage, new LambdaQueryWrapper<ExpoInfo>()
                .eq(ExpoInfo::getCompanyId, dto.getCompanyId())
                .orderByDesc(BaseEntity::getCreateTime)
        );
        List<ExpoInfoVO> vos = CopyObjectUtils.copyAlistToBlist(page.getRecords(), ExpoInfoVO.class);
        vos.stream().forEach(r -> {
            Integer expoStatus = r.getExpoStatus();
            ExpoStatusEnums statusEnums = ExpoStatusEnums.getByCode(expoStatus);
            if (Objects.nonNull(statusEnums)) {
                r.setExpoStatusStr(statusEnums.getMessage());
            }
        });
        return Result.build(vos, page.getTotal());
    }

    @Override
    public Result<List<ExpoInfoVO>> pageList(ExpoInfoPageDTO dto) {
        Page<ExpoInfo> page = new Page<>(dto.getPageNo(), dto.getPageSize());
        List<ExpoInfoExt> expoInfoList = expoInfoManager.pageList(page, dto);
        List<ExpoInfoVO> voList = CopyObjectUtils.copyAlistToBlist(expoInfoList, ExpoInfoVO.class);
        voList.stream().forEach(r -> {
            Integer expoStatus = r.getExpoStatus();
            ExpoStatusEnums statusEnums = ExpoStatusEnums.getByCode(expoStatus);
            if (Objects.nonNull(statusEnums)) {
                r.setExpoStatusStr(statusEnums.getMessage());
            }
        });
        return Result.build(voList, page.getTotal());
    }

    @Override
    public List<ExpoInfoCopyFormVO> getListCopyFormList(ExpoInfoDTO dto) {
        List<ExpoInfoCopyFormVO> voList = new ArrayList<>();
        List<ExpoInfo> list = expoInfoManager.getListByCompanyId(dto.getCompanyId());
        if(CollectionUtils.isEmpty(list)){
            return voList;
        }
        List<Integer> expoIds = list.stream().map(expoInfo -> expoInfo.getId()).collect(Collectors.toList());
        List<ExpoForm> expoFormList = expoFormManager.getByExpoIds(expoIds);
        Map<Integer, List<ExpoForm>> expoFormMap = null;
        if(CollectionUtils.isNotEmpty(expoFormList)){
            expoFormMap = expoFormList.stream().collect(Collectors.groupingBy(ExpoForm::getExpoId));
        }
        for(ExpoInfo expoInfo: list){
            List<ExpoFormVO> expoFormVOList = null;
            if(null != expoFormMap && expoFormMap.containsKey(expoInfo.getId())){
                List<ExpoForm> expoForms = expoFormMap.get(expoInfo.getId());
                expoFormVOList = CopyObjectUtils.copyAlistToBlist(expoForms, ExpoFormVO.class);
            }
            ExpoInfoCopyFormVO vo = new ExpoInfoCopyFormVO();
            vo.setExpoName(expoInfo.getExpoName());
            vo.setFormList(expoFormVOList);
            voList.add(vo);
        }
        return voList;
    }

    @Override
    public List<ExpoInfoVO> list(ExpoInfoDTO dto) {
        List<ExpoInfo> expoInfos = expoInfoManager.list(new LambdaQueryWrapper<ExpoInfo>()
                .eq(Objects.nonNull(dto.getExpoStatus()), ExpoInfo::getExpoStatus, dto.getExpoStatus())
                .eq(ExpoInfo::getCompanyId, dto.getCompanyId())
        );
        List<ExpoInfoVO> vos = CopyObjectUtils.copyAlistToBlist(expoInfos, ExpoInfoVO.class);
        vos.stream().forEach(r -> {
            Integer expoStatus = r.getExpoStatus();
            ExpoStatusEnums statusEnums = ExpoStatusEnums.getByCode(expoStatus);
            if (Objects.nonNull(statusEnums)) {
                r.setExpoStatusStr(statusEnums.getMessage());
            }
        });
        return vos;
    }

    @Override
    public Result<ExpoInfoVO> info(ExpoInfoDTO dto) {
        ExpoInfo expoInfo = expoInfoManager.getOne(new LambdaQueryWrapper<ExpoInfo>()
                .eq(BaseEntity::getId, dto.getId())
                .eq(ExpoInfo::getCompanyId, dto.getCompanyId())
        );
        ExpoInfoVO expoInfoVO = CopyObjectUtils.copyAtoB(expoInfo, ExpoInfoVO.class);
        // 附件
        List<ExpoAttachmentFileVO> handbookAttachmentFiles = expoAttachmentFileManager.getExpoAttachmentFileList(dto.getId(), dto.getId(), ExpoAttachmentFileEnum.Type.EXPO_HANDBOOK);
        List<ExpoAttachmentFileVO> templateAttachmentFiles = expoAttachmentFileManager.getExpoAttachmentFileList(dto.getId(), dto.getId(), ExpoAttachmentFileEnum.Type.EXPO_POSTER_TEMPLATE);
        expoInfoVO.setHandbookAttachmentFiles(handbookAttachmentFiles);
        expoInfoVO.setTemplateAttachmentFiles(templateAttachmentFiles);

        return Result.build(expoInfoVO);
    }

    @Override
    public Result delete(ExpoInfoDTO dto) {
        expoInfoManager.remove(new LambdaQueryWrapper<ExpoInfo>()
                .eq(BaseEntity::getId, dto.getId())
                .eq(ExpoInfo::getCompanyId, dto.getCompanyId())
                .eq(ExpoInfo::getExpoStatus, ExpoStatusEnums.UN_STARTED.getCode())
        );
        return Result.build();
    }

    @Override
    public Result channelConfigSetting(ExpoChannelConfigDTO dto) {
        Integer companyId = dto.getCompanyId();
        checkAndChannel(dto.getChannelId(), companyId);
        ExpoForm expoForm = checkAndForm(dto.getFormId(), companyId);
        if(ExpoFormEnums.FormType.AUDIENCE_REGISTER.getCode() != expoForm.getFormType()){
            //渠道只能绑定观众注册表单
            throw new BusinessException(ExpoResultCode.ExpoResultEnum.EXPO_CHANNEL_RELATION_FORM_TYPE_ERROR.getCode(),
                    ExpoResultCode.ExpoResultEnum.EXPO_CHANNEL_RELATION_FORM_TYPE_ERROR.getMessage());
        }
        ExpoChannelConfig channelConfig = expoChannelConfigManager.getOne(new LambdaQueryWrapper<ExpoChannelConfig>()
                .eq(ExpoChannelConfig::getCompanyId, companyId)
                .eq(ExpoChannelConfig::getExpoId, dto.getExpoId())
                .eq(ExpoChannelConfig::getChannelId, dto.getChannelId())
        );
        if (Objects.nonNull(channelConfig)) {
            if ((Objects.nonNull(dto.getId()) && !dto.getId().equals(channelConfig.getId())) || Objects.isNull(dto.getId())) {
                throw new BusinessException(ExpoResultCode.ExpoResultEnum.EXPO_CHANNEL_CONFIG_IS_EXIST.getCode(),
                        ExpoResultCode.ExpoResultEnum.EXPO_CHANNEL_CONFIG_IS_EXIST.getMessage());
            }
        }
        expoChannelConfigManager.saveOrUpdate(dto);
        return Result.build();
    }

    @Override
    public Result<List<ExpoChannelConfigVO>> pageForChannel(ExpoChannelConfigPageDTO dto) {
        Page<ExpoChannelConfig> page = new Page<>(dto.getPageNo(), dto.getPageSize());
        Page<ExpoChannelConfig> configPage = expoChannelConfigManager.page(page, new LambdaQueryWrapper<ExpoChannelConfig>()
                .eq(ExpoChannelConfig::getExpoId, dto.getExpoId())
                .eq(ExpoChannelConfig::getCompanyId, dto.getCompanyId())
                .orderByDesc(BaseEntity::getCreateTime)
        );
        List<ExpoChannelConfigVO> vos = autoList(dto.getUserId(), configPage.getRecords());
        return Result.build(vos, configPage.getTotal());
    }

    @Override
    public Result<List<ExpoChannelConfigVO>> channelConfigList(ExpoChannelConfigDTO dto) {
        List<ExpoChannelConfig> channelConfigs = expoChannelConfigManager.list(new LambdaQueryWrapper<ExpoChannelConfig>()
                .eq(ExpoChannelConfig::getCompanyId, dto.getCompanyId())
                .eq(ExpoChannelConfig::getExpoId, dto.getExpoId())
        );
        List<ExpoChannelConfigVO> vos = autoList(dto.getUserId(), channelConfigs);
        return Result.build(vos);
    }

    @Override
    public Result<ExpoChannelConfigVO> channelConfig(ExpoChannelConfigDTO dto) {
        ExpoChannelConfig channelConfig = expoChannelConfigManager.getOne(new LambdaQueryWrapper<ExpoChannelConfig>()
                .eq(ExpoChannelConfig::getCompanyId, dto.getCompanyId())
                .eq(BaseEntity::getId, dto.getId())
        );
        ExpoChannelConfigVO vo = CopyObjectUtils.copyAtoB(channelConfig, ExpoChannelConfigVO.class);
        if (Objects.isNull(channelConfig)) {
            return Result.build(vo);
        }
        ExpoChannel channel = expoChannelManager.getById(channelConfig.getChannelId());
        ExpoForm form = expoFormManager.getById(channelConfig.getFormId());
        if (Objects.nonNull(channel)) {
            vo.setChannelName(channel.getChannelName());
        }
        if (Objects.nonNull(form)) {
            vo.setFormName(form.getFormName());
        }
        return Result.build(vo);
    }

    @Override
    public Result deleteChannelConfig(ExpoChannelConfigDTO dto) {
        expoChannelConfigManager.getOne(new LambdaQueryWrapper<ExpoChannelConfig>()
                .eq(ExpoChannelConfig::getCompanyId, dto.getCompanyId())
                .eq(BaseEntity::getId, dto.getId())
        );
        return Result.build();
    }

    @Override
    public void batchHandleStatus() {
        LocalDateTime now = LocalDateTime.now();
        //查询未开始的展会
        List<ExpoInfo> infos = expoInfoManager.list(new LambdaQueryWrapper<ExpoInfo>()
                .eq(ExpoInfo::getExpoStatus, ExpoStatusEnums.UN_STARTED.getCode())
                .le(ExpoInfo::getZoneIdStartTime, now)
                .ge(ExpoInfo::getZoneIdEndTime, now)
        );
        infos.stream().forEach(r -> r.setExpoStatus(ExpoStatusEnums.STARTED.getCode()));
        if (CollectionUtils.isNotEmpty(infos)) {
            //批量进行中
            expoInfoManager.saveOrUpdateBatch(infos);
        }
        infos = expoInfoManager.list(new LambdaQueryWrapper<ExpoInfo>()
                .ne(ExpoInfo::getExpoStatus, ExpoStatusEnums.ENDED.getCode())
                .lt(ExpoInfo::getEndTime, now)
        );
        infos.stream().forEach(r -> r.setExpoStatus(ExpoStatusEnums.ENDED.getCode()));
        if (CollectionUtils.isNotEmpty(infos)) {
            //批量结束
            expoInfoManager.saveOrUpdateBatch(infos);
        }
    }

    @Override
    public List<ExpoInfoVO> webExpoPage(ExpoInfoDTO dto) {
        //TODO 这里需要确定一下展示的是全部展会还是正在进行中的展会
        List<ExpoInfo> expoInfoList = expoInfoManager.
                queryWebExpoInfo(dto.getCompanyId());
        if(CollectionUtils.isEmpty(expoInfoList)){
            return new ArrayList<>();
        }
        return CopyObjectUtils.copyAlistToBlist(expoInfoList, ExpoInfoVO.class);
    }

    /**
     * 首页立即沟通
     *
     * @param dto 参数
     * @return ExpoIndexCommunicateVO
     */
    @Override
    public ExpoIndexCommunicateVO indexCommunicate(ExpoCommunicateGroupDTO dto) {
        ExpoIndexCommunicateVO vo = new ExpoIndexCommunicateVO();
        // 查询租户信息
        TenantInfoResp tenantInfo = feignCommonManager.getTenantInfo(dto.getTenantId());
        if (Objects.isNull(tenantInfo)) {
            log.info("租户不存在===");
            return vo;
        }
        dto.setCompanyId(tenantInfo.getCompanyId());
        // 查询是否存在群聊
        ExpoCommunicateGroup communicateGroup = expoCommunicateGroupManager.queryOne(dto);
        if (Objects.nonNull(communicateGroup)) {
            vo.setGroupId(communicateGroup.getGroupId());
            vo.setSessionId(communicateGroup.getSessionId());
            return vo;
        }
        // 查询公司客服
        List<Integer> companyIds = new ArrayList<>();
        companyIds.add(dto.getCompanyId());
        if (dto.getBusinessType() == 1) {
            companyIds.add(dto.getBusinessId());
        }
        List<CscCustomerServiceResp> serviceResps = feignCommonManager.queryCompanyService(companyIds);
        if (CollectionUtils.isEmpty(serviceResps)) {
            log.info("没有客服===");
            return vo;
        }
        List<Integer> groupUserIds = serviceResps.stream().map(CscCustomerServiceResp::getUserId).distinct().collect(Collectors.toList());
        // 创建群聊
        groupUserIds.add(dto.getCreateUser());
        if (dto.getBusinessType() == 2) {
            groupUserIds.add(dto.getBusinessId());
        }
        ImcSessionResp group = feignCommonManager.createGroup(groupUserIds, dto.getCreateUser());
        if (Objects.nonNull(group)) {
            vo.setSessionId(group.getId());
            vo.setGroupId(group.getGroupId());
            // 保存建群记录
            ExpoCommunicateGroup save = CopyObjectUtils.copyAtoB(dto, ExpoCommunicateGroup.class);
            save.setGroupId(group.getGroupId());
            save.setSessionId(group.getId());
            expoCommunicateGroupManager.save(save);
        }
        return vo;
    }

    /**
     * 查询展会
     *
     * @param id
     * @param companyId
     * @return
     */
    public ExpoInfo checkAndExpo(Integer id, Integer companyId) {
        ExpoInfo expoInfo = expoInfoManager.getOne(new LambdaQueryWrapper<ExpoInfo>()
                .eq(BaseEntity::getId, id)
                .eq(ExpoInfo::getCompanyId, companyId)
        );
        if (Objects.isNull(expoInfo)) {
            throw new BusinessException(ExpoResultCode.ExpoResultEnum.EXPO_IS_NOT_EXIST.getCode(),
                    ExpoResultCode.ExpoResultEnum.EXPO_IS_NOT_EXIST.getMessage());
        }
        return expoInfo;
    }


    /**
     * 查询渠道
     *
     * @param id
     * @param companyId
     * @return
     */
    public ExpoChannel checkAndChannel(Integer id, Integer companyId) {
        ExpoChannel channel = expoChannelManager.getOne(new LambdaQueryWrapper<ExpoChannel>()
                .eq(ExpoChannel::getCompanyId, companyId)
                .eq(BaseEntity::getId, id)
        );
        if (Objects.isNull(channel)) {
            throw new BusinessException(ExpoResultCode.ExpoResultEnum.EXPO_CHANNEL_IS_NOT_EXIST.getCode(),
                    ExpoResultCode.ExpoResultEnum.EXPO_CHANNEL_IS_NOT_EXIST.getMessage());
        }
        return channel;
    }

    public ExpoForm checkAndForm(Integer id, Integer companyId) {
        ExpoForm form = expoFormManager.getOne(new LambdaQueryWrapper<ExpoForm>()
                .eq(ExpoForm::getCompanyId, companyId)
                .eq(BaseEntity::getId, id)
        );
        if (Objects.isNull(form)) {
            throw new BusinessException(ExpoResultCode.ExpoResultEnum.EXPO_FORM_IS_NOT_EXIST.getCode(),
                    ExpoResultCode.ExpoResultEnum.EXPO_FORM_IS_NOT_EXIST.getMessage());
        }
        return form;
    }

    private List<ExpoChannelConfigVO> autoList(Integer userId, List<ExpoChannelConfig> channelConfigs) {
        Set<Integer> channelIdList = channelConfigs.stream().map(ExpoChannelConfig::getChannelId).collect(Collectors.toSet());
        Map<Integer, ExpoChannel> channelMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(channelIdList)) {
            List<ExpoChannel> channels = expoChannelManager.listByIds(channelIdList);
            channelMap.putAll(channels.stream().collect(Collectors.toMap(BaseEntity::getId, r -> r)));
        }
        Map<Integer, ExpoForm> formMap = new HashMap<>();
        Set<Integer> formIds = channelConfigs.stream().map(ExpoChannelConfig::getFormId).collect(Collectors.toSet());
        Set<String> tenantIds = new HashSet<>();
        if (CollectionUtils.isNotEmpty(formIds)) {
            List<ExpoForm> forms = expoFormManager.listByIds(formIds);
            formMap.putAll(forms.stream().collect(Collectors.toMap(BaseEntity::getId, r -> r)));
            tenantIds.addAll(forms.stream().map(ExpoForm::getPublishTenantId).collect(Collectors.toList()));
        }
        List<ExpoChannelConfigVO> vos = CopyObjectUtils.copyAlistToBlist(channelConfigs, ExpoChannelConfigVO.class);
        Map<String, TenantInfoResp> tenantInfoRespMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(tenantIds)) {
            BatchTenantDetailReq req = new BatchTenantDetailReq();
            req.setTenantIdSet(tenantIds);
            Result<List<TenantInfoResp>> result = tenantInfoFeignClient.batchQueryTenantDetail(req);
            tenantInfoRespMap.putAll(result.getData().stream().collect(Collectors.toMap(TenantInfoResp::getTenantId, r -> r)));
        }
        vos.stream().forEach(r -> {
            ExpoChannel channel = channelMap.get(r.getChannelId());
            if (Objects.nonNull(channel)) {
                r.setChannelName(channel.getChannelName());
            }
            ExpoForm expoForm = formMap.get(r.getFormId());
            if (Objects.nonNull(expoForm)) {
                r.setFormName(expoForm.getFormName());
                TenantInfoResp tenantInfoResp = tenantInfoRespMap.get(expoForm.getPublishTenantId());
                if (Objects.nonNull(tenantInfoResp)) {
                    r.setScanUrl(String.format(gateway + GatewayRoutingConstants.routingScanRegisterUrl(),
                            r.getId(), r.getExpoId(), r.getCompanyId(), userId));
                }
            }
        });
        return vos;
    }
}
