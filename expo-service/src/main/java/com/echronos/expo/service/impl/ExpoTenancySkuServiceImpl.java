package com.echronos.expo.service.impl;

import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.echronos.commons.Result;
import com.echronos.commons.exception.BusinessException;
import com.echronos.commons.utils.CopyObjectUtils;
import com.echronos.expo.constants.ExpoConstants;
import com.echronos.expo.constants.NumberConstant;
import com.echronos.expo.dto.ExpoTenancySkuDTO;
import com.echronos.expo.enums.ExpoResultCode;
import com.echronos.expo.manager.ExpoInfoManager;
import com.echronos.expo.manager.ExpoTenancySkuManager;
import com.echronos.expo.manager.FeignCommonManager;
import com.echronos.expo.model.ExpoInfo;
import com.echronos.expo.model.ExpoTenancySku;
import com.echronos.expo.service.IExpoTenancySkuService;
import com.echronos.expo.vo.ExpoPageVO;
import com.echronos.expo.vo.tenancysku.ExpoTenancySkuVO;
import com.echronos.pms.resp.SkuListResp;
import com.echronos.search.api.req.PmsProductReq;
import com.echronos.search.api.resp.PmsProductResp;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.RoundingMode;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * date2025/8/9 14:02
 */
@Slf4j
@Service
public class ExpoTenancySkuServiceImpl implements IExpoTenancySkuService {

    @Resource
    private ExpoTenancySkuManager expoTenancySkuManager;
    @Resource
    private ExpoInfoManager expoInfoManager;
    @Resource
    private FeignCommonManager feignCommonManager;

    @Override
    public ExpoPageVO<ExpoTenancySkuVO> page(ExpoTenancySkuDTO dto) {
        //查询展会是否存在
        validityExpoExist(dto.getExpoId());
        //查询展会商品信息
        List<ExpoTenancySku> tenancySkuList = expoTenancySkuManager.
                queryTenancySkuByExpoId(dto.getExpoId(), dto.getCompanyId());
        if (CollectionUtils.isEmpty(tenancySkuList)) {
            return new ExpoPageVO<>();
        }
        Set<Integer> shopSkuIdSet = tenancySkuList.stream().
                map(ExpoTenancySku::getShopSkuId).collect(Collectors.toSet());
        //组装商品查询数据
        PmsProductReq pmsProductReq = CopyObjectUtils.copyAtoB(dto, PmsProductReq.class);
        if (StringUtils.isNotBlank(dto.getKeyword())) {
            pmsProductReq.setName(dto.getKeyword());
        }
        pmsProductReq.setShopSkuIds(shopSkuIdSet);
        pmsProductReq.setSort(ExpoConstants.SORT_DESC);
        pmsProductReq.setCurrentCompanyId(dto.getCompanyId());
        pmsProductReq.setIsCustomizeMade(NumberConstant.ONE);
        Result<List<PmsProductResp>> result = feignCommonManager.searchProduct(pmsProductReq);
        //店铺商品ID信息存储
        Map<Integer, PmsProductResp> productRespMap = new HashMap<>();
        //sku商品信息存储, 可以获取到商品的数量/价格小数点位数
        Map<Integer, SkuListResp> skuListRespMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(result.getData())) {
            productRespMap = result.getData().stream().collect(Collectors.
                    toMap(e -> e.getId(), Function.identity(), (key1, key2) -> key2));
            List<Integer> skuIdList = result.getData().stream().map(PmsProductResp::getSkuId).distinct().collect(Collectors.toList());
            skuListRespMap = feignCommonManager.querySkuByIdsToMap(skuIdList);
        }

        List<ExpoTenancySkuVO> voList = new ArrayList<>();
        for (ExpoTenancySku item : tenancySkuList) {
            if (!productRespMap.isEmpty() && productRespMap.containsKey(item.getShopSkuId())) {
                PmsProductResp productResp = productRespMap.get(item.getShopSkuId());
                ExpoTenancySkuVO vo = CopyObjectUtils.copyAtoB(productResp, ExpoTenancySkuVO.class);
                Integer priceNum = NumberConstant.TWO;
                if (skuListRespMap.containsKey(item.getSkuId())) {
                    priceNum = skuListRespMap.get(item.getSkuId()).getPriceNum();
                }
                vo.setMarketPrice(vo.getMarketPrice().setScale(priceNum, RoundingMode.HALF_UP));
                vo.setId(item.getId());
                vo.setShopSkuId(item.getShopSkuId());
                vo.setSkuId(item.getShopSkuId());
                voList.add(vo);
            }
        }
        return new ExpoPageVO<>(result.getTotalCount(), result.getTotalCount(), voList);
    }

    @Override
    public void add(ExpoTenancySkuDTO dto) {
        //查询展会是否存在
        validityExpoExist(dto.getExpoId());
        ExpoTenancySku expoTenancySku = CopyObjectUtils.copyAtoB(dto, ExpoTenancySku.class);
        expoTenancySkuManager.save(expoTenancySku);
    }

    @Override
    public void del(ExpoTenancySkuDTO dto) {
        validityExpoTenancySkuExist(dto.getId(), dto.getCompanyId());
        expoTenancySkuManager.updateDel(dto.getId(), dto.getUserId());
    }

    /**
     * 校验展会是否存在
     *
     * @param expoId
     */
    private void validityExpoExist(Integer expoId) {
        ExpoInfo expoInfo = expoInfoManager.getById(expoId);
        if (Objects.isNull(expoInfo)) {
            throw new BusinessException(ExpoResultCode.ExpoResultEnum.EXPO_IS_NOT_EXIST.getCode(),
                    ExpoResultCode.ExpoResultEnum.EXPO_IS_NOT_EXIST.getMessage());
        }
    }

    /**
     * 校验租赁商品是否存在
     *
     * @param id
     * @param companyId
     */
    private void validityExpoTenancySkuExist(Integer id, Integer companyId) {
        ExpoTenancySku tenancySku = expoTenancySkuManager.queryExpoTenancySkuById(id, companyId);
        if (Objects.isNull(tenancySku)) {
            throw new BusinessException(ExpoResultCode.ExpoResultEnum.EXPO_TENANCY_PRODUCT_IS_NOT_EXIST.getCode(),
                    ExpoResultCode.ExpoResultEnum.EXPO_TENANCY_PRODUCT_IS_NOT_EXIST.getMessage());
        }
    }
}
