package com.echronos.expo.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.echronos.commons.utils.CopyObjectUtils;
import com.echronos.expo.dto.ExpoAttachmentFileDTO;
import com.echronos.expo.enums.ExpoAttachmentFileEnum;
import com.echronos.expo.manager.ExpoAttachmentFileManager;
import com.echronos.expo.model.ExpoAttachmentFile;
import com.echronos.expo.service.IExpoAttachmentFileService;
import com.echronos.expo.vo.ExpoAttachmentFileVO;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025-08-04 14:11
 */
@Service
public class ExpoAttachmentFileServiceImpl implements IExpoAttachmentFileService {

    @Resource
    private ExpoAttachmentFileManager attachmentFileManager;

    @Override
    public void addOrUpdateFile(Integer expoId, Integer businessId, List<ExpoAttachmentFileDTO> dtoList, ExpoAttachmentFileEnum.Type type) {
        attachmentFileManager.addOrUpdateAttachmentFile(expoId, businessId, dtoList, type);
    }

}
