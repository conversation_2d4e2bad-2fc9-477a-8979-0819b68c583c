package com.echronos.expo.service;

import com.echronos.expo.dto.ExpoReferenceFormDTO;
import com.echronos.expo.vo.ExpoReferenceFormVO;

/**
 * <AUTHOR>
 * @date 2025-08-09 11:29
 */
public interface IExpoReferenceFormService {

    /**
     * 根据ID获取表单详情
     */
    ExpoReferenceFormVO getDetailById(ExpoReferenceFormDTO dto);

    /**
     * 新增或编辑表单
     * @param dto
     */
    void addOrEdit(ExpoReferenceFormDTO dto);

    /**
     * 审核表单
     * @param dto
     */
    void audit(ExpoReferenceFormDTO dto);

}
