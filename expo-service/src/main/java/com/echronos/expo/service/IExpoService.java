package com.echronos.expo.service;

import com.echronos.commons.Result;
import com.echronos.expo.dto.*;
import com.echronos.expo.vo.*;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025/5/13 16:11
 * @ClassName IExpoService
 */
public interface IExpoService {

    /**
     * 创建/编辑展会
     *
     * @param dto
     */
    Result edit(ExpoInfoDTO dto);


    /**
     * 分页查询展会
     *
     * @param dto
     * @return
     */
    Result<List<ExpoInfoVO>> pageFor(ExpoInfoPageDTO dto);

    /**
     * 展会列表分页
     *
     * @param dto
     * @return
     */
    Result<List<ExpoInfoVO>> pageList(ExpoInfoPageDTO dto);

    /**
     * 获取展会及复制表单列表
     *
     * @param dto
     * @return
     */
    List<ExpoInfoCopyFormVO> getListCopyFormList(ExpoInfoDTO dto);


    /**
     * 展会列表
     *
     * @param dto
     * @return
     */
    List<ExpoInfoVO> list(ExpoInfoDTO dto);

    /**
     * 展会详情
     *
     * @param dto
     * @return
     */
    Result<ExpoInfoVO> info(ExpoInfoDTO dto);

    /**
     * 删除展会详情
     *
     * @param dto
     * @return
     */
    Result delete(ExpoInfoDTO dto);

    /**
     * 配置展会观众渠道
     *
     * @param dto
     * @return
     */
    Result channelConfigSetting(ExpoChannelConfigDTO dto);


    /**
     * 分页查询渠道配置
     *
     * @param dto
     * @return
     */
    Result<List<ExpoChannelConfigVO>> pageForChannel(ExpoChannelConfigPageDTO dto);

    /**
     * 渠道列表
     *
     * @param dto
     * @return
     */
    Result<List<ExpoChannelConfigVO>> channelConfigList(ExpoChannelConfigDTO dto);

    /**
     * 渠道配置详情
     *
     * @param dto
     * @return
     */
    Result<ExpoChannelConfigVO> channelConfig(ExpoChannelConfigDTO dto);

    /**
     * 删除渠道配置
     *
     * @param dto
     * @return
     */
    Result deleteChannelConfig(ExpoChannelConfigDTO dto);

    /**
     * 展会状态变更
     * 批处理
     */
    void batchHandleStatus();

    /**
     * web建站查询展会列表
     * @param dto
     */
    List<ExpoInfoVO> webExpoPage(ExpoInfoDTO dto);

    /**
     * 首页立即沟通
     *
     * @param dto 参数
     * @return ExpoIndexCommunicateVO
     */
    ExpoIndexCommunicateVO indexCommunicate(ExpoCommunicateGroupDTO dto);
}
