package com.echronos.expo.service;

import com.echronos.expo.dto.ExpoAttachmentFileDTO;
import com.echronos.expo.enums.ExpoAttachmentFileEnum;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025-08-04 14:10
 */
public interface IExpoAttachmentFileService {

    /**
     * 添加或更新附件
     * @param expoId 展会ID
     * @param type 类型
     * @param businessId 业务ID
     * @param dtoList 参数DTO
     */
    void addOrUpdateFile(Integer expoId, Integer businessId, List<ExpoAttachmentFileDTO> dtoList, ExpoAttachmentFileEnum.Type type);

}
