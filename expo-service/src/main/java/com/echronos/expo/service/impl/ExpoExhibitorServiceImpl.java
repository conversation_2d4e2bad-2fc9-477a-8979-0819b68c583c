package com.echronos.expo.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.extra.qrcode.QrCodeUtil;
import cn.hutool.extra.qrcode.QrConfig;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.echronos.commons.enums.CommonResultCode;
import com.echronos.commons.enums.CommonStatus;
import com.echronos.commons.exception.BusinessException;
import com.echronos.commons.utils.CopyObjectUtils;
import com.echronos.crm.enums.CustomerCategoryEnum;
import com.echronos.crm.req.InsertCustomerReq;
import com.echronos.crm.resp.CategoryResp;
import com.echronos.crm.resp.CustomerResp;
import com.echronos.expo.constants.GatewayRoutingConstants;
import com.echronos.expo.constants.NumberConstant;
import com.echronos.expo.dto.*;
import com.echronos.expo.enums.*;
import com.echronos.expo.manager.*;
import com.echronos.expo.model.*;
import com.echronos.expo.model.ext.ExpoExhibitorExt;
import com.echronos.expo.service.IExpoExhibitorService;
import com.echronos.expo.vo.*;
import com.echronos.expo.vo.exhibitor.*;
import com.echronos.mcs.api.resp.ShopSkuBasisResp;
import com.echronos.order.req.AttachmentsOssAddrReq;
import com.echronos.order.req.GenOrderParentReq;
import com.echronos.order.req.GenOrderProductReq;
import com.echronos.order.req.GenOrderReq;
import com.echronos.order.resp.GenOrderResp;
import com.echronos.order.resp.OrderPaymentInfoResp;
import com.echronos.order.resp.PayWayResp;
import com.echronos.system.resp.MemberResp;
import com.echronos.system.resp.member.MemberPowerResp;
import com.echronos.tenant.api.resp.TenantInfoResp;
import com.echronos.user.api.req.QueryUserReq;
import com.echronos.user.api.resp.UserInfoResp;
import com.echronos.user.api.resp.company.QueryCompanyResp;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 展商服务实现类
 *
 * <AUTHOR>
 * date2025/8/4 15:09
 */
@Service
@Slf4j
public class ExpoExhibitorServiceImpl implements IExpoExhibitorService {

    @Resource
    private ExpoExhibitorManager expoExhibitorManager;
    @Resource
    private ExpoExhibitorBoothManager expoExhibitorBoothManager;
    @Resource
    private ExpoFormManager expoFormManager;
    @Resource
    private FeignCommonManager feignCommonManager;
    @Resource
    private ExpoBoothOrderManager expoBoothOrderManager;
    @Resource
    private ExpoBoothManager expoBoothManager;
    @Resource
    private ExpoAudienceManager expoAudienceManager;
    @Resource
    private ExpoBoothOrderDetailManager expoBoothOrderDetailManager;
    @Resource
    private ExpoInfoManager expoInfoManager;
    @Resource
    private ExpoReferenceFormManager expoReferenceFormManager;
    @Resource
    private ExpoAttachmentFileManager expoAttachmentFileManager;
    @Resource
    private ExpoLeaseOrderManager expoLeaseOrderManager;
    @Resource
    private ExpoLeaseOrderDetailManager expoLeaseOrderDetailManager;
    @Resource
    private ExpoTravelOrderManager expoTravelOrderManager;
    @Resource
    private ExpoTravelOrderDetailManager expoTravelOrderDetailManager;
    @Resource
    private ExpoExhibitorScanRecordManager expoExhibitorScanRecordManager;

    @Override
    public IPage<ExpoExhibitorVO> pageList(ExpoExhibitorDTO dto) {
        Page<ExpoBoothDTO> page = new Page<>(dto.getPageNo(), dto.getPageSize());
        //查询行转列字段
        FormSqlFieldDTO formSqlFieldDTO = expoFormManager.getFromSqlFieldAndSort("", null, dto.getFilters(), dto.getSort());
        //行转列字段
        dto.setFieldList(formSqlFieldDTO.getRowToColumnSql());
        //筛选条件
        dto.setWhereSqlStr(formSqlFieldDTO.getConditionScreen());
        //排序条件
        dto.setSortStr(formSqlFieldDTO.getBuildSortNew());
        List<ExpoExhibitorDTO> recordList = expoExhibitorManager.pageList(page, dto);
        List<ExpoExhibitorVO> voList = new ArrayList<>();
        if(CollectionUtil.isNotEmpty(recordList)){
            // 展商ID
            List<Integer> exhibitorIdList = recordList.stream().map(BaseNotTenantEntity::getId).collect(Collectors.toList());
            // 展商对应展位
            Map<Integer, List<ExpoExhibitorBoothDTO>> exhibitorBoothMap = expoExhibitorBoothManager.getDetailByExhibitorIdList(exhibitorIdList);
            // 展商对应订单
            List<ExpoBoothOrder> boothOrderList = expoBoothOrderManager.getByExpoExhibitorIds(exhibitorIdList);
            List<ExpoLeaseOrder> leaseOrderList = expoLeaseOrderManager.getListByExhibitorIds(exhibitorIdList);
            // 展商展位费用
            Map<Integer, BigDecimal> boothAmountMap = new HashMap<>();
            // 展商租赁费
            Map<Integer, BigDecimal> leaseAmountMap = new HashMap<>();
            // 展商未支付金额
            Map<Integer, BigDecimal> unpaidAmountMap = new HashMap<>();
            // 展商已支付金额
            Map<Integer, BigDecimal> paidAmountMap = new HashMap<>();
            for(ExpoExhibitorDTO record : recordList){
                boothAmountMap.put(record.getId(), BigDecimal.ZERO);
                leaseAmountMap.put(record.getId(), BigDecimal.ZERO);
                unpaidAmountMap.put(record.getId(), BigDecimal.ZERO);
                paidAmountMap.put(record.getId(), BigDecimal.ZERO);
            }

            // 查询订单费用
            if(CollectionUtil.isNotEmpty(boothOrderList)){
                Map<Integer, List<ExpoBoothOrder>> exhibitorBoothOrderMap = boothOrderList.stream().collect(Collectors.groupingBy(ExpoBoothOrder::getExhibitorId));
                // 所有订单编号
                List<Long> orderNoList = boothOrderList.stream().map(ExpoBoothOrder::getOrderNo).collect(Collectors.toList());
                // 批量查询订单金额信息
                Map<Long, OrderPaymentInfoResp> orderPaymentInfoMap = feignCommonManager.batchQueryOrderPaymentInfo(orderNoList);
                for(Map.Entry<Integer, List<ExpoBoothOrder>> entry : exhibitorBoothOrderMap.entrySet()){
                    Integer exhibitorId = entry.getKey();
                    BigDecimal boothAmount = boothAmountMap.get(exhibitorId);
                    BigDecimal unpaidAmount = unpaidAmountMap.get(exhibitorId);
                    BigDecimal paidAmount = paidAmountMap.get(exhibitorId);
                    for(ExpoBoothOrder expoBoothOrder : entry.getValue()){
                        OrderPaymentInfoResp orderPaymentInfoResp = orderPaymentInfoMap.get(expoBoothOrder.getOrderNo());
                        boothAmount = boothAmount.add(orderPaymentInfoResp.getTotalAmount());
                        unpaidAmount = unpaidAmount.add(orderPaymentInfoResp.getUnpaidAmount());
                        paidAmount = paidAmount.add(orderPaymentInfoResp.getPaidAmount());
                    }
                    boothAmountMap.put(exhibitorId, boothAmount);
                    unpaidAmountMap.put(exhibitorId, unpaidAmount);
                    paidAmountMap.put(exhibitorId, paidAmount);
                }
            }

            // 查询租赁订单费用
            if(CollectionUtil.isNotEmpty(leaseOrderList)){
                Map<Integer, List<ExpoLeaseOrder>> exhibitorBoothOrderMap = leaseOrderList.stream().collect(Collectors.groupingBy(ExpoLeaseOrder::getExhibitorId));
                // 所有订单编号
                List<Long> orderNoList = leaseOrderList.stream().map(ExpoLeaseOrder::getOrderNo).collect(Collectors.toList());
                // 批量查询订单金额信息
                Map<Long, OrderPaymentInfoResp> orderPaymentInfoMap = feignCommonManager.batchQueryOrderPaymentInfo(orderNoList);
                for(Map.Entry<Integer, List<ExpoLeaseOrder>> entry : exhibitorBoothOrderMap.entrySet()){
                    Integer exhibitorId = entry.getKey();
                    BigDecimal boothAmount = boothAmountMap.get(exhibitorId);
                    BigDecimal unpaidAmount = unpaidAmountMap.get(exhibitorId);
                    BigDecimal paidAmount = paidAmountMap.get(exhibitorId);
                    for(ExpoLeaseOrder expoLeaseOrder : entry.getValue()){
                        OrderPaymentInfoResp orderPaymentInfoResp = orderPaymentInfoMap.get(expoLeaseOrder.getOrderNo());
                        boothAmount = boothAmount.add(orderPaymentInfoResp.getTotalAmount());
                        unpaidAmount = unpaidAmount.add(orderPaymentInfoResp.getUnpaidAmount());
                        paidAmount = paidAmount.add(orderPaymentInfoResp.getPaidAmount());
                    }
                    boothAmountMap.put(exhibitorId, boothAmount);
                    unpaidAmountMap.put(exhibitorId, unpaidAmount);
                    paidAmountMap.put(exhibitorId, paidAmount);
                }
            }

            for(ExpoExhibitorDTO record : recordList){
                ExpoExhibitorVO vo = CopyObjectUtils.copyAtoB(record, ExpoExhibitorVO.class);
                buildVOEnum(vo);
                if(exhibitorBoothMap.containsKey(vo.getId())){
                    List<ExpoExhibitorBoothDTO> boothList = exhibitorBoothMap.get(vo.getId());
                    List<String> boothZones = new ArrayList<>();
                    List<String> boothNumbers = new ArrayList<>();
                    List<String> boothTypeNames = new ArrayList<>();
                    List<String> dimensions = new ArrayList<>();
                    for(ExpoExhibitorBoothDTO exhibitorBooth : boothList){
                        boothZones.add(exhibitorBooth.getBoothZone());
                        boothNumbers.add(exhibitorBooth.getBoothNumber());
                        boothTypeNames.add(ExpoBoothEnums.BoothType.getMsgByCode(exhibitorBooth.getBoothType()).getMsg());
                        dimensions.add(exhibitorBooth.getDimensions());
                    }
                    vo.setBoothZones(String.join(",", boothZones));
                    vo.setBoothNumbers(String.join(",", boothNumbers));
                    vo.setBoothTypeNames(String.join(",", boothTypeNames));
                    vo.setDimensions(String.join(",", dimensions));
                }
                BigDecimal boothAmount = boothAmountMap.get(record.getId());
                BigDecimal unpaidAmount = unpaidAmountMap.get(record.getId());
                BigDecimal paidAmount = paidAmountMap.get(record.getId());
                BigDecimal leaseAmount = leaseAmountMap.get(record.getId());
                vo.setPaidAmount(paidAmount);
                vo.setBoothTotalAmount(boothAmount);
                vo.setUnpaidAmount(unpaidAmount);
                vo.setLeaseTotalAmount(leaseAmount);
                voList.add(vo);
            }
        }
        IPage iPage = new Page();
        iPage.setTotal(page.getTotal());
        iPage.setRecords(voList);
        return iPage;
    }

    @Override
    public ExpoExhibitorEditDetailVO getEditDetail(ExpoExhibitorDTO dto) {
        ExpoExhibitor expoExhibitor = checkExpoExhibitor(dto.getId());
        if(null == expoExhibitor){
           throw new BusinessException(-1, "展商不存在");
        }
        Map<Integer, CustomerResp> customerMap = feignCommonManager.getBatchCustomerByIds(Lists.newArrayList(expoExhibitor.getId()));
        MemberResp memberResp = feignCommonManager.getByMemberId(expoExhibitor.getBusinessMemberId());
        // 展商信息
        ExpoExhibitorEditDetailVO vo = CopyObjectUtils.copyAtoB(expoExhibitor, ExpoExhibitorEditDetailVO.class);
        buildVOEnum(vo);
        vo.setCustomerName(customerMap.get(expoExhibitor.getId()).getCustomerName());
        vo.setBusinessMemberName(null != memberResp ? memberResp.getName() : null);

        // 展位费用
        BigDecimal boothTotalAmount = BigDecimal.ZERO;
        // 租赁费用
        BigDecimal leaseTotalAmount = BigDecimal.ZERO;
        // 订单总金额
        BigDecimal paidAmount = BigDecimal.ZERO;
        // 未支付金额
        BigDecimal unpaidAmount = BigDecimal.ZERO;
        // 展位订单
        List<ExpoBoothOrder> exhibitorOrderList = expoBoothOrderManager.getByExpoExhibitorId(expoExhibitor.getId());
        if(CollectionUtil.isNotEmpty(exhibitorOrderList)){
            // 所有订单编号
            List<Long> orderNoList = exhibitorOrderList.stream().map(ExpoBoothOrder::getOrderNo).collect(Collectors.toList());
            // 批量查询订单金额信息
            Map<Long, OrderPaymentInfoResp> orderPaymentInfoMap = feignCommonManager.batchQueryOrderPaymentInfo(orderNoList);
            if(CollectionUtil.isNotEmpty(orderPaymentInfoMap)){
                for(OrderPaymentInfoResp orderPaymentInfo : orderPaymentInfoMap.values()){
                    boothTotalAmount = boothTotalAmount.add(orderPaymentInfo.getTotalAmount());
                    paidAmount = paidAmount.add(orderPaymentInfo.getPaidAmount());
                    unpaidAmount = unpaidAmount.add(orderPaymentInfo.getUnpaidAmount());
                }
            }
        }

        // 查询租赁订单费用
        List<ExpoLeaseOrder> leaseOrderList = expoLeaseOrderManager.getListByExhibitorId(expoExhibitor.getId());
        if(CollectionUtil.isNotEmpty(leaseOrderList)){
            // 所有订单编号
            List<Long> orderNoList = leaseOrderList.stream().map(ExpoLeaseOrder::getOrderNo).collect(Collectors.toList());
            // 批量查询订单金额信息
            Map<Long, OrderPaymentInfoResp> orderPaymentInfoMap = feignCommonManager.batchQueryOrderPaymentInfo(orderNoList);
            if(CollectionUtil.isNotEmpty(orderPaymentInfoMap)){
                for(OrderPaymentInfoResp orderPaymentInfo : orderPaymentInfoMap.values()){
                    leaseTotalAmount = leaseTotalAmount.add(orderPaymentInfo.getTotalAmount());
                    paidAmount = paidAmount.add(orderPaymentInfo.getPaidAmount());
                    unpaidAmount = unpaidAmount.add(orderPaymentInfo.getUnpaidAmount());
                }
            }
        }

        // 展商对应展位
        Map<Integer, List<ExpoExhibitorBoothDTO>> exhibitorBoothMap = expoExhibitorBoothManager.getDetailByExhibitorIdList(Lists.newArrayList(expoExhibitor.getId()));
        if(exhibitorBoothMap.containsKey(expoExhibitor.getId())){
            List<ExpoBoothDetailVO> boothVOList = new ArrayList<>();
            List<ExpoExhibitorBoothDTO> boothList = exhibitorBoothMap.get(expoExhibitor.getId());
            for(ExpoExhibitorBoothDTO boothDTO : boothList){
                ExpoBoothDetailVO boothVO = CopyObjectUtils.copyAtoB(boothDTO, ExpoBoothDetailVO.class);
                boothVO.setId(boothDTO.getBoothId());
                boothVO.setBoothTypeName(ExpoBoothEnums.BoothType.getMsgByCode(boothVO.getBoothType()).getMsg());
                boothVOList.add(boothVO);
            }
            vo.setBoothList(boothVOList);
        }
        // 赋值填充订单费用
        vo.setBoothTotalAmount(boothTotalAmount);
        vo.setLeaseTotalAmount(leaseTotalAmount);
        vo.setPaidAmount(paidAmount);
        vo.setUnpaidAmount(unpaidAmount);
        return vo;
    }

    /**
     * 填充枚举信息
     * @param vo
     */
    private void buildVOEnum(ExpoExhibitorVO vo){
        vo.setContractStatusName(ExpoExhibitorEnums.ContractStatus.getByCode(vo.getContractStatus()).getMsg());
        vo.setSendEmailStatusName(ExpoExhibitorEnums.ContractStatus.getByCode(vo.getSendEmailStatus()).getMsg());
        vo.setEnterpriseInfoStatusName(ExpoExhibitorEnums.ContractStatus.getByCode(vo.getEnterpriseInfoStatus()).getMsg());
        vo.setJournalInfoStatusName(ExpoExhibitorEnums.JournalInfoStatus.getByCode(vo.getJournalInfoStatus()).getMsg());
        vo.setLeaseDemandTypeName(ExpoExhibitorEnums.LeaseDemandType.getByCode(vo.getLeaseDemandType()).getMsg());
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void editDetail(ExpoExhibitorDTO dto) {
        ExpoExhibitor expoExhibitor = checkExpoExhibitor(dto.getId());
        ExpoInfo expoInfo = expoInfoManager.getById(expoExhibitor.getExpoId());
        if(!expoInfo.getCompanyId().equals(dto.getCompanyId())){
            // 非本公司数据不能编辑
            throw new BusinessException(CommonResultCode.CommonResultEnum.BAD_REQUEST.getCode(), CommonResultCode.CommonResultEnum.BAD_REQUEST.getMessage());
        }

        LocalDateTime updateTime = LocalDateTime.now();
        dto.setUpdateTime(updateTime);
        // 展商旧展位信息
        List<ExpoExhibitorBooth> exhibitorBoothList = expoExhibitorBoothManager.getListByExhibitorId(dto.getId());
        List<Integer> oldBoothIds = exhibitorBoothList.stream().map(ExpoExhibitorBooth::getBoothId).collect(Collectors.toList());
        List<ExpoBooth> boothList = expoBoothManager.getBoothByIds(dto.getBoothIds());
        if(dto.getBoothIds().size() != boothList.size()){
            throw new BusinessException(-1, "展位信息有误");
        }
        Map<Integer, ExpoBooth> boothMap = boothList.stream().collect(Collectors.toMap(ExpoBooth::getId, Function.identity(), (key1, key2) -> key2));
        if(dto.getBoothIds().size() != boothMap.keySet().size()){
            throw new BusinessException(-1, "展位数量有误");
        }

        // 需要添加的展商展位
        List<ExpoExhibitorBooth> addExhibitorBoothList = new ArrayList<>();
        for(Integer boothId : dto.getBoothIds()){
            if(CollectionUtil.isEmpty(oldBoothIds) || !oldBoothIds.contains(boothId)){
                if(boothMap.get(boothId).getStatus() == ExpoBoothEnums.Status.NOT_SOLD.getCode()){
                    throw new BusinessException(-1, "展位已售出");
                }
                ExpoExhibitorBooth addExhibitorBooth = new ExpoExhibitorBooth();
                addExhibitorBooth.setExhibitorId(expoExhibitor.getId());
                addExhibitorBooth.setBoothId(boothId);
                addExhibitorBoothList.add(addExhibitorBooth);
            }
        }

        // 修改展商信息
        expoExhibitorManager.updateInfo(dto);

        // 添加展商展位
        if(CollectionUtil.isNotEmpty(addExhibitorBoothList)){
            expoExhibitorBoothManager.saveBatch(addExhibitorBoothList);
        }

        // 需要回归空闲的展位
        oldBoothIds.removeAll(dto.getBoothIds());
        if(CollectionUtil.isNotEmpty(oldBoothIds)){
            // 将展位状态设置为空闲
            expoBoothManager.updateStatus(oldBoothIds, ExpoBoothEnums.Status.NOT_SOLD.getCode(), dto.getUpdateUser(), updateTime);
            // 移除展商展位
            expoExhibitorBoothManager.removeExhibitorBooth(dto.getId(), oldBoothIds, dto.getUserId(), updateTime);
        }
    }

    @Override
    public ExhibitorDetailPlatformVO getDetailPlatform(ExpoExhibitorDTO dto) {
        ExpoExhibitor expoExhibitor = checkExpoExhibitor(dto.getId());
        // 展商展位
        List<ExpoExhibitorBoothDTO> exhibitorBoothList = expoExhibitorBoothManager.getDetailByExhibitorId(expoExhibitor.getId());
        List<ExpoBoothDetailVO> boothVOList = null;
        if(CollectionUtil.isNotEmpty(exhibitorBoothList)){
            boothVOList = CopyObjectUtils.copyAlistToBlist(exhibitorBoothList, ExpoBoothDetailVO.class);
            for(ExpoBoothDetailVO boothVO : boothVOList){
                boothVO.setBoothTypeName(ExpoBoothEnums.BoothType.getMsgByCode(boothVO.getBoothType()).getMsg());
            }
        }

        // 展位费用
        BigDecimal boothTotalAmount = BigDecimal.ZERO;
        // 租赁费用
        BigDecimal leaseTotalAmount = BigDecimal.ZERO;
        // 已支付金额
        BigDecimal paidAmount = BigDecimal.ZERO;
        // 未支付金额
        BigDecimal unpaidAmount = BigDecimal.ZERO;
        // 总金额
        BigDecimal totalAmount = BigDecimal.ZERO;

        // 展商订单
        List<ExpoBoothOrder> boothOrderList = expoBoothOrderManager.getByExpoExhibitorId(expoExhibitor.getId());
        if(CollectionUtil.isNotEmpty(boothOrderList)){
            List<Long> orderNoList = boothOrderList.stream().map(ExpoBoothOrder::getOrderNo).collect(Collectors.toList());
            // 批量查询订单金额信息
            Map<Long, OrderPaymentInfoResp> orderPaymentInfoMap = feignCommonManager.batchQueryOrderPaymentInfo(orderNoList);
            if(CollectionUtil.isNotEmpty(orderPaymentInfoMap)){
                for(OrderPaymentInfoResp orderPaymentInfo : orderPaymentInfoMap.values()){
                    boothTotalAmount = boothTotalAmount.add(orderPaymentInfo.getTotalAmount());
                    totalAmount = totalAmount.add(orderPaymentInfo.getTotalAmount());
                    paidAmount = paidAmount.add(orderPaymentInfo.getPaidAmount());
                    unpaidAmount = unpaidAmount.add(orderPaymentInfo.getUnpaidAmount());
                }
            }
        }
        // todo 租赁订单
//        List<ExpoBoothOrder> boothOrderList = expoBoothOrderManager.getByExpoExhibitorId(expoExhibitor.getId());
//        if(CollectionUtil.isNotEmpty(boothOrderList)){
//            List<Long> orderNoList = boothOrderList.stream().map(ExpoBoothOrder::getOrderNo).collect(Collectors.toList());
//            // 批量查询订单金额信息
//            Map<Long, OrderPaymentInfoResp> orderPaymentInfoMap = feignCommonManager.batchQueryOrderPaymentInfo(orderNoList);
//            if(CollectionUtil.isNotEmpty(orderPaymentInfoMap)){
//                for(OrderPaymentInfoResp orderPaymentInfo : orderPaymentInfoMap.values()){
//                    leaseTotalAmount = leaseTotalAmount.add(orderPaymentInfo.getTotalAmount());
//                    paidAmount = paidAmount.add(orderPaymentInfo.getPaidAmount());
//                    unpaidAmount = unpaidAmount.add(orderPaymentInfo.getUnpaidAmount());
//                    totalAmount = totalAmount.add(orderPaymentInfo.getTotalAmount());
//                }
//            }
//        }

        // 查出这个展会配置的表单信息
        List<ExpoForm> formList = expoFormManager.getFormListByFormGroup(expoExhibitor.getExpoId(),
                ExpoFormEnums.FormGroup.EXHIBITOR.getCode(), CommonStatus.YesOrNoEnum.YES.getValue());
        List<ExhibitorFormVO> exhibitorFormVOList = new ArrayList<>();
        if(CollectionUtil.isNotEmpty(formList)){
            // 查询这个展商填写了的表单
            List<ExpoReferenceForm> referenceFormList = expoReferenceFormManager
                    .getListByBusinessIdFromGroup(expoExhibitor.getExpoId(), expoExhibitor.getId(), ExpoFormEnums.FormGroup.EXHIBITOR.getCode());
            Map<Integer, ExpoReferenceForm> referenceFormMap = referenceFormList.stream()
                    .collect(Collectors.toMap(ExpoReferenceForm::getFormType, Function.identity()));
            for(ExpoForm expoForm : formList){
                ExhibitorFormVO exhibitorForm = CopyObjectUtils.copyAtoB(expoForm, ExhibitorFormVO.class);
                if(CollectionUtil.isNotEmpty(referenceFormMap) && referenceFormMap.containsKey(expoForm.getId())){
                    ExpoReferenceForm erf = referenceFormMap.get(expoForm.getId());
                    exhibitorForm.setExhibitorFormId(erf.getId());
                    exhibitorForm.setSubmitTime(erf.getSubmitTime());
                    exhibitorForm.setAuditStatus(erf.getAuditStatus());
                    exhibitorForm.setAuditRemark(erf.getAuditRemark());
                }
                exhibitorFormVOList.add(exhibitorForm);
            }
        }
        ExhibitorDetailPlatformVO vo = CopyObjectUtils.copyAtoB(expoExhibitor, ExhibitorDetailPlatformVO.class);
        buildVOEnum(vo);
        vo.setId(expoExhibitor.getId());
        vo.setExpoId(expoExhibitor.getExpoId());
        vo.setBoothList(boothVOList);
        vo.setExhibitorFormList(exhibitorFormVOList);
        vo.setBoothTotalAmount(boothTotalAmount);
        vo.setLeaseTotalAmount(leaseTotalAmount);
        vo.setPaidAmount(paidAmount);
        vo.setUnpaidAmount(unpaidAmount);
        vo.setTotalAmount(totalAmount);
        return vo;
    }

    @Override
    public ExhibitorDetailVO getDetailParticipants(ExpoExhibitorDTO dto) {
        ExpoExhibitor expoExhibitor = checkExpoExhibitor(dto.getId());
        // 展商展位号
        List<ExpoExhibitorBoothDTO> exhibitorBoothList = expoExhibitorBoothManager.getDetailByExhibitorId(expoExhibitor.getId());
        List<String> boothNumbersList = null;
        if(CollectionUtil.isNotEmpty(exhibitorBoothList)){
            boothNumbersList = exhibitorBoothList.stream().map(ExpoExhibitorBoothDTO::getBoothNumber).collect(Collectors.toList());
        }
        // 展商订单
        List<ExpoBoothOrder> boothOrderList = expoBoothOrderManager.getByExpoExhibitorId(expoExhibitor.getId());
        List<ExhibitorBoothOrderDetailVO> boothOrderVOList = new ArrayList<>();
        if(CollectionUtil.isNotEmpty(boothOrderList)){
            List<Long> orderNoList = boothOrderList.stream().map(ExpoBoothOrder::getOrderNo).collect(Collectors.toList());
            // 批量查询订单金额信息
            Map<Long, OrderPaymentInfoResp> orderPaymentInfoMap = feignCommonManager.batchQueryOrderPaymentInfo(orderNoList);
            for(Long orderNo : orderNoList){
                ExhibitorBoothOrderDetailVO orderDetailVO = new ExhibitorBoothOrderDetailVO();
                orderDetailVO.setOrderNo(orderNo);
                OrderPaymentInfoResp orderPaymentInfoResp = orderPaymentInfoMap.get(orderNo);
                if(null != orderPaymentInfoResp){
                    orderDetailVO.setTotalAmount(orderPaymentInfoResp.getTotalAmount());
                    orderDetailVO.setIsPaid(orderPaymentInfoResp.getIsPaid());
                }
                boothOrderVOList.add(orderDetailVO);
            }
        }
        // 审核中数量
        Integer waitAuditCount = 0;
        // 审核通过数量
        Integer auditPassCount = 0;
        // 已拒绝数量
        Integer auditRejectCount = 0;
        // 待提交数量
        Integer toBeSubmitCount = 0;

        // 查出这个展会配置的表单信息
        List<ExpoForm> formList = expoFormManager.getFormListByFormGroup(expoExhibitor.getExpoId(),
                ExpoFormEnums.FormGroup.EXHIBITOR.getCode(), CommonStatus.YesOrNoEnum.YES.getValue());
        List<ExhibitorFormVO> exhibitorFormVOList = new ArrayList<>();
        if(CollectionUtil.isNotEmpty(formList)){
            // 查询这个展商填写了的表单
            List<ExpoReferenceForm> referenceFormList = expoReferenceFormManager
                    .getListByBusinessIdFromGroup(expoExhibitor.getExpoId(), expoExhibitor.getId(), ExpoFormEnums.FormGroup.EXHIBITOR.getCode());
            Map<Integer, ExpoReferenceForm> referenceFormMap = referenceFormList.stream()
                    .collect(Collectors.toMap(ExpoReferenceForm::getFormType, Function.identity()));
            for(ExpoForm expoForm : formList){
                ExhibitorFormVO exhibitorForm = CopyObjectUtils.copyAtoB(expoForm, ExhibitorFormVO.class);
                if(CollectionUtil.isNotEmpty(referenceFormMap) && referenceFormMap.containsKey(expoForm.getId())){
                    ExpoReferenceForm erf = referenceFormMap.get(expoForm.getId());
                    exhibitorForm.setExhibitorFormId(erf.getId());
                    exhibitorForm.setSubmitTime(erf.getSubmitTime());
                    exhibitorForm.setAuditStatus(erf.getAuditStatus());
                    exhibitorForm.setAuditRemark(erf.getAuditRemark());
                    switch (ExpoReferenceFormEnum.AuditStatus.getEnumByCode(erf.getAuditStatus())){
                        case WAIT_AUDIT:
                            waitAuditCount++;
                            break;
                        case AUDIT_REJECT:
                            auditRejectCount++;
                            break;
                        case AUDIT_PASS:
                            auditPassCount++;
                            break;
                    }
                }else{
                    toBeSubmitCount++;
                }
                exhibitorFormVOList.add(exhibitorForm);
            }
        }
        ExhibitorDetailVO vo = new ExhibitorDetailVO();
        vo.setId(expoExhibitor.getId());
        vo.setExpoId(expoExhibitor.getExpoId());
        vo.setBoothNumbersList(boothNumbersList);
        vo.setBoothOrderList(boothOrderVOList);
        vo.setExhibitorFormList(exhibitorFormVOList);
        vo.setAuditPassCount(auditPassCount);
        vo.setAuditRejectCount(auditRejectCount);
        vo.setWaitAuditCount(waitAuditCount);
        vo.setToBeSubmitCount(toBeSubmitCount);
        return vo;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delete(ExpoExhibitorDTO dto) {
        ExpoExhibitor expoExhibitor = checkExpoExhibitor(dto.getId());
        dto.setUpdateTime(LocalDateTime.now());
        // 展商展位
        List<ExpoExhibitorBooth> exhibitorBoothList = expoExhibitorBoothManager.getListByExhibitorId(expoExhibitor.getId());
        if(CollectionUtil.isNotEmpty(exhibitorBoothList)){
            List<Integer> boothIds = exhibitorBoothList.stream().map(ExpoExhibitorBooth::getBoothId).collect(Collectors.toList());
            // 展位需要回归空闲状态
            expoBoothManager.updateStatus(boothIds, ExpoBoothEnums.Status.NOT_SOLD.getCode(), dto.getUserId(), dto.getUpdateTime());
        }
        expoExhibitorManager.removeById(expoExhibitor.getId(), dto.getUserId(), dto.getUpdateTime());
        expoExhibitorBoothManager.removeByExhibitorId(expoExhibitor.getId(), dto.getUserId(), dto.getUpdateTime());
        expoBoothOrderManager.removeByExhibitorId(expoExhibitor.getId(), dto.getUserId(), dto.getUpdateTime());
        expoBoothOrderDetailManager.removeByExhibitorId(expoExhibitor.getId(), dto.getUserId(), dto.getUpdateTime());
    }

    /**
     * 校验展商信息
     * @param id
     * @return
     */
    private ExpoExhibitor checkExpoExhibitor(Integer id){
        ExpoExhibitor expoExhibitor = expoExhibitorManager.getByExhibitorId(id);
        if(null == expoExhibitor){
            throw new BusinessException(-1, "展商不存在");
        }
        return expoExhibitor;
    }

    @Override
    public ExhibitorInviteQrCodeVO getInviteQrCode(ExpoExhibitorDTO dto) {
        ExpoExhibitor expoExhibitor = checkExpoExhibitor(dto.getId());
        ExpoInfo expoInfo = expoInfoManager.getById(expoExhibitor.getExpoId());
        // todo 生成展商渠道
        // todo 需要查站点域名
        String gateway = "";
        String locationUrl = GatewayRoutingConstants.getScanExhibitorAudienceRegisterCode();
        String qrcodeUrl = String.format(gateway + locationUrl, expoInfo.getId(), dto.getCompanyId(), expoExhibitor.getExpoId());
        QrConfig qrConfig = new QrConfig(200, 200);
        String png = QrCodeUtil.generateAsBase64(qrcodeUrl, qrConfig, "png");
        ExhibitorInviteQrCodeVO vo = new ExhibitorInviteQrCodeVO();
        vo.setQrcodeUrl(qrcodeUrl);
        vo.setQrcodeImage(png);
        return vo;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void addLeaseOrder(AddExhibitorOrderDTO dto) {
        addOrder(dto, 1);
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void addTravelOrder(AddExhibitorOrderDTO dto) {
        addOrder(dto, 2);
    }

    /**
     * 添加订单
     * @param dto 参数DTO
     * @param type 订单类型
     */
    public void addOrder(AddExhibitorOrderDTO dto, Integer type){
        // 校验参数
        validateOrderParam(dto);
        ExpoExhibitor expoExhibitor = checkExpoExhibitor(dto.getId());
        ExpoInfo expoInfo = expoInfoManager.getById(expoExhibitor.getExpoId());
        // 计算订单总金额
        BigDecimal totalAmount = calculateTotalAmount(dto.getOrderDetailList());
        // 查询商品信息
        Set<Integer> shopSkuIds = dto.getOrderDetailList().stream().map(e -> e.getShopSkuId()).collect(Collectors.toSet());
        Map<Integer, ShopSkuBasisResp> shopSkuRespMap = feignCommonManager.queryShopSkuBasisByIds(shopSkuIds);
        if(shopSkuRespMap.size() != dto.getOrderDetailList().size()){
            throw new BusinessException(ExpoResultCode.ExpoResultEnum.EXPO_INVALID_PRODUCT.getCode(),
                    ExpoResultCode.ExpoResultEnum.EXPO_INVALID_PRODUCT.getMessage());
        }

        // 查询客户是否存在
        Map<Integer, CustomerResp> customerMap = feignCommonManager.getBatchCustomerByIds(Lists.newArrayList(dto.getCustomerId()));
        if(null == customerMap || !customerMap.containsKey(dto.getCustomerId())){
            throw new BusinessException(ExpoResultCode.ExpoResultEnum.EXPO_CUSTOMER_NOT_FOUND.getCode(),
                    ExpoResultCode.ExpoResultEnum.EXPO_CUSTOMER_NOT_FOUND.getMessage());
        }
        CustomerResp customerResp = customerMap.get(dto.getCustomerId());
        dto.setCustomerCompanyId(customerResp.getCustomerCompanyId());
        dto.setTotalAmount(totalAmount);
        dto.setDiscountAmount(dto.getDiscountAmount() != null ? dto.getDiscountAmount() : BigDecimal.ZERO);
        dto.setExhibitorId(expoExhibitor.getId());
        // 保存订单
        Integer orderId = null;

        if(type == 1){
            ExpoLeaseOrder order = CopyObjectUtils.copyAtoB(dto, ExpoLeaseOrder.class);
            expoLeaseOrderManager.save(order);
            orderId = order.getId();
        } else {
            ExpoTravelOrder order = CopyObjectUtils.copyAtoB(dto, ExpoTravelOrder.class);
            expoTravelOrderManager.save(order);
            orderId = order.getId();
        }

        // 添加附件
        ExpoAttachmentFileEnum.Type fileType = type == 1 ? ExpoAttachmentFileEnum.Type.LEASE_ORDER : ExpoAttachmentFileEnum.Type.TRAVEL_ORDER;
        expoAttachmentFileManager.addOrUpdateAttachmentFile(expoExhibitor.getExpoId(), orderId, dto.getAttachmentList(), fileType);

        // 创建订单明细
        List<AddExhibitorOrderDetailDTO> orderDetailList = new ArrayList<>();
        GenOrderProductReq productReq;
        List<GenOrderProductReq> orderProductList = new ArrayList<>();
        for (AddExhibitorOrderDetailDTO detailDTO : dto.getOrderDetailList()) {
            AddExhibitorOrderDetailDTO detail = new AddExhibitorOrderDetailDTO();
            detail.setOrderId(orderId);
            detail.setQuantity(detailDTO.getQuantity());
            detail.setPrice(detailDTO.getPrice());
            detail.setTaxRate(detailDTO.getTaxRate());

            // 获取展位商品信息并设置快照
            ShopSkuBasisResp shopSkuBasisResp = shopSkuRespMap.get(detailDTO.getShopSkuId());
            detail.setSkuId(shopSkuBasisResp.getSkuId());
            detail.setShopSkuId(shopSkuBasisResp.getId());
            detail.setSkuCode(shopSkuBasisResp.getSkuCode());
            detail.setName(shopSkuBasisResp.getName());
            detail.setStandardJson(JSON.toJSONString(shopSkuBasisResp.getStandardList()));
            detail.setMarketPrice(shopSkuBasisResp.getMarketPrice());
            detail.setUnit(shopSkuBasisResp.getUnit());
            orderDetailList.add(detail);

            // 订单需要的参数
            productReq = new GenOrderProductReq();
            productReq.setSkuId(detail.getSkuId());
            productReq.setShopSkuId(detail.getShopSkuId());
            productReq.setCategoryId(shopSkuBasisResp.getCategoryId());
            productReq.setCategoryName(shopSkuBasisResp.getCategoryName());
            productReq.setProductName(shopSkuBasisResp.getName());
            productReq.setNumber(BigDecimal.ONE);
            productReq.setProductUnit(shopSkuBasisResp.getUnit());
            productReq.setProductUnitNum(shopSkuBasisResp.getUnitNum());
            productReq.setPriceNum(shopSkuBasisResp.getPriceNum());
            productReq.setSkuCode(shopSkuBasisResp.getSkuCode());
            productReq.setProductPrice(detailDTO.getPrice());
            productReq.setProductMarketPrice(shopSkuBasisResp.getMarketPrice());
            productReq.setProductMainPic(CollectionUtil.isNotEmpty(shopSkuBasisResp.getImageList()) ? shopSkuBasisResp.getImageList().get(0) : null);
            productReq.setProductSpecificationList(CopyObjectUtils.copyAlistToBlist(shopSkuBasisResp.getStandardList(), com.echronos.order.req.StandardReq.class));
            productReq.setHaveTranFee(CommonStatus.YesOrNoEnum.NO.getValue());
            productReq.setBrandId(shopSkuBasisResp.getBrandId());
            productReq.setBrandName(null);
            productReq.setBarsCode(shopSkuBasisResp.getBarsCode());
            productReq.setSkuCode(shopSkuBasisResp.getSkuCode());
            productReq.setBelongtoCompanyId(expoInfo.getCompanyId());
            productReq.setIsGift(CommonStatus.YesOrNoEnum.NO.getValue());
            orderProductList.add(productReq);

        }

        // 订单与订单明细关联
        if(type == 1){
            List<ExpoLeaseOrderDetail> addOrderDetailList = CopyObjectUtils.copyAlistToBlist(orderDetailList, ExpoLeaseOrderDetail.class);
            expoLeaseOrderDetailManager.saveBatch(addOrderDetailList);
        } else {
            List<ExpoTravelOrderDetail> addOrderDetailList = CopyObjectUtils.copyAlistToBlist(orderDetailList, ExpoTravelOrderDetail.class);
            expoTravelOrderDetailManager.saveBatch(addOrderDetailList);
        }

        // 调用订单创建接口
        PayWayResp expenseOrderPayWay = feignCommonManager.getExpenseOrderPayWay(expoInfo.getCompanyId());
        QueryCompanyResp sellerCompanyResp = feignCommonManager.queryCompanyByIds(Lists.newArrayList(expoInfo.getCompanyId())).get(0);
        GenOrderReq orderReq = new GenOrderReq();
        orderReq.setSerialNumber(orderId);
        orderReq.setOrderAppType(null);
        orderReq.setIsOnline(false);
        orderReq.setCoreDocumentId(orderId);
        orderReq.setBuyerMemberId(customerResp.getMemberId());
        orderReq.setBuyerName(customerResp.getName());
        orderReq.setBuyerCompanyId(customerResp.getCompanyId());
        orderReq.setBuyerCompanyName(customerResp.getCompanyName());
        orderReq.setSellerCompanyId(expoInfo.getCompanyId());
        orderReq.setSellerCompanyName(sellerCompanyResp.getCompanyName());
        orderReq.setPayWayNo(expenseOrderPayWay.getPayWayNo());
        List<AttachmentsOssAddrReq> attachmentList = null;
        if(CollectionUtil.isNotEmpty(dto.getAttachmentList())){
            attachmentList = new ArrayList<>();
            for(ExpoAttachmentFileDTO attachmentFileDTO : dto.getAttachmentList()){
                AttachmentsOssAddrReq attachmentReq = new AttachmentsOssAddrReq();
                attachmentReq.setAttachmentsOssAddr(attachmentFileDTO.getFilePath());
                attachmentReq.setFileName(attachmentFileDTO.getFileName());
                attachmentReq.setSize(attachmentFileDTO.getFileSize());
                attachmentReq.setFileType(attachmentFileDTO.getType());
                attachmentList.add(attachmentReq);
            }
        }
        orderReq.setAttachmentList(attachmentList);
        orderReq.setOrderProductList(orderProductList);
        GenOrderParentReq req = new GenOrderParentReq();
        req.setRequestUserId(dto.getUserId());
        req.setRequestMemberId(dto.getMemberId());
        req.setCurrentCompanyId(dto.getCompanyId());
        req.setGenOrderReqList(Lists.newArrayList(orderReq));
        Map<Integer, GenOrderResp> genOrderRespMap = feignCommonManager.addGenOrder(req);
        GenOrderResp genOrderResp = genOrderRespMap.get(orderId);

        // 保存订单编号
        if(type == 1){
            ExpoLeaseOrder updateOrder = new ExpoLeaseOrder();
            updateOrder.setId(orderId);
            updateOrder.setOrderNo(genOrderResp.getOrderNo());
            expoLeaseOrderManager.updateById(updateOrder);
        }else{
            ExpoTravelOrder updateOrder = new ExpoTravelOrder();
            updateOrder.setId(orderId);
            updateOrder.setOrderNo(genOrderResp.getOrderNo());
            expoTravelOrderManager.updateById(updateOrder);
        }
    }

    /**
     * 验证订单参数
     * @param dto
     */
    private void validateOrderParam(AddExhibitorOrderDTO dto) {
        // 校验优惠金额不能超过总金额
        BigDecimal totalAmount = calculateTotalAmount(dto.getOrderDetailList());
        if (dto.getDiscountAmount() != null && dto.getDiscountAmount().compareTo(totalAmount) > 0) {
            throw new BusinessException(ExpoResultCode.ExpoResultEnum.EXPO_DISCOUNT_AMOUNT_EXCEED.getCode(),
                    ExpoResultCode.ExpoResultEnum.EXPO_DISCOUNT_AMOUNT_EXCEED.getMessage());
        }

    }


    /**
     * 计算订单总金额
     * @param orderDetails 订单明细
     * @return 总金额
     */
    private BigDecimal calculateTotalAmount(List<AddExhibitorOrderDetailDTO> orderDetails) {
        return orderDetails.stream()
                .map(detail -> detail.getPrice().multiply(detail.getQuantity()))
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void scanCode(ExpoExhibitorDTO dto) {
        //查询是否存在观众
        ExpoAudience expoAudience = expoAudienceManager.queryAudienceById(dto.getId(), dto.getExpoId());
        if (Objects.isNull(expoAudience)) {
            throw new BusinessException(ExpoResultCode.ExpoResultEnum.EXPO_AUDIENCE_IS_NOT_EXIST.getCode(),
                    ExpoResultCode.ExpoResultEnum.EXPO_AUDIENCE_IS_NOT_EXIST.getMessage());
        }
        //查询当前登录的公司是否为自己的展商
        ExpoExhibitor expoExhibitor = expoExhibitorManager.queryExhibitorByCompanyId(dto.getExpoId(), dto.getCompanyId());
        if(Objects.isNull(expoExhibitor)){
            throw new BusinessException(ExpoResultCode.ExpoResultEnum.EXPO_COMPANY_NOT_BELONG_TO_EXHIBITOR.getCode(),
                    ExpoResultCode.ExpoResultEnum.EXPO_COMPANY_NOT_BELONG_TO_EXHIBITOR.getMessage());
        }
        //记录展商扫码记录
        ExpoExhibitorScanRecord expoExhibitorScanRecord = new ExpoExhibitorScanRecord();
        expoExhibitorScanRecord.setExhibitorId(expoExhibitor.getId());
        expoExhibitorScanRecord.setAudienceId(dto.getExpoAudienceId());
        expoExhibitorScanRecord.setExpoId(dto.getExpoId());
        expoExhibitorScanRecord.setCompanyId(dto.getCompanyId());
        expoExhibitorScanRecordManager.save(expoExhibitorScanRecord);
        //todo 这里需要确认一下是用邮箱去查询还是用电话去查询(现在目前是用e-mail去查询)
        if (StringUtils.isNotBlank(expoAudience.getEmail())) {
            MemberPowerResp memberPowerResp = feignCommonManager.
                    getProprietorshipId(null, expoAudience.getName(), expoAudience.getEmail());
            if (Objects.nonNull(memberPowerResp)) {
                InsertCustomerReq req = new InsertCustomerReq();
                req.setCustomerName(expoAudience.getName());
                //todo 这里还需要确定来源
                req.setCustomerCategory(CustomerCategoryEnum.PERSONAL_CUSTOMER.getCode());
                req.setPhone(expoAudience.getPhone());
                req.setCompanyId(dto.getCompanyId());
                req.setCustomerCompanyId(memberPowerResp.getCompanyId());
                feignCommonManager.insertCustomer(req);
            }
        }
    }

    @Override
    public ExpoPageVO<WebExpoExhibitorVO> webQueryExhibitorList(ExpoExhibitorDTO dto) {
        if(Objects.isNull(dto.getExpoId()) && CollectionUtils.isEmpty(dto.getIdList())){
            throw new BusinessException(ExpoResultCode.ExpoResultEnum.EXPO_EXHIBITOR_ID_OR_EXPO_ID_IS_NULL.getCode(),
                    ExpoResultCode.ExpoResultEnum.EXPO_EXHIBITOR_ID_OR_EXPO_ID_IS_NULL.getMessage());
        }
        IPage<ExpoExhibitorExt> page = expoExhibitorManager.webExhibitorPage(dto);
        if (CollectionUtils.isEmpty(page.getRecords())) {
            return new ExpoPageVO<>();
        }
        //这里返回的customerId是供应商ID
        List<Integer> customerCompanyIdList = page.getRecords().stream().
                map(ExpoExhibitorExt::getCustomerCompanyId).distinct().collect(Collectors.toList());
        Map<Integer, QueryCompanyResp> companyRespMap = feignCommonManager.queryCompanyByIds(customerCompanyIdList);
        //查询供应商分类信息
        List<ExpoExhibitorExt> expoExhibitorExtList = page.getRecords().stream().
                filter(e -> Objects.nonNull(e.getCustomerId())).distinct().collect(Collectors.toList());
        List<Integer> customerIdList = expoExhibitorExtList.stream().map(ExpoExhibitorExt::getCustomerId).collect(Collectors.toList());
        Map<Integer, List<CategoryResp>> categoryMap = feignCommonManager.queryCategoryListBySupplierId(customerIdList);
        List<WebExpoExhibitorVO> voList = CopyObjectUtils.copyAlistToBlist(page.getRecords(), WebExpoExhibitorVO.class);
        for (WebExpoExhibitorVO item : voList) {
            if (Objects.nonNull(companyRespMap) && companyRespMap.containsKey(item.getCustomerCompanyId())) {
                QueryCompanyResp companyResp = companyRespMap.get(item.getCustomerCompanyId());
                item.setAvatar(companyResp.getLogoUrl());
            }
            // 组装供应商分类信息
            if (Objects.nonNull(categoryMap) && categoryMap.containsKey(item.getId())) {
                item.setSupplierCategoryRespList(categoryMap.get(item.getId()));
            }
        }
        return new ExpoPageVO<>(page.getTotal(), page.getPages(), voList);
    }

    @Override
    public ValidityHasAudienceVO validityHasAudience(ExpoExhibitorDTO dto) {
        ValidityHasAudienceVO vo = new ValidityHasAudienceVO();
        //查询展商信息
        ExpoExhibitor expoExhibitor = expoExhibitorManager.getByExhibitorId(dto.getId());
        if(Objects.isNull(expoExhibitor)){
            log.info("查询出来的展商信息为空");
            vo.setHasReservation(Boolean.FALSE);
            return vo;
        }
        //查询展会信息
        ExpoInfo expoInfo = expoInfoManager.getById(expoExhibitor.getExpoId());
        if(Objects.isNull(expoInfo)){
            log.info("查询出来的展会信息为空");
            vo.setHasReservation(Boolean.FALSE);
            return vo;
        }
        //获取当前用户信息
        QueryUserReq req = new QueryUserReq();
        req.setId(dto.getId());
        UserInfoResp userInfoResp = feignCommonManager.queryUserInfo(req);
        if(Objects.isNull(userInfoResp)){
            log.info("查询出来的用户信息为空");
            vo.setHasReservation(Boolean.FALSE);
            return vo;
        }
        ExpoAudience expoAudience = expoAudienceManager.queryExistAudience(expoInfo.getId(), userInfoResp.getEmail());
        if(Objects.isNull(expoAudience)){
            log.info("查询出来的观众信息为空");
            vo.setHasReservation(Boolean.FALSE);
            return vo;
        }
        vo.setHasReservation(Boolean.TRUE);
        return vo;
    }

    /**
     * 预约设置
     *
     * @param dto 参数
     */
    @Override
    public void appointSet(ExpoExhibitorDTO dto) {
        ExpoExhibitor expoExhibitor = expoExhibitorManager.getByExhibitorId(dto.getId());
        if (Objects.isNull(expoExhibitor)) {
            throw new BusinessException(-1, "展商不存在");
        }
        if (dto.getIsAppoint().equals(CommonStatus.YesOrNoEnum.YES.getValue())) {
            if (expoExhibitor.getIsAppoint().equals(CommonStatus.YesOrNoEnum.YES.getValue())) {
                throw new BusinessException(-1, "预约功能已开启");
            }
        } else if (dto.getIsAppoint().equals(CommonStatus.YesOrNoEnum.NO.getValue())) {
            if (expoExhibitor.getIsAppoint().equals(CommonStatus.YesOrNoEnum.NO.getValue())) {
                throw new BusinessException(-1, "预约功能已关闭");
            }
        } else {
            throw new BusinessException(-1, "参数错误");
        }
        // 更新
        expoExhibitor.setIsAppoint(dto.getIsAppoint());
        expoExhibitorManager.updateById(expoExhibitor);
    }

    @Override
    public ExhibitorNumberVO queryExhibitorTotal(ExpoExhibitorDTO dto) {
        List<ExpoExhibitor> exhibitorList = expoExhibitorManager.queryExhibitorNumber(dto.getExpoId());
        ExhibitorNumberVO vo = new ExhibitorNumberVO();
        vo.setTotal(exhibitorList.size());
        return vo;
    }

    @Override
    public ExpoExhibitorOtherInfoStatusVO expoExhibitorEnterpriseInfoStatus(ExpoExhibitorDTO dto) {
        List<ExpoExhibitor> exhibitorList = expoExhibitorManager.queryExhibitorNumber(dto.getExpoId());
        if(CollectionUtil.isEmpty(exhibitorList)){
            return null;
        }
        ExpoExhibitorOtherInfoStatusVO vo = new ExpoExhibitorOtherInfoStatusVO();
        //未提交的展商数据
        List<ExpoExhibitor> toSubmitList = exhibitorList.stream().filter(e ->
                ExpoExhibitorEnums.EnterpriseInfoStatus.TO_SUBMIT.code().equals(e.getEnterpriseInfoStatus())).collect(Collectors.toList());
        //待审核的展商数据
        List<ExpoExhibitor> toAuditList = exhibitorList.stream().filter(e ->
                ExpoExhibitorEnums.EnterpriseInfoStatus.TO_AUDIT.code().equals(e.getEnterpriseInfoStatus())).collect(Collectors.toList());
        //审核通过的展商数据
        List<ExpoExhibitor> approvedList = exhibitorList.stream().filter(e ->
                ExpoExhibitorEnums.EnterpriseInfoStatus.APPROVED.code().equals(e.getEnterpriseInfoStatus())).collect(Collectors.toList());
        //审核不通过的展商数据
        List<ExpoExhibitor> rejectedList = exhibitorList.stream().filter(e ->
                ExpoExhibitorEnums.EnterpriseInfoStatus.REJECTED.code().equals(e.getEnterpriseInfoStatus())).collect(Collectors.toList());
        if(CollectionUtil.isNotEmpty(toSubmitList)){
            BigDecimal toSubmitConversionRate = new BigDecimal(toSubmitList.size()).
                    divide(new BigDecimal(exhibitorList.size()), NumberConstant.FOUR, RoundingMode.HALF_UP);
            vo.setToSubmitConversionRate(toSubmitConversionRate.multiply(new BigDecimal(NumberConstant.ONE_HUNDRED))
                    .setScale(NumberConstant.TWO, NumberConstant.ONE_HUNDRED));
        }
        if (CollectionUtil.isNotEmpty(toAuditList)) {
            BigDecimal toAuditConversionRate = new BigDecimal(toAuditList.size()).
                    divide(new BigDecimal(exhibitorList.size()), NumberConstant.FOUR, RoundingMode.HALF_UP);
            vo.setToAuditConversionRate(toAuditConversionRate.multiply(new BigDecimal(NumberConstant.ONE_HUNDRED))
                    .setScale(NumberConstant.TWO, NumberConstant.ONE_HUNDRED));
        }
        if (CollectionUtil.isNotEmpty(approvedList)) {
            BigDecimal approvedConversionRate = new BigDecimal(approvedList.size()).
                    divide(new BigDecimal(exhibitorList.size()), NumberConstant.FOUR, RoundingMode.HALF_UP);
            vo.setApprovedConversionRate(approvedConversionRate.multiply(new BigDecimal(NumberConstant.ONE_HUNDRED))
                    .setScale(NumberConstant.TWO, NumberConstant.ONE_HUNDRED));
        }
        if  (CollectionUtil.isNotEmpty(rejectedList)) {
            BigDecimal rejectedConversionRate = new BigDecimal(rejectedList.size()).
                    divide(new BigDecimal(exhibitorList.size()), NumberConstant.FOUR, RoundingMode.HALF_UP);
            vo.setRejectedConversionRate(rejectedConversionRate.multiply(new BigDecimal(NumberConstant.ONE_HUNDRED))
                    .setScale(NumberConstant.TWO, NumberConstant.ONE_HUNDRED));
        }
        return vo;
    }

    @Override
    public ExpoExhibitorOtherInfoStatusVO expoExhibitorJournalInfoStatus(ExpoExhibitorDTO dto) {
        List<ExpoExhibitor> exhibitorList = expoExhibitorManager.queryExhibitorNumber(dto.getExpoId());
        if(CollectionUtil.isEmpty(exhibitorList)){
            return null;
        }
        ExpoExhibitorOtherInfoStatusVO vo = new ExpoExhibitorOtherInfoStatusVO();
        //未提交的展商数据
        List<ExpoExhibitor> toSubmitList = exhibitorList.stream().filter(e ->
                ExpoExhibitorEnums.JournalInfoStatus.TO_SUBMIT.code().equals(e.getJournalInfoStatus())).collect(Collectors.toList());
        //待审核的展商数据
        List<ExpoExhibitor> toAuditList = exhibitorList.stream().filter(e ->
                ExpoExhibitorEnums.JournalInfoStatus.TO_AUDIT.code().equals(e.getJournalInfoStatus())).collect(Collectors.toList());
        //审核通过的展商数据
        List<ExpoExhibitor> approvedList = exhibitorList.stream().filter(e ->
                ExpoExhibitorEnums.JournalInfoStatus.APPROVED.code().equals(e.getJournalInfoStatus())).collect(Collectors.toList());
        //审核不通过的展商数据
        List<ExpoExhibitor> rejectedList = exhibitorList.stream().filter(e ->
                ExpoExhibitorEnums.JournalInfoStatus.REJECTED.code().equals(e.getJournalInfoStatus())).collect(Collectors.toList());
        if(CollectionUtil.isNotEmpty(toSubmitList)){
            BigDecimal toSubmitConversionRate = new BigDecimal(toSubmitList.size()).
                    divide(new BigDecimal(exhibitorList.size()), NumberConstant.FOUR, RoundingMode.HALF_UP);
            vo.setToSubmitConversionRate(toSubmitConversionRate.multiply(new BigDecimal(NumberConstant.ONE_HUNDRED))
                    .setScale(NumberConstant.TWO, NumberConstant.ONE_HUNDRED));
        }
        if (CollectionUtil.isNotEmpty(toAuditList)) {
            BigDecimal toAuditConversionRate = new BigDecimal(toAuditList.size()).
                    divide(new BigDecimal(exhibitorList.size()), NumberConstant.FOUR, RoundingMode.HALF_UP);
            vo.setToAuditConversionRate(toAuditConversionRate.multiply(new BigDecimal(NumberConstant.ONE_HUNDRED))
                    .setScale(NumberConstant.TWO, NumberConstant.ONE_HUNDRED));
        }
        if(CollectionUtil.isNotEmpty(approvedList)){
            BigDecimal approvedConversionRate = new BigDecimal(approvedList.size()).
                    divide(new BigDecimal(exhibitorList.size()), NumberConstant.FOUR, RoundingMode.HALF_UP);
            vo.setApprovedConversionRate(approvedConversionRate.multiply(new BigDecimal(NumberConstant.ONE_HUNDRED))
                    .setScale(NumberConstant.TWO, NumberConstant.ONE_HUNDRED));
        }
        if(CollectionUtil.isNotEmpty(rejectedList)){
            BigDecimal rejectedConversionRate = new BigDecimal(rejectedList.size()).
                    divide(new BigDecimal(exhibitorList.size()), NumberConstant.FOUR, RoundingMode.HALF_UP);
            vo.setRejectedConversionRate(rejectedConversionRate.multiply(new BigDecimal(NumberConstant.ONE_HUNDRED))
                    .setScale(NumberConstant.TWO, NumberConstant.ONE_HUNDRED));
        }
        return vo;
    }

    @Override
    public ExpoPageVO<WebExpoExhibitorVO> queryExhibitorListByTenantId(ExpoExhibitorDTO dto) {
        TenantInfoResp tenantInfo = feignCommonManager.getTenantInfo(dto.getTenantId());
        if(Objects.isNull(tenantInfo)){
            log.info("查询租户信息为空");
            return new ExpoPageVO<>();
        }
        dto.setCompanyId(tenantInfo.getCompanyId());
        IPage<ExpoExhibitorExt> page = expoExhibitorManager.queryAllExhibitorList(dto);
        if (CollectionUtils.isEmpty(page.getRecords())) {
            return new ExpoPageVO<>();
        }
        //TODO 这里要确认一下 目前展商村的customerId 是客户ID 但是建站获取展商的数据是供应商数据
        List<Integer> customerCompanyIdList = page.getRecords().stream().
                map(ExpoExhibitorExt::getCustomerCompanyId).distinct().collect(Collectors.toList());
        Map<Integer, QueryCompanyResp> companyRespMap = feignCommonManager.queryCompanyByIds(customerCompanyIdList);
        //查询供应商分类信息
        List<ExpoExhibitorExt> expoExhibitorExtList = page.getRecords().stream().
                filter(e -> Objects.nonNull(e.getCustomerId())).distinct().collect(Collectors.toList());
        List<Integer> customerIdList = expoExhibitorExtList.stream().map(ExpoExhibitorExt::getCustomerId).collect(Collectors.toList());
        Map<Integer, List<CategoryResp>> categoryMap = feignCommonManager.queryCategoryListBySupplierId(customerIdList);
        List<WebExpoExhibitorVO> voList = CopyObjectUtils.copyAlistToBlist(page.getRecords(), WebExpoExhibitorVO.class);
        for (WebExpoExhibitorVO item : voList) {
            if (Objects.nonNull(companyRespMap) && companyRespMap.containsKey(item.getCustomerCompanyId())) {
                QueryCompanyResp companyResp = companyRespMap.get(item.getCustomerCompanyId());
                item.setAvatar(companyResp.getLogoUrl());
            }
            // 组装供应商分类信息
            if (Objects.nonNull(categoryMap) && categoryMap.containsKey(item.getId())) {
                item.setSupplierCategoryRespList(categoryMap.get(item.getId()));
            }
        }
        return new ExpoPageVO<>(page.getTotal(), page.getPages(), voList);
    }
}
