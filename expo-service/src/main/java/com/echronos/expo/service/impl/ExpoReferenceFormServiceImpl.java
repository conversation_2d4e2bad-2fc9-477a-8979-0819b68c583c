package com.echronos.expo.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.echronos.commons.exception.BusinessException;
import com.echronos.commons.utils.CopyObjectUtils;
import com.echronos.expo.dto.ExpoReferenceFormDTO;
import com.echronos.expo.enums.ExpoExhibitorEnums;
import com.echronos.expo.enums.ExpoFormEnums;
import com.echronos.expo.enums.ExpoReferenceFormEnum;
import com.echronos.expo.enums.ExpoResultCode;
import com.echronos.expo.manager.*;
import com.echronos.expo.model.*;
import com.echronos.expo.service.IExpoReferenceFormService;
import com.echronos.expo.vo.ExpoReferenceFormExtendVO;
import com.echronos.expo.vo.ExpoReferenceFormVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025-08-09 11:31
 */
@Slf4j
@Service
public class ExpoReferenceFormServiceImpl implements IExpoReferenceFormService {

    @Resource
    private ExpoReferenceFormManager expoReferenceFormManager;
    @Resource
    private ExpoReferenceFormExtendManager expoReferenceFormExtendManager;
    @Resource
    private ExpoFormManager expoFormManager;
    @Resource
    private ExpoExhibitorManager expoExhibitorManager;
    @Resource
    private ExpoAudienceManager expoAudienceManager;
    @Resource
    private ExpoInfoManager expoInfoManager;

    @Override
    public ExpoReferenceFormVO getDetailById(ExpoReferenceFormDTO dto) {
        ExpoReferenceForm referenceForm = expoReferenceFormManager.getById(dto.getId());
        if(null == referenceForm){
            throw new BusinessException(-1, "表单不存在");
        }
        List<ExpoReferenceFormExtend> referenceFormExtendList = expoReferenceFormExtendManager.getListByRefFormId(dto.getId());
        List<ExpoReferenceFormExtendVO> extendVOList = null;
        if(CollectionUtil.isNotEmpty(referenceFormExtendList)){
            extendVOList = CopyObjectUtils.copyAlistToBlist(referenceFormExtendList, ExpoReferenceFormExtendVO.class);
        }
        ExpoReferenceFormVO referenceFormVO = CopyObjectUtils.copyAtoB(referenceForm, ExpoReferenceFormVO.class);
        referenceFormVO.setExtendList(extendVOList);
        return referenceFormVO;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void addOrEdit(ExpoReferenceFormDTO dto) {
        ExpoForm expoForm = expoFormManager.getFormByFormCode(dto.getFormCode());
        if(null == expoForm){
            // 表单不存在未绑定
            throw new BusinessException(ExpoResultCode.ExpoResultEnum.EXPO_FORM_NOT_EXIST_OR_UNBOUND.getCode(),
                    ExpoResultCode.ExpoResultEnum.EXPO_FORM_NOT_EXIST_OR_UNBOUND.getMessage());
        }else if(!expoForm.getIsEnable()){
            // 表单不存在
            throw new BusinessException(ExpoResultCode.ExpoResultEnum.EXPO_FORM_IS_DISABLED.getCode(),
                    ExpoResultCode.ExpoResultEnum.EXPO_FORM_IS_DISABLED.getMessage());
        }
        if(null == dto.getId()){
            ExpoReferenceForm isExist = expoReferenceFormManager.getByGroupTypeBusinessId(dto.getBusinessId(),
                    expoForm.getFormGroup(), expoForm.getFormType());
            if(null != isExist){
                throw new BusinessException(ExpoResultCode.ExpoResultEnum.EXPO_FORM_ALREADY_EXISTS.getCode(),
                        ExpoResultCode.ExpoResultEnum.EXPO_FORM_ALREADY_EXISTS.getMessage());
            }
        }else{
            ExpoReferenceForm referenceForm = expoReferenceFormManager.getById(dto.getId());
            if(null == referenceForm){
                // 表单不存在
                throw new BusinessException(ExpoResultCode.ExpoResultEnum.EXPO_FORM_IS_NOT_EXIST.getCode(),
                        ExpoResultCode.ExpoResultEnum.EXPO_FORM_IS_NOT_EXIST.getMessage());
            }
            if(referenceForm.getExpoId() != expoForm.getExpoId()){
                // 表单不属于次展会
                throw new BusinessException(ExpoResultCode.ExpoResultEnum.EXPO_FORM_NOT_BELONG_TO_EXPO.getCode(),
                        ExpoResultCode.ExpoResultEnum.EXPO_FORM_NOT_BELONG_TO_EXPO.getMessage());
            }
        }
        dto.setAuditStatus(ExpoReferenceFormEnum.AuditStatus.WAIT_AUDIT.getCode());
        if(ExpoFormEnums.FormGroup.AUDIENCE.getCode() == expoForm.getFormGroup()){
            ExpoAudience isExist = expoAudienceManager.queryAudienceById(dto.getBusinessId(), null);
            if(null == isExist){
                // 观众不存在
                throw new BusinessException(ExpoResultCode.ExpoResultEnum.EXPO_AUDIENCE_NOT_EXIST.getCode(),
                        ExpoResultCode.ExpoResultEnum.EXPO_AUDIENCE_NOT_EXIST.getMessage());
            }
            // 观众表单默认审核通过
            dto.setAuditStatus(ExpoReferenceFormEnum.AuditStatus.AUDIT_PASS.getCode());
        } else {
            ExpoExhibitor isExist = expoExhibitorManager.getByExhibitorId(dto.getBusinessId());
            if(null == isExist){
                // 展商不存在
                throw new BusinessException(ExpoResultCode.ExpoResultEnum.EXPO_EXHIBITOR_NOT_EXIST.getCode(),
                        ExpoResultCode.ExpoResultEnum.EXPO_EXHIBITOR_NOT_EXIST.getMessage());
            }
        }
        dto.setExpoId(expoForm.getExpoId());
        dto.setSubmitTime(LocalDateTime.now());
        expoReferenceFormManager.saveOrUpdate(dto);
        expoReferenceFormExtendManager.removeByRefFormId(dto.getId());
        List<ExpoReferenceFormExtend> extendList = CopyObjectUtils.copyAlistToBlist(dto.getExtendList(), ExpoReferenceFormExtend.class);
        expoReferenceFormExtendManager.saveBatch(extendList);
        // 更新展商表单审核状态
        expoExhibitorManager.updateExhibitorAuditStatus(dto.getId(), dto.getAuditStatus(), expoForm.getFormType());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void audit(ExpoReferenceFormDTO dto) {
        ExpoReferenceForm referenceForm = expoReferenceFormManager.getById(dto.getId());
        if(null == referenceForm){
            throw new BusinessException(ExpoResultCode.ExpoResultEnum.EXPO_FORM_IS_DISABLED.getCode(),
                    ExpoResultCode.ExpoResultEnum.EXPO_FORM_IS_DISABLED.getMessage());
        }
        ExpoInfo expoInfo = expoInfoManager.getById(referenceForm.getExpoId());
        if( expoInfo.getCompanyId() != dto.getCompanyId()){
            throw new BusinessException(ExpoResultCode.ExpoResultEnum.EXPO_DATA_NO_PERMISSION.getCode(),
                    ExpoResultCode.ExpoResultEnum.EXPO_DATA_NO_PERMISSION.getMessage());
        }

        ExpoReferenceForm update = new ExpoReferenceForm();
        update.setId(dto.getId());
        update.setAuditStatus(dto.getAuditStatus());
        update.setAuditRemark(dto.getAuditRemark());
        update.setAuditTime(LocalDateTime.now());
        expoReferenceFormManager.updateById(update);

        // 更新展商表单审核状态
        ExpoForm expoForm = expoFormManager.getId(referenceForm.getFormId());
        expoExhibitorManager.updateExhibitorAuditStatus(referenceForm.getBusinessId(), dto.getAuditStatus(), expoForm.getFormType());

    }

}
