package com.echronos.expo.service;

import com.echronos.expo.vo.statistics.ExpoAudienceChannelVO;
import com.echronos.expo.vo.statistics.ExpoAudienceSignNotSignInRateVO;
import com.echronos.expo.vo.statistics.ExpoAudienceStatisticsVO;
import com.echronos.expo.vo.statistics.ExpoIndexCountVO;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025-08-15 15:10
 */
public interface ExpoStatisticsService {

    /**
     * 展会总数（首页）
     * @return
     */
    ExpoIndexCountVO getIndexExpoCount(Integer companyId);

    /**
     * 展会展位数（首页）
     * @return
     */
    ExpoIndexCountVO getIndexExhibitorCount(Integer companyId);

    /**
     * 观众总数（首页）
     * @return
     */
    ExpoIndexCountVO getIndexAudienceCount(Integer companyId);

    /**
     * 展位预订率（首页）
     * @return
     */
    ExpoIndexCountVO getIndexBoothCount(Integer companyId);

    /**
     * 展会观众维度统计
     * @param expoId
     * @return
     */
    ExpoAudienceStatisticsVO getExpoAudienceStatistics(Integer expoId);

    /**
     * 获取展会观众展位签到未签比率
     * @param expoId
     * @return
     */
    ExpoAudienceSignNotSignInRateVO getExpoAudienceSignInNotSignInCount(Integer expoId);

    /**
     * 获取展会观众渠道统计
     * @param expoId
     * @return
     */
    List<ExpoAudienceChannelVO> getExpoAudienceChannelList(Integer expoId);
}
