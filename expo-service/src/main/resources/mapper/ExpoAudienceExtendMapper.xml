<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="com.echronos.expo.dao.ExpoAudienceExtendDao">

    <resultMap id="ExpoAudienceExtendMap" type="com.echronos.expo.model.ExpoAudienceExtend">
        <result property="id" column="id"/>
        <result property="companyId" column="company_id"/>
        <result property="expoId" column="expo_id"/>
        <result property="formId" column="form_id"/>
        <result property="audienceId" column="audience_id"/>
        <result property="colName" column="col_name"/>
        <result property="colValue" column="col_value"/>
        <result property="formCode" column="form_code"/>
        <result property="versionNumber" column="version_number"/>
        <result property="createUser" column="create_user"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="updateUser" column="update_user"/>
        <result property="isDeleted" column="is_deleted"/>
        <result property="tenantId" column="tenant_id"/>
    </resultMap>

    <sql id="columns">
        <![CDATA[
        id
        , company_id, expo_id, form_id, audience_id, col_name, col_value, form_code, version_number, create_user, create_time, update_time, update_user, is_deleted, tenant_id
        ]]>
    </sql>

    <sql id="where">
        <where>
            <if test="null!=id">
                AND id = #{id}
            </if>
            <if test="null!=companyId">
                AND company_id = #{companyId}
            </if>
            <if test="null!=expoId">
                AND expo_id = #{expoId}
            </if>
            <if test="null!=formId">
                AND form_id = #{formId}
            </if>
            <if test="null!=audienceId">
                AND audience_id = #{audienceId}
            </if>
            <if test="null!=colName and ''!=colName">
                AND col_name = #{colName}
            </if>
            <if test="null!=colValue and ''!=colValue">
                AND col_value = #{colValue}
            </if>
            <if test="null!=formCode and ''!=formCode">
                AND form_code = #{formCode}
            </if>
            <if test="null!=versionNumber">
                AND version_number = #{versionNumber}
            </if>
            <if test="null!=createUser">
                AND create_user = #{createUser}
            </if>
            <if test="null!=createTime">
                AND create_time = #{createTime}
            </if>
            <if test="null!=updateTime">
                AND update_time = #{updateTime}
            </if>
            <if test="null!=updateUser">
                AND update_user = #{updateUser}
            </if>
            <if test="null!=isDeleted">
                AND is_deleted = #{isDeleted}
            </if>
            <if test="null!=tenantId and ''!=tenantId">
                AND tenant_id = #{tenantId}
            </if>
        </where>
    </sql>
    <select id="fieldList" resultType="java.lang.String">
        select distinct col_name from ech_expo.ech_expo_audience_extend
        where expo_id = #{expoId}
        and is_deleted = 0
        <if test="formCode != null and formCode != ''">
            and form_code = #{formCode}
        </if>
    </select>
</mapper>

