<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="com.echronos.expo.dao.ExpoTenancySkuDao">

    <resultMap id="ExpoTenancySkuConfigureMap" type="com.echronos.expo.model.ExpoTenancySku">
        <result property="id" column="id"/>
        <result property="expoId" column="expo_id"/>
        <result property="companyId" column="company_id"/>
        <result property="shopSkuId" column="shop_sku_id"/>
        <result property="skuId" column="sku_id"/>
        <result property="createUser" column="create_user"/>
        <result property="createTime" column="create_time"/>
        <result property="updateUser" column="update_user"/>
        <result property="updateTime" column="update_time"/>
        <result property="isDeleted" column="is_deleted"/>
        <result property="tenantId" column="tenant_id"/>
    </resultMap>
    
    <sql id="columns">
        <![CDATA[
        id, expo_id, company_id, shop_sku_id, sku_id, create_user, create_time, update_user, update_time, is_deleted, tenant_id
        ]]>
    </sql>

    <sql id="where">
        <where>
            <if test="null!=id">
                AND id = #{id}
            </if>
            <if test="null!=expoId">
                AND expo_id = #{expoId}
            </if>
            <if test="null!=companyId">
                AND company_id = #{companyId}
            </if>
            <if test="null!=shopSkuId">
                AND shop_sku_id = #{shopSkuId}
            </if>
            <if test="null!=skuId">
                AND sku_id = #{skuId}
            </if>
            <if test="null!=createUser">
                AND create_user = #{createUser}
            </if>
            <if test="null!=createTime">
                AND create_time = #{createTime}
            </if>
            <if test="null!=updateUser">
                AND update_user = #{updateUser}
            </if>
            <if test="null!=updateTime">
                AND update_time = #{updateTime}
            </if>
            <if test="null!=isDeleted">
                AND is_deleted = #{isDeleted}
            </if>
            <if test="null!=tenantId and ''!=tenantId">
                AND tenant_id = #{tenantId}
            </if>
        </where>
    </sql>
</mapper>

