<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="com.echronos.expo.dao.ExpoAudienceEmailRecordsDao">

    <resultMap id="ExpoAudienceEmailRecordsMap" type="com.echronos.expo.model.ExpoAudienceEmailRecords">
        <result property="id" column="id"/>
        <result property="companyId" column="company_id"/>
        <result property="expoId" column="expo_id"/>
        <result property="audienceId" column="audience_id"/>
        <result property="email" column="email"/>
        <result property="content" column="content"/>
        <result property="paramJson" column="param_json"/>
        <result property="businessCode" column="business_code"/>
        <result property="isSend" column="is_send"/>
        <result property="createUser" column="create_user"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="updateUser" column="update_user"/>
        <result property="isDeleted" column="is_deleted"/>
        <result property="tenantId" column="tenant_id"/>
    </resultMap>
    
    <sql id="columns">
        <![CDATA[
        id, company_id, expo_id, audience_id, email, content, param_json, business_code, is_send, create_user, create_time, update_time, update_user, is_deleted, tenant_id
        ]]>
    </sql>

    <sql id="where">
        <where>
            <if test="null!=id">
                AND id = #{id}
            </if>
            <if test="null!=companyId">
                AND company_id = #{companyId}
            </if>
            <if test="null!=expoId">
                AND expo_id = #{expoId}
            </if>
            <if test="null!=audienceId">
                AND audience_id = #{audienceId}
            </if>
            <if test="null!=email and ''!=email">
                AND email = #{email}
            </if>
            <if test="null!=content and ''!=content">
                AND content = #{content}
            </if>
            <if test="null!=paramJson and ''!=paramJson">
                AND param_json = #{paramJson}
            </if>
            <if test="null!=businessCode and ''!=businessCode">
                AND business_code = #{businessCode}
            </if>
            <if test="null!=isSend">
                AND is_send = #{isSend}
            </if>
            <if test="null!=createUser">
                AND create_user = #{createUser}
            </if>
            <if test="null!=createTime">
                AND create_time = #{createTime}
            </if>
            <if test="null!=updateTime">
                AND update_time = #{updateTime}
            </if>
            <if test="null!=updateUser">
                AND update_user = #{updateUser}
            </if>
            <if test="null!=isDeleted">
                AND is_deleted = #{isDeleted}
            </if>
            <if test="null!=tenantId and ''!=tenantId">
                AND tenant_id = #{tenantId}
            </if>
        </where>
    </sql>
</mapper>

