<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="com.echronos.expo.dao.ExpoReferenceFormDao">

    <resultMap id="ExpoReferenceFormMap" type="com.echronos.expo.model.ExpoReferenceForm">
        <result property="id" column="id"/>
        <result property="formId" column="form_id"/>
        <result property="formGroup" column="form_group"/>
        <result property="formType" column="form_type"/>
        <result property="expoId" column="expo_id"/>
        <result property="businessId" column="business_id"/>
        <result property="formCode" column="form_code"/>
        <result property="formVersion" column="form_version"/>
        <result property="submitTime" column="submit_time"/>
        <result property="auditStatus" column="audit_status"/>
        <result property="auditRemark" column="audit_remark"/>
        <result property="createUser" column="create_user"/>
        <result property="createTime" column="create_time"/>
        <result property="updateUser" column="update_user"/>
        <result property="updateTime" column="update_time"/>
        <result property="isDeleted" column="is_deleted"/>
    </resultMap>


    <select id="getByGroupTypeBusinessId" resultType="com.echronos.expo.model.ExpoReferenceForm">
        select
            t1.*
        from
            ech_expo_reference_form t1,
            ech_expo_form t2
        where
            t1.form_id = t2.id
            and t1.is_deleted = 0 and t2.is_deleted = 0
            and t1.business_id = #{businessId}
            and t2.form_group = #{formGroup}
            and t2.form_type = #{formType}
    </select>

</mapper>

