<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="com.echronos.expo.dao.ExpoPrintConfigDao">

    <resultMap id="ExpoPrintConfigMap" type="com.echronos.expo.model.ExpoPrintConfig">
        <result property="id" column="id"/>
        <result property="expoId" column="expo_id"/>
        <result property="formId" column="form_id"/>
        <result property="isContainQr" column="is_contain_qr"/>
        <result property="fieldJson" column="field_json"/>
        <result property="createUser" column="create_user"/>
        <result property="updateUser" column="update_user"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="isDeleted" column="is_deleted"/>
    </resultMap>
    
    <sql id="columns">
        <![CDATA[
        id, expo_id, form_id, is_contain_qr, field_json, create_user, update_user, create_time, update_time, is_deleted
        ]]>
    </sql>

    <sql id="where">
        <where>
            <if test="null!=id">
                AND id = #{id}
            </if>
            <if test="null!=expoId">
                AND expo_id = #{expoId}
            </if>
            <if test="null!=formId">
                AND form_id = #{formId}
            </if>
            <if test="null!=isContainQr">
                AND is_contain_qr = #{isContainQr}
            </if>
            <if test="null!=fieldJson and ''!=fieldJson">
                AND field_json = #{fieldJson}
            </if>
            <if test="null!=createUser">
                AND create_user = #{createUser}
            </if>
            <if test="null!=updateUser">
                AND update_user = #{updateUser}
            </if>
            <if test="null!=createTime">
                AND create_time = #{createTime}
            </if>
            <if test="null!=updateTime">
                AND update_time = #{updateTime}
            </if>
            <if test="null!=isDeleted">
                AND is_deleted = #{isDeleted}
            </if>
        </where>
    </sql>
</mapper>

