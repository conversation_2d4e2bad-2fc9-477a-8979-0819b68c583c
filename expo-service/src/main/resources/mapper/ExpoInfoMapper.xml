<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="com.echronos.expo.dao.ExpoInfoDao">

    <resultMap id="ExpoInfoMap" type="com.echronos.expo.model.ExpoInfo">
        <result property="id" column="id"/>
        <result property="companyId" column="company_id"/>
        <result property="expoName" column="expo_name"/>
        <result property="shortName" column="short_name"/>
        <result property="countryCode" column="country_code"/>
        <result property="cityCode" column="city_code"/>
        <result property="hallName" column="hall_name"/>
        <result property="startTime" column="start_time"/>
        <result property="endTime" column="end_time"/>
        <result property="expoStatus" column="expo_status"/>
        <result property="remark" column="remark"/>
        <result property="createUser" column="create_user"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="updateUser" column="update_user"/>
        <result property="isDeleted" column="is_deleted"/>
        <result property="tenantId" column="tenant_id"/>
    </resultMap>

    <select id="pageList" resultType="com.echronos.expo.model.ext.ExpoInfoExt">
        select
            *,
            count(t2.*) as exhibitorCount,
            t2.id as exhibitorId
        from
            ech_expo_info t1
            left join ech_expo_exhibitor t2 on t1.id = t2.expo_id and t2.is_deleted = 0
        where
            t1.is_deleted = 0
            <if test="dto.companyId != null">
                and t1.company_id = #{dto.companyId}
            </if>
            <if test="dto.customerCompanyId != null">
                and t2.customer_company_id = #{dto.customerCompanyId}
            </if>
        group by t1.id
    </select>

    <select id="getExpoIndexCount" resultType="com.echronos.expo.model.ext.ExpoIndexCountExt">
        SELECT
            COUNT(*) as total
            COUNT(CASE WHEN YEAR(start_time) = YEAR(CURDATE()) THEN 1 END) AS calculateTotalOne,
            COUNT(CASE WHEN YEAR(start_time) = YEAR(CURDATE()) - 1 THEN 1 END) AS calculateTotalTwo
        FROM
            ech_expo_info
        WHERE
            is_deleted = 0
            AND company_id = #{companyId}
    </select>

</mapper>

