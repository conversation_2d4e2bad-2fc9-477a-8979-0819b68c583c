<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="com.echronos.expo.dao.ExpoAppointedPersonnelTimeDao">

    <resultMap id="ExpoAppointedPersonnelTimeMap" type="com.echronos.expo.model.ExpoAppointedPersonnelTime">
        <result property="id" column="id"/>
        <result property="personnelId" column="personnel_id"/>
        <result property="startTime" column="start_time"/>
        <result property="endTime" column="end_time"/>
        <result property="createUser" column="create_user"/>
        <result property="updateUser" column="update_user"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="isDeleted" column="is_deleted"/>
    </resultMap>
    
    <sql id="columns">
        <![CDATA[
        id, personnel_id, start_time, end_time, create_user, update_user, create_time, update_time, is_deleted
        ]]>
    </sql>

    <sql id="where">
        <where>
            <if test="null!=id">
                AND id = #{id}
            </if>
            <if test="null!=personnelId">
                AND personnel_id = #{personnelId}
            </if>
            <if test="null!=startTime">
                AND start_time = #{startTime}
            </if>
            <if test="null!=endTime">
                AND end_time = #{endTime}
            </if>
            <if test="null!=createUser">
                AND create_user = #{createUser}
            </if>
            <if test="null!=updateUser">
                AND update_user = #{updateUser}
            </if>
            <if test="null!=createTime">
                AND create_time = #{createTime}
            </if>
            <if test="null!=updateTime">
                AND update_time = #{updateTime}
            </if>
            <if test="null!=isDeleted">
                AND is_deleted = #{isDeleted}
            </if>
        </where>
    </sql>

    <select id="queryBy" resultType="com.echronos.expo.model.ext.ExpoAppointedPersonnelTimeExt">
        SELECT
            apt.id,
            apt.start_time,
            apt.end_time,
            CASE
                WHEN EXISTS (
                        SELECT 1
                        FROM ech_expo_appointment_time at
                        INNER JOIN ech_expo_appointment a
                        ON at.appointment_id = a.id
                            AND a.status IN (10, 20)
                            AND a.is_deleted = 0
                        WHERE
                        at.appointed_time_id = apt.id
                            AND at.is_deleted = 0
                    ) THEN 0
                ELSE 1
                END AS isAvailable
        FROM ech_expo_appointed_personnel_time apt
        WHERE
            apt.personnel_id = #{personnelId}
          AND apt.is_deleted = 0
        ORDER BY apt.start_time;
    </select>
</mapper>

