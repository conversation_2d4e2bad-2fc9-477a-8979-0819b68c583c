<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="com.echronos.expo.dao.ExpoTravelOrderDao">

    <resultMap id="ExpoTravelOrderMap" type="com.echronos.expo.model.ExpoTravelOrder">
        <result property="id" column="id"/>
        <result property="expoId" column="expo_id"/>
        <result property="exhibitorId" column="exhibitor_id"/>
        <result property="customerId" column="customer_id"/>
        <result property="customerCompanyId" column="customer_company_id"/>
        <result property="customerContactsId" column="customer_contacts_id"/>
        <result property="orderTime" column="order_time"/>
        <result property="payWayNo" column="pay_way_no"/>
        <result property="businessMemberId" column="business_member_id"/>
        <result property="projectId" column="project_id"/>
        <result property="remark" column="remark"/>
        <result property="orderNo" column="order_no"/>
        <result property="totalAmount" column="total_amount"/>
        <result property="discountAmount" column="discount_amount"/>
        <result property="createUser" column="create_user"/>
        <result property="createTime" column="create_time"/>
        <result property="updateUser" column="update_user"/>
        <result property="updateTime" column="update_time"/>
        <result property="isDeleted" column="is_deleted"/>
    </resultMap>
    
    <sql id="columns">
        <![CDATA[
        id, expo_id, exhibitor_id, customer_id, customer_company_id, customer_contacts_id, order_time, pay_way_no, business_member_id, project_id, remark, order_no, total_amount, discount_amount, create_user, create_time, update_user, update_time, is_deleted
        ]]>
    </sql>

    <sql id="where">
        <where>
            <if test="null!=id">
                AND id = #{id}
            </if>
            <if test="null!=expoId">
                AND expo_id = #{expoId}
            </if>
            <if test="null!=exhibitorId">
                AND exhibitor_id = #{exhibitorId}
            </if>
            <if test="null!=customerId">
                AND customer_id = #{customerId}
            </if>
            <if test="null!=customerCompanyId">
                AND customer_company_id = #{customerCompanyId}
            </if>
            <if test="null!=customerContactsId">
                AND customer_contacts_id = #{customerContactsId}
            </if>
            <if test="null!=orderTime">
                AND order_time = #{orderTime}
            </if>
            <if test="null!=payWayNo and ''!=payWayNo">
                AND pay_way_no = #{payWayNo}
            </if>
            <if test="null!=businessMemberId">
                AND business_member_id = #{businessMemberId}
            </if>
            <if test="null!=projectId">
                AND project_id = #{projectId}
            </if>
            <if test="null!=remark and ''!=remark">
                AND remark = #{remark}
            </if>
            <if test="null!=orderNo">
                AND order_no = #{orderNo}
            </if>
            <if test="null!=totalAmount">
                AND total_amount = #{totalAmount}
            </if>
            <if test="null!=discountAmount">
                AND discount_amount = #{discountAmount}
            </if>
            <if test="null!=createUser">
                AND create_user = #{createUser}
            </if>
            <if test="null!=createTime">
                AND create_time = #{createTime}
            </if>
            <if test="null!=updateUser">
                AND update_user = #{updateUser}
            </if>
            <if test="null!=updateTime">
                AND update_time = #{updateTime}
            </if>
            <if test="null!=isDeleted">
                AND is_deleted = #{isDeleted}
            </if>
        </where>
    </sql>
</mapper>

