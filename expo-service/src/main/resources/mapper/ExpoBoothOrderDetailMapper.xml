<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="com.echronos.expo.dao.ExpoBoothOrderDetailDao">

    <resultMap id="ExpoBoothOrderDetailMap" type="com.echronos.expo.model.ExpoBoothOrderDetail">
        <result property="id" column="id"/>
        <result property="boothOrderId" column="booth_order_id"/>
        <result property="boothId" column="booth_id"/>
        <result property="quantity" column="quantity"/>
        <result property="price" column="price"/>
        <result property="noTaxPrice" column="no_tax_price"/>
        <result property="taxRate" column="tax_rate"/>
        <result property="shopSkuId" column="shop_sku_id"/>
        <result property="skuId" column="sku_id"/>
        <result property="name" column="name"/>
        <result property="skuCode" column="sku_code"/>
        <result property="standardJson" column="standard_json"/>
        <result property="marketPrice" column="market_price"/>
        <result property="unit" column="unit"/>
        <result property="createUser" column="create_user"/>
        <result property="createTime" column="create_time"/>
        <result property="updateUser" column="update_user"/>
        <result property="updateTime" column="update_time"/>
        <result property="isDeleted" column="is_deleted"/>
    </resultMap>

    <update id="removeByExhibitorId">
        update
            ech_expo_booth_order_detail t1,
            ech_expo_booth_order t2
        set
            t1.is_deleted = 1,
            t1.update_user = #{userId},
            t1.update_time = #{updateTime}
        where
            t1.booth_order_id = t2.id
            and t1.is_deleted = 0
            and t2.is_deleted = 0
            and t2.exhibitor_id = #{exhibitorId}
    </update>

</mapper>

