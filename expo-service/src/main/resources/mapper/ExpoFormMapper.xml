<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="com.echronos.expo.dao.ExpoFormDao">

    <resultMap id="ExpoFormMap" type="com.echronos.expo.model.ExpoForm">
        <result property="id" column="id"/>
        <result property="companyId" column="company_id"/>
        <result property="expoId" column="expo_id"/>
        <result property="formType" column="form_type"/>
        <result property="formName" column="form_name"/>
        <result property="description" column="description"/>
        <result property="publishTenantId" column="publish_tenant_id"/>
        <result property="formCode" column="form_code"/>
        <result property="versionNumber" column="version_number"/>
        <result property="fieldJson" column="field_json"/>
        <result property="createUser" column="create_user"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="updateUser" column="update_user"/>
        <result property="isDeleted" column="is_deleted"/>
        <result property="tenantId" column="tenant_id"/>
    </resultMap>
    
    <sql id="columns">
        <![CDATA[
        id, company_id, expo_id, form_type, form_name, description, publish_tenant_id, form_code, version_number, field_json, create_user, create_time, update_time, update_user, is_deleted, tenant_id
        ]]>
    </sql>

    <sql id="where">
        <where>
            <if test="null!=id">
                AND id = #{id}
            </if>
            <if test="null!=companyId">
                AND company_id = #{companyId}
            </if>
            <if test="null!=expoId">
                AND expo_id = #{expoId}
            </if>
            <if test="null!=formType">
                AND form_type = #{formType}
            </if>
            <if test="null!=formName and ''!=formName">
                AND form_name = #{formName}
            </if>
            <if test="null!=description and ''!=description">
                AND description = #{description}
            </if>
            <if test="null!=publishTenantId and ''!=publishTenantId">
                AND publish_tenant_id = #{publishTenantId}
            </if>
            <if test="null!=formCode and ''!=formCode">
                AND form_code = #{formCode}
            </if>
            <if test="null!=versionNumber">
                AND version_number = #{versionNumber}
            </if>
            <if test="null!=fieldJson and ''!=fieldJson">
                AND field_json = #{fieldJson}
            </if>
            <if test="null!=createUser">
                AND create_user = #{createUser}
            </if>
            <if test="null!=createTime">
                AND create_time = #{createTime}
            </if>
            <if test="null!=updateTime">
                AND update_time = #{updateTime}
            </if>
            <if test="null!=updateUser">
                AND update_user = #{updateUser}
            </if>
            <if test="null!=isDeleted">
                AND is_deleted = #{isDeleted}
            </if>
            <if test="null!=tenantId and ''!=tenantId">
                AND tenant_id = #{tenantId}
            </if>
        </where>
    </sql>
</mapper>

