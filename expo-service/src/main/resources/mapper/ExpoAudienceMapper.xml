<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="com.echronos.expo.dao.ExpoAudienceDao">

    <resultMap id="ExpoAudienceMap" type="com.echronos.expo.model.ExpoAudience">
        <result property="id" column="id"/>
        <result property="companyId" column="company_id"/>
        <result property="expoId" column="expo_id"/>
        <result property="name" column="name"/>
        <result property="phone" column="phone"/>
        <result property="email" column="email"/>
        <result property="companyName" column="company_name"/>
        <result property="customerId" column="customer_id"/>
        <result property="formCode" column="form_code"/>
        <result property="versionNumber" column="version_number"/>
        <result property="ip" column="ip"/>
        <result property="createUser" column="create_user"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="updateUser" column="update_user"/>
        <result property="isDeleted" column="is_deleted"/>
        <result property="tenantId" column="tenant_id"/>
        <result property="isAppoint" column="is_appoint"/>
    </resultMap>

    <sql id="columns">
        <![CDATA[
        id
        , company_id, expo_id, name, phone, email, company_name, customer_id, form_code, version_number, ip, create_user, create_time, update_time, update_user, is_deleted, tenant_id, is_appoint
        ]]>
    </sql>

    <sql id="where">
        <where>
            <if test="null!=id">
                AND id = #{id}
            </if>
            <if test="null!=companyId">
                AND company_id = #{companyId}
            </if>
            <if test="null!=expoId">
                AND expo_id = #{expoId}
            </if>
            <if test="null!=name and ''!=name">
                AND name = #{name}
            </if>
            <if test="null!=phone and ''!=phone">
                AND phone = #{phone}
            </if>
            <if test="null!=email and ''!=email">
                AND email = #{email}
            </if>
            <if test="null!=companyName and ''!=companyName">
                AND company_name = #{companyName}
            </if>
            <if test="null!=customerId">
                AND customer_id = #{customerId}
            </if>
            <if test="null!=formCode and ''!=formCode">
                AND form_code = #{formCode}
            </if>
            <if test="null!=versionNumber">
                AND version_number = #{versionNumber}
            </if>
            <if test="null!=ip">
                AND ip = #{ip}
            </if>
            <if test="null!=createUser">
                AND create_user = #{createUser}
            </if>
            <if test="null!=createTime">
                AND create_time = #{createTime}
            </if>
            <if test="null!=updateTime">
                AND update_time = #{updateTime}
            </if>
            <if test="null!=updateUser">
                AND update_user = #{updateUser}
            </if>
            <if test="null!=isDeleted">
                AND is_deleted = #{isDeleted}
            </if>
            <if test="null!=tenantId and ''!=tenantId">
                AND tenant_id = #{tenantId}
            </if>
            <if test="null!=isAppoint">
                AND is_appoint = #{isAppoint}
            </if>
        </where>
    </sql>

    <select id="pageForAudience" resultType="com.echronos.expo.dto.ExpoAudienceDTO">
            select eoar.* from (
                select
                    eeoa.*,
                    eeoe.*,
                    eeoc.channel_name,
                    if(eoae.id is not null,1,0) isSend,
                    eoae.sendCount,
                    eoae.lastSendTime,
                    if(eoas.id is not null, 1, 0) isSign,
                    eoas.signCount,
                    eoas.lastSignTime
                from ech_expo.ech_expo_audience eeoa
                left join ech_expo.ech_expo_channel eeoc on eeoa.channel_id = eeoc.id
                left join(
                    select id,
                           audience_id,
                           count(1) as sendCount,
                           max(create_time) as lastSendTime
                    from ech_expo.ech_expo_audience_email_records
                    where  company_id = #{dto.companyId} and expo_id = #{dto.expoId}  and is_send = 1 and is_deleted = 0
                    group by audience_id
                    order by create_time desc
                ) eoae on eeoa.id = eoae.audience_id
                left join (
                    select id,
                            audience_id,
                            count(1) as signCount,
                            max(create_time) as lastSignTime
                    from ech_expo.ech_expo_audience_sign
                    where company_id = #{dto.companyId} and expo_id = #{dto.expoId} and is_deleted = 0
                    group by audience_id
                    order by create_time desc
                )  eoas on eeoa.id = eoas.audience_id
                left join (
                    select
                    audience_id
                    <if test="dto.fieldList != null and dto.fieldList.size() > 0">
                        <foreach collection="dto.fieldList" item="field">
                            , GROUP_CONCAT(case col_name when #{field} then col_value end)  #{field}
                        </foreach>
                    </if>
                    from
                    ech_expo.ech_expo_audience_extend
                    where
                        company_id = #{dto.companyId}
                      and expo_id = #{dto.expoId}
                      and is_deleted = 0
                    group by audience_id
                ) eeoe on eeoa.id = eeoe.audience_id
                where
                eeoa.is_deleted = 0
                and eeoa.company_id = #{dto.companyId}
                and eeoa.expo_id = #{dto.expoId}
                group by eeoa.id
            ) eoar where eoar.is_deleted = 0
            <if test="dto.keywords != null and dto.keywords != ''">
                and(
                 eoar.name like concat('%',#{dto.keywords},'%')
                or eoar.phone like concat('%',#{dto.keywords},'%')
                or eoar.email like concat('%',#{dto.keywords},'%')
                or eoar.company_name like concat('%',#{dto.keywords},'%')
                    )
            </if>
            <if test="dto.idList!=null and dto.idList.size()>0">
                 and eoar.id in
                <foreach collection="dto.idList" item="id" separator="," open="(" close=")">
                    #{id}
                </foreach>
            </if>
            <if test="dto.buyerList != null and dto.buyerList.size() > 0">
                and (
                <foreach collection="dto.buyerList" item="asBuyer" separator="or">
                    eoar.asBuyer like  concat('%',#{asBuyer},'%')
                </foreach>
                )
            </if>
            <if test="null != dto.whereSqlStr and '' != dto.whereSqlStr">
             ${dto.whereSqlStr}
            </if>
            <choose>
                <when test="null != dto.sortSqlStr and '' != dto.sortSqlStr">
                    order by ${dto.sortSqlStr}
                </when>
                <otherwise>
                    order by eoar.create_time desc
                </otherwise>
            </choose>
    </select>

    <select id="queryAudienceIdList" resultType="java.lang.Integer">
        select t1.id from ech_expo_audience t1
            inner join ech_expo_audience_sign t2 on t1.id = t2.audience_id
        where t1.is_deleted = false
          and t2.is_deleted = false
          and t1.company_id = #{companyId}
          and t1.create_time BETWEEN #{startTime} and #{endTime}
          and t2.create_time BETWEEN #{startTime} and #{endTime}
    </select>


    <select id="getIndexAudienceCount" resultType="com.echronos.expo.model.ext.ExpoIndexCountExt">
        SELECT
            COUNT(DISTINCT ea.customer_id) AS total,
            COUNT(DISTINCT CASE
                    WHEN DATE_FORMAT(ea.create_time, '%Y-%m') = DATE_FORMAT(CURDATE() - INTERVAL 1 MONTH, '%Y-%m')
                    THEN ea.customer_id
                END) AS calculateTotalOne,
            COUNT(DISTINCT CASE
                    WHEN DATE_FORMAT(ea.create_time, '%Y-%m') = DATE_FORMAT(CURDATE(), '%Y-%m')
                    THEN ea.customer_id
                END) AS calculateTotalTwo
        FROM
            ech_expo_audience ea
        JOIN
            ech_expo_info ei ON ea.expo_id = ei.id
        WHERE
            ei.is_deleted = 0
            AND ea.is_deleted = 0
            AND ei.company_id = #{companyId}
            AND ea.customer_id IS NOT NULL
    </select>

    <select id="getAudienceSignInCount" resultType="java.lang.Integer">
        SELECT
            COUNT(DISTINCT eas.audience_id) AS signInTotal
        FROM
            ech_expo_audience_sign eas
        JOIN
            ech_expo_audience ea ON eas.audience_id = ea.id
        WHERE
            eas.expo_id = #{expoId}
            AND eas.is_deleted = 0
            AND ea.is_deleted = 0
    </select>

    <select id="getAudienceCumulativeSignInCount" resultType="java.lang.Integer">
        SELECT
            COUNT(eas.audience_id) AS signInTotal
        FROM
            ech_expo_audience_sign eas
        JOIN
            ech_expo_audience ea ON eas.audience_id = ea.id
        WHERE
            eas.expo_id = #{expoId}
            AND eas.is_deleted = 0
            AND ea.is_deleted = 0
    </select>

    <select id="getChannelAudienceCountList" resultType="com.echronos.expo.model.ext.ExpoChannelAudienceExt">
        SELECT
            c.channel_name as channelName,
            COUNT(DISTINCT a.customer_id) AS registerCount,
            COUNT(DISTINCT CASE
                WHEN s.id IS NOT NULL THEN a.customer_id
            END) AS signInCount
        FROM
            ech_expo_audience a
        JOIN
            ech_expo_channel c ON a.channel_id = c.id
        LEFT JOIN
            ech_expo_audience_sign s ON a.id = s.audience_id
        WHERE
            a.expo_id = #{expoId}
            AND a.is_deleted = 0
            AND c.is_deleted = 0
            AND s.is_deleted = 0
        GROUP BY
            c.id, c.channel_name
    </select>

</mapper>

