<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="com.echronos.expo.dao.ExpoAppointmentTimeDao">

    <resultMap id="ExpoAppointmentTimeMap" type="com.echronos.expo.model.ExpoAppointmentTime">
        <result property="id" column="id"/>
        <result property="appointmentId" column="appointment_id"/>
        <result property="appointedTimeId" column="appointed_time_id"/>
        <result property="startTime" column="start_time"/>
        <result property="endTime" column="end_time"/>
        <result property="createUser" column="create_user"/>
        <result property="updateUser" column="update_user"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="isDeleted" column="is_deleted"/>
    </resultMap>
    
    <sql id="columns">
        <![CDATA[
        id, appointment_id, appointed_time_id, start_time, end_time, create_user, update_user, create_time, update_time, is_deleted
        ]]>
    </sql>

    <sql id="where">
        <where>
            <if test="null!=id">
                AND id = #{id}
            </if>
            <if test="null!=appointmentId">
                AND appointment_id = #{appointmentId}
            </if>
            <if test="null!=appointedTimeId">
                AND appointed_time_id = #{appointedTimeId}
            </if>
            <if test="null!=startTime">
                AND start_time = #{startTime}
            </if>
            <if test="null!=endTime">
                AND end_time = #{endTime}
            </if>
            <if test="null!=createUser">
                AND create_user = #{createUser}
            </if>
            <if test="null!=updateUser">
                AND update_user = #{updateUser}
            </if>
            <if test="null!=createTime">
                AND create_time = #{createTime}
            </if>
            <if test="null!=updateTime">
                AND update_time = #{updateTime}
            </if>
            <if test="null!=isDeleted">
                AND is_deleted = #{isDeleted}
            </if>
        </where>
    </sql>
</mapper>

