<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="com.echronos.expo.dao.ExpoExhibitorBoothDao">

    <resultMap id="ExpoExhibitorBoothMap" type="com.echronos.expo.model.ExpoExhibitorBooth">
        <result property="id" column="id"/>
        <result property="exhibitorId" column="exhibitor_id"/>
        <result property="boothId" column="booth_id"/>
        <result property="createUser" column="create_user"/>
        <result property="createTime" column="create_time"/>
        <result property="updateUser" column="update_user"/>
        <result property="updateTime" column="update_time"/>
        <result property="isDeleted" column="is_deleted"/>
    </resultMap>

    <select id="getDetailByExhibitorIdList" resultType="com.echronos.expo.dto.ExpoExhibitorBoothDTO">
        select
            t1.*,
            t2.id as boothId,
            t2.booth_name as boothName,
            t2.booth_floor as boothFloor,
            t2.booth_zone as boothZone,
            t2.booth_number as boothNumber,
            t2.booth_type as boothType,
            t2.dimensions as dimensions
        from
            ech_expo_exhibitor_booth t1,
            ech_expo_booth t2
        where
            t1.is_deleted = 0
            and t2.is_deleted = 0
            and t1.booth_id = t2.id
            and t1.exhibitor_id in
            <foreach collection="exhibitorIdList" item="id" separator="," open="(" close=")">
                #{id}
            </foreach>
    </select>

</mapper>

