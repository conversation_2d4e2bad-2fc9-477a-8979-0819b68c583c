<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="com.echronos.expo.dao.ExpoFormExtendDao">

    <resultMap id="ExpoFormExtendMap" type="com.echronos.expo.model.ExpoFormExtend">
        <result property="id" column="id"/>
        <result property="formId" column="form_id"/>
        <result property="businessId" column="business_id"/>
        <result property="colName" column="col_name"/>
        <result property="colValue" column="col_value"/>
        <result property="colSort" column="col_sort"/>
        <result property="createUser" column="create_user"/>
        <result property="createTime" column="create_time"/>
        <result property="updateUser" column="update_user"/>
        <result property="updateTime" column="update_time"/>
        <result property="isDeleted" column="is_deleted"/>
    </resultMap>
    
    <sql id="columns">
        <![CDATA[
        id, form_id, business_id, col_name, col_value, col_sort, create_user, create_time, update_user, update_time, is_deleted
        ]]>
    </sql>

    <sql id="where">
        <where>
            <if test="null!=id">
                AND id = #{id}
            </if>
            <if test="null!=formId">
                AND form_id = #{formId}
            </if>
            <if test="null!=businessId">
                AND business_id = #{businessId}
            </if>
            <if test="null!=colName and ''!=colName">
                AND col_name = #{colName}
            </if>
            <if test="null!=colValue and ''!=colValue">
                AND col_value = #{colValue}
            </if>
            <if test="null!=colSort and ''!=colSort">
                AND col_sort = #{colSort}
            </if>
            <if test="null!=createUser">
                AND create_user = #{createUser}
            </if>
            <if test="null!=createTime">
                AND create_time = #{createTime}
            </if>
            <if test="null!=updateUser">
                AND update_user = #{updateUser}
            </if>
            <if test="null!=updateTime">
                AND update_time = #{updateTime}
            </if>
            <if test="null!=isDeleted">
                AND is_deleted = #{isDeleted}
            </if>
        </where>
    </sql>
</mapper>

