Configuration: 
 status: warn
 Properties: # 定义全局变量
   Property: # 缺省配置（用于开发环境）。其他环境需要在VM参数中指定，如下：
     - name: log_root_level
       value: INFO
     - name: project.name
       value: ECH_EXPO
     - name: log_mybatis_level
       value: DEBUG
 Appenders:
   Console:  # 输出到控制台
     name: CONSOLE
     target: SYSTEM_OUT
     PatternLayout: 
       charset: UTF-8
       pattern: "%date{yyyy-MM-dd HH:mm:ss.SSS}{GMT+8} [${env:project.name}] %-5level [%thread] - [%X{X-B3-TraceId}]%msg %n"
 Loggers:
   Root:
     level: ${env:log_root_level}
     AppenderRef:
       - ref: CONSOLE
   Logger: # 为com.xjj包配置特殊的Log级别，方便调试
      - name: com.echronos.expo.dao
        level: ${env:log_mybatis_level}
        additivity: false
        AppenderRef:  
          - ref: CONSOLE